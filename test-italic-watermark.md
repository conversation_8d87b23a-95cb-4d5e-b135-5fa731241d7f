# 倾斜水印持久化功能测试

## 功能说明

现在倾斜水印已经支持数据持久化，可以保存到文档数据中，重新打开时会自动恢复。

## 使用方法

### 1. 插入倾斜水印
```javascript
// 插入倾斜水印
editor.insertItalicMark("机密文档", {
  direction: "right", // "left" | "right" | "horizontal"
  module: ["pageWrite", "printView", "printPaper"], // 显示模块
  font: {
    fontSize: 18,
    fontFamily: "华文彩云",
    opacity: 0.5,
    color: "red",
  }
});
```

### 2. 清除倾斜水印
```javascript
// 清除倾斜水印
editor.clearItalicMark();
```

### 3. 获取文档数据（包含倾斜水印）
```javascript
// 获取包含倾斜水印的文档数据
const rawData = editor.getRawData();
console.log(rawData.waterMarks); // 可以看到倾斜水印数据
```

### 4. 加载包含倾斜水印的文档
```javascript
// 加载文档时，倾斜水印会自动恢复
editor.loadRawData(rawData);
```

## 实现原理

1. **数据保存**：倾斜水印数据保存在 `editor.waterMarks` 数组中，类型为 `"italicMark"`
2. **数据加载**：加载时从 `waterMarks` 数组中恢复倾斜水印到 `editor.internal.italicWatermark`
3. **显示渲染**：支持在页面编写、打印预览、打印输出等不同模块中显示
4. **打印支持**：打印时会正确输出倾斜水印

## 数据结构

倾斜水印在 `waterMarks` 数组中的数据结构：
```javascript
{
  type: "italicMark",
  mode: "repeat",
  width: 0,
  height: 0,
  start: { x: 0, y: 0 },
  is_edit: false,
  params: {
    text: "机密文档",
    direction: "right",
    module: ["pageWrite", "printView", "printPaper"],
    font: {
      fontSize: 18,
      fontFamily: "华文彩云",
      opacity: 0.5,
      color: "red",
    },
    id: "italic_watermark"
  }
}
```

## 兼容性

- 向后兼容：旧的 `editor.internal.italicWatermark` 方式仍然有效
- 新功能：新增的持久化功能不会影响现有代码
- 自动迁移：加载时会自动将保存的数据恢复到内存中

## 测试场景

1. ✅ 插入倾斜水印后保存文档
2. ✅ 重新打开文档，倾斜水印正确显示
3. ✅ 打印时倾斜水印正确输出
4. ✅ 清除倾斜水印功能正常
5. ✅ 多种显示模块控制正常

const config = {
  showDesignFloatingMenu: true,
  systemConfig: {
    source: "design",
    fieldShowMode: 3,
    field_symbol_color: "#0B57D0",
    page_padding_top: 0,
    page_padding_bottom: 0,
    page_padding_left: 40,
    page_padding_right: 40,
    table_padding_horizontal: 0,
    // insert_row_carry_data: 2,
    group_line_color: "#1677FF",
    group_normal_color: "#A1A1A1",
    show_corner_line: false,
    // comment: {
    //   wordSelectedBgColor: "yellow",
    //   wordUnselectedBgColor: "rgba(0, 0, 0, 0)",
    //   listBgColor: "rgba(0, 0, 0, 0)",
    //   listWidth: 270,
    //   listItemBgColor: "#ffffff",
    //   hideSwitch: true,
    //   hideCloseBtn: false,
    //   title: "数据来源",
    //   hideDeleteBtn: true,
    //   hideReplaceBtn: true,
    //   hideDate: true,
    //   defaultAllOpen: true,
    // },
  },
  rightClickMenuConfig: [
    // 默认右键菜单配置
    // value: 自定义显示文字 icon: 自定义显示图标 line: 自定义是否加横线 children: 二级菜单数组 handler：添加自定义事件 key必须是加custom的
    { key: "ctrlZ" }, // 撤销
    { key: "save", value: "保存" }, // 保存
    { key: "redo" }, // 重做
    { key: "openFile" },
    { key: "systemPrint", value: "打印预览", line: true }, // value: "系统打印",
    { key: "add_comment" }, // value: "系统打印",
    {
      value: "插入",
      key: "custom-insert",
      icon: "icon-docuAlign",
      children: [
        { key: "insertFraction" }, // value: "插入分数",
        { key: "insertField" }, // value: "插入文本域",
        { key: "choice" }, // 自定义多选框
        { key: "insertLocalImage" }, /// value: "插入图片",
        { key: "insertLocalMarkableImage" }, /// value: "插入图片",
        { key: "insertTable" }, // value: "插入表格",
        { key: "insertGroup" }, // value: "插入分组",
        { key: "insertList" }, // value: "插入列表",
        { key: "insertHorizontalLine" }, //value: "插入水平线"
        { key: "italicWaterMark" }, // value: 倾斜水印
      ],
      line: true,
    },
    { key: "copy" }, // 复制
    { key: "cut" }, // 剪切
    { key: "paste" }, // 粘贴
    { key: "deleteSelection" }, // 删除
    { key: "pasteText", line: true }, // 粘贴为纯文本
    { key: "mergeCell" }, // 合并单元格
    { key: "splitCell" }, // 拆分单元格
    { key: "modifyCellAttr" }, // 单元格属性
    { key: "threeCellDivision" }, // 三分单元格
    { key: "cancelThreeCellDivision" }, // 取消三分单元格
    { key: "setCellsGroup" }, // 设置单元格成组
    { key: "cancelCellsGroup" }, // 取消单元格成组
    { key: "modifyTableAttr" }, // 表格属性
    {
      value: "表格操作",
      icon: "icon-biaoge",
      children: [
        { key: "setFixedTableHeader" }, // 设置固定表头
        { key: "cancelFixedTableHeader" }, // 设置固定表头
        { key: "insertEmptyParaFromUp" }, // 上方插入空行
        { key: "insertEmptyParaFromDown" }, // 下方插入空行
        { key: "insertRowFromUp" }, // 从上方插入一行
        { key: "insertRowFromDown" }, // 从下方插入一行
        { key: "insertColFromLeft" }, // 从左侧插入一列
        { key: "insertColFromRight" }, // 从右侧插入一列
        { key: "delTblRow" }, // 删除行
        { key: "delTblCol" }, // 删除列
        { key: "deleteEmptyParaUp" }, // 删除表格上方的空行
        { key: "deleteEmptyParaDown" }, // 删除表格下方的空行
        { key: "changeTableBgColor" }, // 选区修改表格背景色
        { key: "averageRowHeight" },
        { key: "averageColWidth" },
        { key: "deleteTbl" }, // 删除表格
      ],
    },
    { key: "tableLine", line: true }, // value: "表格线"
    { key: "fieldProp" }, // 文本域属性
    { key: "choiceProp" }, // 复选框属性
    { key: "caliperProp" }, // 卡尺属性
    { key: "modifyProperty" }, // 分组属性
    { key: "restartListIndex" }, // 重新编号
    { key: "delGroup" }, // value: "删除分组",
    { key: "paragraph" }, // 段落
    { key: "font", line: true }, // 字体
    { key: "water_mark", value: "开启水印模式" }, // 开启水印模式
    { key: "shape_mode", value: "开启图形模式" }, // 开启关闭图形编辑
    { key: "setPageConfig", value: "页面设置" }, //页面设置
    { key: "scanAndAnalyze" }, //页面设置
    { key: "insertPageNum" }, // 插入页码
  ],
};
export default config;

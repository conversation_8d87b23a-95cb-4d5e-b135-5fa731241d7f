import { diff_match_patch } from "diff_match_patch";

import { getUUID } from "../assets/js/utils";
const traceContrast = {
  data() {
    return {
      traceImageMap: {},
      traceCaliperMap: {},
      traceId: "",
      currentUser: null,
      traceInfo: [],
      dmp: new diff_match_patch(),
      fontStyleList: [],
      bgColorList: [
        "rgba(250,227,113,0.3)",
        "rgba(174,221,129,0.3)",
        "rgba(217,104,49,0.3)",
        "rgba(69,137,148,0.3)",
        "rgba(222,156,83,0.3)",
        "rgba(230,179,61,0.3)",
        "rgba(178,190,126,0.3)",
        "rgba(251,178,159,0.3)",
        "rgba(252,157,154,0.3)",
        "rgba(179,214,110,0.3)",
      ],
    };
  },
  mounted() {
    //不同颜色代表的样式的数组，数组内对象包含font_id和fontStyle
    for (let i = 0; i < this.bgColorList.length * 2 + 1; i++) {
      const fontId = getUUID("font-default");
      let deleteLine = false;
      if (i < this.bgColorList.length || i === this.bgColorList.length * 2) {
        deleteLine = false;
      } else {
        deleteLine = true;
      }
      this.fontStyleList.push({
        fontId: fontId,
        fontStyle: {
          family: "宋体",
          height: 16,
          bold: false,
          italic: false,
          underline: false,
          strikethrough: deleteLine,
          script: 3,
          color: "#000",
          bgColor: null,
          temp_word_bgColor: null,
        },
      });
      if (i < this.bgColorList.length) {
        this.fontStyleList[i].fontStyle.temp_word_bgColor = this.bgColorList[i];
      } else if (
        i >= this.bgColorList.length &&
        i < this.bgColorList.length * 2
      ) {
        this.fontStyleList[i].fontStyle.temp_word_bgColor =
          this.bgColorList[i - this.bgColorList.length];
      }
    }
  },
  methods: {
    // openTraceContrast() {
    //   const traceRawData = this.compareTraces(rawData3, rawData4, true);
    //   this.instance.editor.reInitRaw(traceRawData);
    //   this.instance.editor.update();
    //   this.instance.editor.render();
    // },
    //比较段落文本
    generateReplaceArray() {
      const replaceArr = [];
      for (let charCode = 0x3040; charCode <= 0x30ff; charCode++) {
        const char = String.fromCharCode(charCode);
        replaceArr.push(char);
      }

      return replaceArr;
    },
    diffParaText(str1, str2, all) {
      const dmp = this.dmp;
      const pattern = /\$\$\$(.*?)\$\$\$/g;
      let matches1 = str1.match(pattern) || [];
      let matches2 = str2.match(pattern) || [];
      let correspondence = {};

      const replaceArr = this.generateReplaceArray();
      correspondence = this.prepareCorrespondence(
        matches1,
        matches2,
        replaceArr
      );
      str1 = this.replaceString(str1, matches1, correspondence);
      str2 = this.replaceString(str2, matches2, correspondence);

      const diff = dmp.diff_main(str1, str2);
      diff.forEach((e) => {
        for (let key in correspondence) {
          const regex = new RegExp(correspondence[key], "g");
          e[1] = e[1].replace(regex, () => `${key}`);
        }
      });
      // dmp.diff_cleanupSemantic(diff); // 他会修改 diff结果 导致对比痕迹出错 所以注释掉
      const traces = [];
      let offset = 0;
      diff.forEach((ele) => {
        if (all || ele[0] !== 0) {
          traces.push({ 0: ele[0], 1: offset, 2: ele[1] });
        }
        offset += ele[0] === -1 ? 0 : this.handleImageOffset(ele[1]).length;
      });
      return traces;
    },
    prepareCorrespondence(matches1, matches2, replaceArr) {
      let correspondence = {};
      let usedIndex = 0;

      // 先标记所有matches，以确保相同的元素使用相同的替换字符
      [...new Set([...matches1, ...matches2])].forEach((match) => {
        if (!correspondence[match]) {
          correspondence[match] = replaceArr[usedIndex++];
        }
      });

      return correspondence;
    },
    replaceString(str, matches, correspondence) {
      matches.forEach((match) => {
        // 使用正则表达式确保能匹配整个字符串
        // 添加对特殊字符的转义处理
        let escapedMatch = match.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&");
        let regex = new RegExp(escapedMatch, "g");
        str = str.replace(regex, correspondence[match]);
      });
      return str;
    },
    //痕迹对比主入口
    mainDiff(paraId, str1, str2, traceInfo) {
      const diffVersion = this.diffParaText(str1, str2, true);
      if (!traceInfo || !traceInfo.length) {
        return diffVersion;
      }
      const finalResult = [];
      this.traceInfo = traceInfo;
      for (let i = 0; i < diffVersion.length; i++) {
        const ele = diffVersion[i];
        if (ele[0] === 0) {
          finalResult.push(ele);
        } else {
          const res = this.traceSource(
            ele[0],
            ele[2],
            ele[1],
            paraId,
            traceInfo
          );
          finalResult.push(...res);
        }
      }
      //设置正确的偏移
      let realOffset = 0;
      finalResult.forEach((ele) => {
        ele[1] = realOffset;
        realOffset += this.handleImageOffset(ele[2]).length;
      });
      return finalResult;
    },

    traceSource(addOrDel, changeContent, location, paragraphId, traceInfo) {
      let content = changeContent;
      let sourceInfo = []; // 溯源信息,可能多条
      let sliceContent = ""; // 目标字符串截取剩余的部分
      let finalIndex = 0; // 位置信息
      const paragraphTrace = []; // 段落修改信息
      // 获取到对象名为id的比对信息
      for (let i = 0; i < traceInfo.length; i++) {
        const element = traceInfo[i];
        if (!element.traces[paragraphId]) continue;
        if (element.traces[paragraphId].length) {
          paragraphTrace.push({
            record: element.traces[paragraphId],
            meta: element.meta,
          });
        }
      }

      // 循环内容，并且逐次递减
      a: while (content) {
        // 当前段的所有修改信息中提取出来 增删一致 内容包含的所有数据
        const records = [];
        for (const item of paragraphTrace) {
          const tempRecord = item.record.filter(
            (re) => addOrDel === re[0] && re[2].indexOf(content) > -1
          );
          if (tempRecord.length) {
            records.push({
              record: tempRecord,
              meta: item.meta,
            });
          }
        }

        // 循环所有符合第一步条件的数据
        for (let i = 0; i < records.length; i++) {
          const element = records[i];
          // 如果里面只有一条数据，表示只有一个人修改过该处内容，那么就是找到了所需要的数据，直接进入下一步，封装并返回结果
          // 如果有多条数据，那么就需要判断改条数据是不是想要的那一条数据
          const offset_result =
            records.length === 1
              ? true
              : this.handleOffset(location, content, element, paragraphTrace);
          if (offset_result) {
            // 位置坐标，如果没有截取，说明全部都已经匹配成功了
            finalIndex = sourceInfo.length
              ? location + this.handleImageOffset(content).length
              : location;
            // 比对结果
            const contrastResult = {
              0: addOrDel,
              1: finalIndex,
              2: content,
              meta: element.meta,
            };
            sourceInfo.push(contrastResult);
            // 没有裁剪过内容，代表匹配完全了
            if (sliceContent === "") {
              return sourceInfo;
            }
            break a;
          }
        }
        // 目标字符串截取剩余的部分收集起来
        sliceContent = content.slice(content.length - 1) + sliceContent;
        content = content.slice(0, content.length - 1);
      }

      // 截取收敛后的操作
      if (sliceContent && sourceInfo.length) {
        // 位置修改
        const sliceLocation =
          location + changeContent.length - sliceContent.length;
        // 比对结果
        const surplusResult = this.traceSource(
          addOrDel,
          sliceContent,
          sliceLocation,
          paragraphId,
          traceInfo
        );
        sourceInfo.push(...surplusResult);
      }
      return sourceInfo;
    },

    handleOffset(location, content, record, paragraphTrace) {
      for (let index = 0; index < record.record.length; index++) {
        const element = record.record[index];
        let isJump = true; // 找到对比开始的位置
        let offset = element[2].indexOf(content) + element[1];
        // 从匹配到的位置开始
        for (let i = 0; i < paragraphTrace.length; i++) {
          const trace = paragraphTrace[i].record; // 这里还是一个数组
          // 需要确定当前记录在该段所有记录中的index
          for (let j = 0; j < trace.length; j++) {
            // 同一段的某个修改记录
            const para_record = trace[j];
            if (
              para_record[0] === element[0] &&
              para_record[1] === element[1] &&
              para_record[2] === element[2]
            ) {
              isJump = false;
            }
            // 新增的内容，偏移之后的会对其位置有影响
            // 删除的内容，偏移之前的会对其位置有影响
            if (
              (isJump && element[0] === 1) ||
              (!isJump && element[0] === -1)
            ) {
              continue;
            }
            // 修改记录在偏移之前的才会都目标内容产生影响
            if (para_record[1] < offset) {
              if (para_record[0] === -1) {
                // 匹配到的修改记录的所有内容，在某个节点完全删除了，那么这个记录不是所要找的记录
                if (
                  offset === para_record[1] &&
                  para_record[2].length >= element[2].length &&
                  element[0] === 1
                ) {
                  return false;
                }
                // 删除的情况和新增字符的情况相反
                if (element[0] === -1) {
                  offset += para_record[2].length;
                } else {
                  offset -= para_record[2].length;
                }
              } else if (para_record[0] === 1) {
                // 新增字符的情况
                if (element[0] === -1) {
                  offset -= para_record[2].length;
                } else {
                  offset += para_record[2].length;
                }
              }
            }
          }
        }
        if (offset === location) {
          return true;
        } else {
          return false;
        }
      }
    },
    handleImageOffset(str) {
      for (const imageId in this.traceImageMap) {
        str = str.replace("$$$" + imageId + "$$$", " ");
      }
      for (const widgetId in this.traceCaliperMap) {
        str = str.replace("$$$" + widgetId + "$$$", " ");
      }
      return str;
    },
    /**
     * 用户登录
     * @param userInfo
     */
    userLogin(userInfo) {
      this.traceId = getUUID("trace");
      this.currentUser = userInfo;
      this.editor.userLogin(userInfo);
    },
    getStrByRawPara(para, rawData) {
      let str = "";
      para.children.forEach((o) => {
        if (o.type === "image") {
          str += `$$$${o.id}$$$`;
          const srcData = rawData.imageSrcObj
            ? rawData.imageSrcObj[o.src]
            : null;
          this.traceImageMap[o.id] = {
            type: o.type,
            src: srcData ?? o.src,
            meta: o.meta,
            width: o.width,
            height: o.height,
          };
        } else if (o.type === "widget") {
          if (o.widgetType === "caliper") {
            str += `$$$${o.id + "-caliper-" + o.selectNum}$$$`;
            this.traceCaliperMap[o.id + "-caliper-" + o.selectNum] = {
              type: o.type,
              widgetType: "caliper",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
            };
          } else if (o.widgetType === "radio") {
            str += `$$$${o.id + "-radio-" + o.selected}$$$`;
            this.traceCaliperMap[o.id + "-radio-" + o.selected] = {
              type: o.type,
              widgetType: "radio",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
              selected: o.selected,
              disabled: 1,
            };
          } else if (o.widgetType === "checkbox") {
            str += `$$$${o.id + "-checkbox-" + o.selected}$$$`;
            this.traceCaliperMap[o.id + "-checkbox-" + o.selected] = {
              type: o.type,
              widgetType: "checkbox",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
              selected: o.selected,
              disabled: 1,
            };
          } else {
            str += "  ";
          }
        } else if (o.type === "text") {
          str += o.value;
        }
        if (o.children) {
          str += this.getStrByRawPara(o, rawData);
        }
      });
      return str;
    },
    /**
     * 获取简单的数据结构
     * @param data rawData.content(段落和表格的集合)或者cell.children
     */
    getSimpleParaArr(data, rawData) {
      const arr = [];
      const groups = rawData.groups || [];
      // 遍历 data 数组
      data.forEach((d) => {
        let groupId = undefined;
        for (let i = 0; i < groups.length; i++) {
          const group = groups[i];
          const hasFind = group.content_para_id.find((id) => id === d.id);
          if (hasFind) {
            groupId = group.id;
            break;
          }
        }
        if (d.type === "p") {
          // 处理段落
          arr.push({
            id: d.id,
            value: this.getStrByRawPara(d, rawData),
            structure: "para",
            groupId,
          });
        } else if (d.type === "table") {
          // 对表格单元格按位置排序，确保顺序一致
          d.cells.sort((a, b) => {
            const [ax, ay] = a.pos;
            const [bx, by] = b.pos;
            if (ax !== bx) {
              return ax - bx;
            }
            return ay - by;
          });

          // 简化单元格数据
          const cells = d.cells.map((cell) => {
            return {
              ...cell,
              children: this.getSimpleParaArr(cell.children, rawData),
              groupId,
            };
          });
          cells.forEach((cell) => {
            cell.children.forEach((p) => (p.groupId = groupId));
          });
          // 添加处理后的表格到结果数组
          arr.push({ ...d, cells, structure: "table", tableId: d.id, groupId });
        }
      });
      // const traceInfo = rawData.meta.traceInfo;
      // if (traceInfo.length) {
      //   const paras = traceInfo[traceInfo.length - 1].paras;
      //   if (paras) {
      //     paras.forEach((para) => {
      //       if (para.id) {
      //         arr.push({
      //           id: para.id,
      //           value: para.value,
      //           structure: "para",
      //         });
      //       }
      //     });
      //   }
      // }

      return arr;
    },

    getSimpleArr(rawData1, rawData2) {
      // 要对比出来那个版本是前边的哪个版本是后边的 原理是谁的traceInfo长 谁就是后边的版本
      if (rawData1?.meta?.traceInfo && rawData2?.meta?.traceInfo) {
        // 第一： 如果两个rawData都有traceInfo 那么比较长短 长的在后边 短的在前边
        if (rawData1.meta.traceInfo.length > rawData2.meta.traceInfo.length) {
          [rawData1, rawData2] = [rawData2, rawData1]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        // 都没有 或者一个有 一个没有
        // ① 都没有 那么就按照原来的位置 不需要更换
        // ② 一个有 一个没有  前边的没有也不需要换
        // ③ 就只有后边的没有 并且前边的有 才需要更换位置
        if (rawData1?.meta?.traceInfo && !rawData2?.meta?.traceInfo) {
          [rawData1, rawData2] = [rawData2, rawData1];
        }
      }
      const getRes1 = this.getSimpleParaArr(rawData1.content, rawData1);
      const getRes2 = this.getSimpleParaArr(rawData2.content, rawData2);
      const headerRes1 = this.getSimpleParaArr(rawData1.header, rawData1);
      const headerRes2 = this.getSimpleParaArr(rawData2.header, rawData2);
      return [getRes1, getRes2, headerRes1, headerRes2];
    },
    // 第二步：根据ID值是否一致, 完善两个数据结构, 获取两个数据的全集 标记状态(增加，删除，不变) 并返回
    getDiffStructure(d1, d2) {
      let i = 0;
      // base.length和compare.length不一致 要进循环 i不等于base.length-1并且也不等于base.length-1也要进循环
      while (!(d1.length === d2.length && i === d1.length)) {
        if (!d1[i] || !d2[i]) {
          // 两个数据结构长度 有可能是不一致的 已经不一致了 就说明有一个到头了 就不用继续下边的判断ID了
          if (!d1[i] && d2[i]) {
            // 如果d1[i]没有 那么就是d1得加上 不用重新生成id，因为在保存修改痕迹的时候会记录这个id，再重新生成就找不到了
            d1.splice(i, 0, {
              ...d2[i],
              /* id: uuid(d2[i].type), */ type: 1,
              i,
            });
          }
          if (d1[i] && !d2[i]) {
            d2.splice(i, 0, { id: "placeholder" });
            d1[i]["type"] = -1;
            d1[i]["i"] = i;
          }
          i++;
          continue;
        }

        d1[i]["type"] = 0;

        if (d1[i].id !== d2[i].id) {
          // 不相等 就有两种情况 ①base中有 compare中没有 那么就该画删除线 ② base中没有 compare中有 那么就该加上
          // ① compare里边有没有
          const isExist = d2.find((compareData) => compareData.id === d1[i].id);
          if (!isExist) {
            // 如果compare里边没有  那么他就应该是加删除线的
            d1[i]["type"] = -1;
            d1[i]["i"] = i;
            d2.splice(i, 0, { id: "占位" });
            i++;
            continue;
          }
          // ② compare中有 就是说base中没有 得添加上
          d1.splice(i, 0, { ...d2[i], /* id: uuid(d2[i].type), */ type: 1, i });
          i++;
          continue;
        }
        i++;
      }
      return { d1, d2 };
    },
    getDiffData(data1, data2, traceInfo, createDate, userName) {
      let { d1, d2 } = this.getDiffStructure(data1, data2);
      let diffData = d1;
      for (let i = 0; i < d1.length; i++) {
        if (diffData[i].type === 0) {
          if (diffData[i].id.startsWith("para-")) {
            // 处理段落变更
            const change = this.mainDiff(
              d1[i].id,
              d1[i]["value"],
              d2[i]["value"],
              traceInfo
            );
            diffData[i] = { id: diffData[i].id, structure: "para", change };
          } else {
            // 处理表格变更
            const cells1 = diffData[i].cells.map((cell) => ({
              ...cell,
              structure: "cell",
            }));
            const cells2 = d2[i].cells.map((cell) => ({
              ...cell,
              structure: "cell",
            }));
            const { d1: diffCells1, d2: diffCells2 } = this.getDiffStructure(
              cells1,
              cells2
            );

            const updatedCells = [];

            // 处理单元格变更
            for (let j = 0; j < diffCells1.length; j++) {
              if (diffCells1[j]["type"] === 0) {
                const { d1: paraDiff1, d2: paraDiff2 } = this.getDiffStructure(
                  diffCells1[j].children,
                  diffCells2[j].children
                );

                for (let p = 0; p < paraDiff1.length; p++) {
                  if (paraDiff1[p]["type"] === 0) {
                    const change = this.mainDiff(
                      paraDiff1[p].id,
                      paraDiff1[p]["value"],
                      paraDiff2[p]["value"],
                      traceInfo
                    );
                    paraDiff1[p] = {
                      id: paraDiff1[p].id,
                      structure: "para",
                      change,
                    };
                  } else {
                    const change = this.mainDiff(
                      paraDiff1[p].id,
                      "",
                      paraDiff2[p]["value"] ?? "",
                      traceInfo
                    );
                    const meta = change.length
                      ? change
                      : [
                          {
                            0: paraDiff1[p].type,
                            1: 0,
                            2: paraDiff1[p].value,
                            meta: this.getMeta(
                              traceInfo,
                              paraDiff1[p].id,
                              paraDiff1[p].type
                            ),
                          },
                        ];
                    paraDiff1[p] = {
                      id: paraDiff1[p].id,
                      structure: "para",
                      change: meta,
                    };
                  }
                }
                updatedCells.push({
                  ...diffCells2[j],
                  structure: "cell",
                  children: paraDiff1,
                });
              } else if (diffCells1[j]["type"] === 1) {
                const { d1: newParas } = this.getDiffStructure(
                  diffCells1[j].children,
                  diffCells2[j].children
                );
                const meta = this.getMeta(traceInfo, newParas[0].id, 1);
                const children = newParas.map((d) => ({
                  id: d.id,
                  structure: "para",
                  change: [{ 0: 1, 1: 0, 2: d.value, meta }],
                }));
                updatedCells.push({
                  ...diffCells2[j],
                  structure: "cell",
                  children,
                });
              }
            }
            diffData[i] = { ...d2[i], structure: "table", cells: updatedCells };
          }
        } else {
          const meta = this.getMeta(
            traceInfo,
            diffData[i].id,
            diffData[i].type,
            createDate,
            userName
          );
          if (diffData[i].id.startsWith("para")) {
            let value = diffData[i].value;
            diffData[i] = {
              id: diffData[i].id,
              structure: "para",
              change: [{ 0: diffData[i].type, 1: 0, 2: value, meta }],
            };
          } else {
            const updatedCells = diffData[i].cells.map((cell) => {
              const updatedChildren = cell.children.map((p) => ({
                id: p.id,
                structure: "para",
                change: [{ 0: diffData[i].type, 1: 0, 2: p.value, meta }],
              }));
              return {
                ...cell,
                type: diffData[i].type,
                children: updatedChildren,
              };
            });
            diffData[i] = {
              ...diffData[i],
              structure: "table",
              cells: updatedCells,
              meta,
            };
          }
        }
      }

      return diffData;
    },

    /**
     * 保存修改痕迹接口
     * isMerge 是否合并同一用户同一次登录的痕迹，传入true时会每次保存时获取上一次的痕迹信息id比对，如果相同则将上次痕迹移除再保存当前对比的痕迹。
     */
    saveTraceInfo(isMerge) {
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      const { editor } = this.instance;
      const exception_msg = "内容未修改，无需保存痕迹！";
      //如果内容未变化，则不进行痕迹保存
      if (!editor.is_modified) {
        return { success: false, msg: exception_msg };
      }
      // 获取到需要对比的两个rawData
      let rawData1 = editor.raw;
      let rawData2 = editor.getRawData();
      const allParaId = [];
      const paragraph = editor.root_cell.paragraph;
      paragraph.forEach((para) => {
        allParaId.push(para.id);
      });

      rawData1 = this.instance.utils.useRawDataByConfig(rawData1);
      rawData2 = this.instance.utils.useRawDataByConfig(rawData2);

      // 通过对比rawData返回异同的痕迹信息
      const traceInfo = this.getDiffTraceInfoByRaw(rawData1, rawData2);
      traceInfo.allParaId = allParaId;
      // 进行验证，当痕迹对比后发现没有不同内容时,不进行保存
      if (!traceInfo.paras.length && !Object.keys(traceInfo.traces).length) {
        return { success: false, msg: exception_msg };
      }
      //如果是合并的话
      if (isMerge) {
        if (editor.document_meta && editor.document_meta.traceInfo) {
          if (typeof editor.document_meta.traceInfo === "string") {
            editor.document_meta.traceInfo = this.instance.utils.uncompressData(
              editor.document_meta.traceInfo
            );
          }
          let trace = editor.document_meta.traceInfo.pop();
          if (typeof trace === "string") {
            trace = this.instance.utils.uncompressData(trace);
          }
          if (trace.meta.id !== this.traceId) {
            trace = this.instance.utils.compareData(trace);
            editor.document_meta.traceInfo.push(trace);
          }
        }
      }
      this.saveEditTrace(traceInfo);
      editor.raw = rawData2;
      return { success: true };
    },

    /**
     *  通过rawData对比出异常痕迹主要方法
     *  @param rawData1
     *  @param rawData2
     */
    getDiffTraceInfoByRaw(rawData1, rawData2) {
      const traceInfo = {};
      const res = this.getSimpleArr(rawData1, rawData2);
      const { d1, d2 } = this.getDiffStructure(res[0], res[1]);
      // const contentRes = this.getDiffStructure(res[0], res[1]);
      const headerRes = this.getDiffStructure(res[2], res[3]);
      const d3 = headerRes.d1;
      const d4 = headerRes.d2;
      let needRecord = []; // 只需要记录增加或者删除的段落(表格)
      traceInfo.paras = [];
      traceInfo.traces = {};
      for (let i = 0; i < d1.length; i++) {
        if (d1[i]["type"] === 0) {
          if (d1[i].structure !== "table") {
            const change = this.diffParaText(d1[i]["value"], d2[i]["value"]);
            if (change?.length) {
              change.forEach((c) => (c["groupId"] = d1[i].groupId));
              traceInfo.traces[d1[i].id] = change;
            }
          } else {
            // 如果是表格的话
            // 1、先对比单元格 是否有新增的单元格和删除的单元格 单元格只记录新增和修改的 删除的就不管了 这是讨论过后的原则
            const { d1: cells1, d2: cells2 } = this.getDiffStructure(
              d1[i].cells,
              d2[i].cells
            );
            for (let j = 0; j < cells1.length; j++) {
              const { d1: paras1, d2: paras2 } = this.getDiffStructure(
                cells1[j].children,
                cells2[j].children || []
              );
              if (cells1[j]["type"] === 0) {
                // 2、该单元格是原来就有的 那么判断 里边的段落 是否有新增 删除 或者修改的
                for (let m = 0; m < paras1.length; m++) {
                  if (paras1[m]["type"] === 0) {
                    // 3、对比段落的痕迹
                    const a = this.diffParaText(
                      paras1[m]["value"],
                      paras2[m]["value"]
                    );
                    if (a?.length) {
                      a.forEach((c) => (c["groupId"] = paras1[m].groupId));
                      traceInfo.traces[paras1[m].id] = a;
                    }
                  } else {
                    needRecord.push({
                      ...paras1[m],
                      isInCell: true,
                      cellId: cells1[j].id,
                      tableId: d1[i].id,
                    });
                  }
                }
              } else if (cells1[j]["type"] === 1) {
                // 该单元格是新增的 那么里边所有的段落 都属于新增的
                paras1.forEach((p) => (p["type"] = 1));
                needRecord = [...needRecord, ...paras1];
              }
            }
          }
        } else {
          needRecord.push(d1[i]);
        }
      }
      for (let i = 0; i < d3.length; i++) {
        if (d3[i]["type"] === 0) {
          if (d3[i].structure !== "table") {
            const change = this.diffParaText(d3[i]["value"], d4[i]["value"]);
            if (change?.length) {
              change.forEach((c) => (c["groupId"] = d3[i].groupId));
              traceInfo.traces[d3[i].id] = change;
            }
          } else {
            // 如果是表格的话
            // 1、先对比单元格 是否有新增的单元格和删除的单元格 单元格只记录新增和修改的 删除的就不管了 这是讨论过后的原则
            const { d1: cells1, d2: cells2 } = this.getDiffStructure(
              d3[i].cells,
              d4[i].cells
            );
            for (let j = 0; j < cells1.length; j++) {
              const { d1: paras1, d2: paras2 } = this.getDiffStructure(
                cells1[j].children,
                cells2[j].children || []
              );
              if (cells1[j]["type"] === 0) {
                // 2、该单元格是原来就有的 那么判断 里边的段落 是否有新增 删除 或者修改的
                for (let m = 0; m < paras1.length; m++) {
                  if (paras1[m]["type"] === 0) {
                    // 3、对比段落的痕迹
                    const a = this.diffParaText(
                      paras1[m]["value"],
                      paras2[m]["value"]
                    );
                    if (a?.length) {
                      a.forEach((c) => (c["groupId"] = paras1[m].groupId));
                      traceInfo.traces[paras1[m].id] = a;
                    }
                  } else {
                    needRecord.push({ id: paras1[m].id, type: paras1[m].type });
                    traceInfo.traces[paras1[m].id] = [
                      {
                        0: paras1[m].type,
                        1: 0,
                        2: paras1[m].value,
                        groupId: paras1[m].groupId,
                      },
                    ];
                  }
                }
              } else if (cells1[j]["type"] === 1) {
                // 该单元格是新增的 那么里边所有的段落 都属于新增的
                paras1.forEach((p) => (p["type"] = 1));
                needRecord = [...needRecord, ...paras1];
              }
            }
          }
        } else {
          needRecord.push(d3[i]);
        }
      }
      traceInfo.paras = needRecord;
      traceInfo.meta = {
        id: this.traceId ?? getUUID("trace-"),
        userId: this.currentUser ? this.currentUser.id : "-1", // -1 代表未登录
        date: Date.now(),
      };
      return traceInfo;
    },
    /**
     * 保存修改痕迹
     * @param traceInfo
     */
    saveEditTrace(traceInfo) {
      traceInfo = this.instance.utils.compressData(traceInfo);
      const { document_meta } = this.instance.editor;
      if (!document_meta.traceInfo) {
        document_meta.traceInfo = [];
        document_meta.traceInfo.push(traceInfo);
      } else {
        if (typeof document_meta.traceInfo === "string") {
          document_meta.traceInfo = this.instance.utils.uncompressData(
            document_meta.traceInfo
          );
        }
        document_meta.traceInfo.push(traceInfo);
      }
    },
    setPageDirection(traceRawData, direction) {
      // traceRawData 是引用类型 修改完了不用返回
      if (traceRawData.config) {
        traceRawData.config.direction = direction;
      } else {
        traceRawData.config = {};
        traceRawData.config.direction = direction;
      }
    },
    compareGroupTraces({
      rawData1,
      rawData2,
      showPersonInfo = false,
      groupId,
      useNewVersion,
    }) {
      //TODO  tang 研究研究
      const editor1 = this.instance.editor.copyEditor();
      const editor2 = this.instance.editor.copyEditor();
      editor1.reInitRaw(rawData1);
      editor2.reInitRaw(rawData2);
      const date = new Date();

      let group1 = editor1.selection.getGroupByGroupId(groupId);
      let group2 = editor2.selection.getGroupByGroupId(groupId);
      let groupData1;
      let groupData2;
      let createDate;
      let userId;
      groupData1 = group1 ? editor1.getGroupRawData(group1, true) : "";
      groupData2 = group2 ? editor2.getGroupRawData(group2, true) : "";
      if (!group1) {
        group1 = editor1.createElement("group", { groupId, date });
        groupData1 = editor1.getGroupRawData(group1, true);
        createDate = group2.date;
        userId = group2.meta.createUserId;
      }
      if (!group2) {
        group2 = editor2.createElement("group", { groupId, date });
        groupData2 = editor2.getGroupRawData(group2, true);
        createDate = group1.date;
        userId = group1.meta.createUserId;
        if (group1) {
          if (groupData1.content.length) {
            groupData2.content = JSON.parse(JSON.stringify(groupData1.content));
            groupData2.content = [groupData2.content.pop()];
            if (groupData2.content) {
              groupData2.content[0].children.forEach((child) => {
                child.value = "";
              });
            }
          }
        }
      }

      const vL1 = groupData1?.meta?.versionList;
      const vL2 = groupData2?.meta?.versionList;
      if (vL1?.length && vL2?.length) {
        const lV1 = vL1[0].version;
        const lV2 = vL2[0].version;
        if (
          this.instance.utils.versionDiff(lV1, "10.10.7") < 0 ||
          this.instance.utils.versionDiff(lV2, "10.10.7") < 0
        ) {
          useNewVersion = false;
        } else {
          if (groupData1?.meta?.traceInfo) {
            const traceInfo = groupData1?.meta?.traceInfo;
            for (let i = 0; i < traceInfo.length; i++) {
              let currentTrace = traceInfo[i];
              if (typeof currentTrace === "string") {
                currentTrace = this.instance.utils.uncompressData(currentTrace);
              }
              currentTrace.paras = currentTrace.paras.filter(
                (p) => p.groupId === groupId
              );
              const newTraces = {};
              for (const key in currentTrace.traces) {
                if (groupId === currentTrace.traces[key][0]?.groupId) {
                  newTraces[key] = currentTrace.traces[key];
                }
              }
              currentTrace.traces = newTraces;
            }
          }
          if (groupData2?.meta?.traceInfo) {
            const traceInfo = groupData2?.meta?.traceInfo;
            for (let i = 0; i < traceInfo.length; i++) {
              let currentTrace = traceInfo[i];
              if (typeof currentTrace === "string") {
                currentTrace = this.instance.utils.uncompressData(currentTrace);
              }
              currentTrace.paras = currentTrace.paras.filter(
                (p) => p.groupId === groupId
              );
              const newTraces = {};
              for (const key in currentTrace.traces) {
                if (groupId === currentTrace.traces[key][0]?.groupId) {
                  newTraces[key] = currentTrace.traces[key];
                }
              }
              currentTrace.traces = newTraces;
            }
          }
        }
      }

      const raw = useNewVersion
        ? this.newVersionCompareTraces(
            groupData1,
            groupData2,
            showPersonInfo,
            createDate,
            userId
          )
        : this.compareTraces(
            groupData1,
            groupData2,
            showPersonInfo,
            createDate,
            userId
          );
      if (raw.content.length === 0)
        [(raw.content = this.editor.config.rawData.content)];
      return raw;
    },
    compareTraces(
      rawData,
      rawData2,
      showPersonInfo = false,
      createDate,
      userId,
      useNewCompare,
      externalParam = { compareHeader: true }
    ) {
      const { compareHeader } = externalParam;
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      if (Object.prototype.toString.call(rawData) !== "[object Object]") {
        rawData = JSON.parse(rawData);
      }
      if (Object.prototype.toString.call(rawData2) !== "[object Object]") {
        rawData2 = JSON.parse(rawData2);
      }
      this.editorId = getUUID("editor");
      let swapPositionRawData1 = rawData;
      let swapPositionRawData2 = rawData2;

      const traceInfo1 = rawData?.meta?.traceInfo;
      const traceInfo2 = rawData2?.meta?.traceInfo;
      if (traceInfo1 && traceInfo2) {
        if (traceInfo1.length > traceInfo2.length) {
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        if (!traceInfo1) {
          // traceInfo1 不存在 不管traceInfo2 有没有 都不用交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData, rawData2];
        } else if (!traceInfo2) {
          // traceInfo1 存在 那么 traceInfo2 没有的话 才需要交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData];
        }
      }

      const res = this.getSimpleArr(swapPositionRawData1, swapPositionRawData2);
      const preTraceInfo = swapPositionRawData1?.meta?.traceInfo || [];
      const nextTraceInfo = swapPositionRawData2?.meta?.traceInfo || [];
      let lastTraceInfo = nextTraceInfo.slice(
        preTraceInfo.length,
        nextTraceInfo.length + 1
      );
      if (Array.isArray(lastTraceInfo)) {
        for (let i = 0; i < lastTraceInfo.length; i++) {
          if (typeof lastTraceInfo[i] === "string") {
            lastTraceInfo[i] = this.instance.utils.uncompressData(
              lastTraceInfo[i]
            );
          }
        }
      }
      let contentRes = this.getDiffData(
        res[0],
        res[1],
        lastTraceInfo,
        createDate,
        userId
      );
      const headerRes = this.getDiffData(res[2], res[3], lastTraceInfo);

      if (useNewCompare) {
        // 获得所有添加或者删除的段落;
        let allChangeParas = this.handlePastTracePara(lastTraceInfo);
        contentRes = this.getParaDiffData(
          lastTraceInfo,
          allChangeParas,
          contentRes
        );
      }

      F: for (const data of contentRes) {
        if (!data.cells) {
          for (const o of data.change) {
            if (o["0"] !== 0 && !o.meta) {
              showPersonInfo = false;
              break F;
            }
          }
        } else {
          for (const cell of data.cells) {
            for (const obj of cell.children) {
              for (const o of obj.change) {
                if (o["0"] !== 0 && !o.meta) {
                  showPersonInfo = false;
                  break F;
                }
              }
            }
          }
        }
      }
      F: for (const data of headerRes) {
        if (!data.cells) {
          for (const o of data.change) {
            if (o["0"] !== 0 && !o.meta) {
              showPersonInfo = false;
              break F;
            }
          }
        } else {
          for (const cell of data.cells) {
            for (const obj of cell.children) {
              for (const o of obj.change) {
                if (o["0"] !== 0 && !o.meta) {
                  showPersonInfo = false;
                  break F;
                }
              }
            }
          }
        }
      }
      const traceRawData = this.showCompareTraces(
        contentRes,
        swapPositionRawData2,
        showPersonInfo
      );
      const traceHeaderRawData = this.showCompareTraces(
        headerRes,
        swapPositionRawData2,
        showPersonInfo,
        "header"
      );
      if (compareHeader) {
        traceRawData.header = traceHeaderRawData.header;
      }
      traceRawData.meta.traceInfo = [
        ...traceRawData.meta.traceInfo,
        ...traceHeaderRawData.meta.traceInfo,
      ];
      if (showPersonInfo) {
        this.instance.editor.view_mode = "person";
      } else {
        this.instance.editor.view_mode = "noPerson";
      }
      // 对比结果的页面方向按照最新数据的页面来判断
      if (swapPositionRawData2?.config?.direction === "horizontal") {
        this.setPageDirection(traceRawData, "horizontal");
      } else {
        this.setPageDirection(traceRawData, "vertical");
      }
      return traceRawData;
    },

    // 新版痕迹对比用，将原始数据中的文本域类型的 obj 转换成平铺开的 obj 数组
    putProcessedObj2Arr(obj, arr, resRawData) {
      for (let i = 0; i < obj.children.length; i++) {
        const current = obj.children[i];
        if (current.type === "field") {
          this.putProcessedObj2Arr(current, arr, resRawData);
        } else if (current.type === "text") {
          if (!current.value) {
            current.continue = true;
          }
          arr.push(current);
        } else if (current.type === "image") {
          // 这个是处理的原始数据里边的 一个 image 就是一个对象
          const src = this.traceImageMap[obj.id]?.src;
          if (!src) {
            arr.push({
              ...current,
              type: "text",
              value: " ",
            });
          } else {
            arr.push({
              ...current,
              src,
            });
          }
        } else if (current.type === "widget") {
          if (current.widgetType === "caliper") {
            arr.push({
              type: "text",
              value: " ",
            });
          } else {
            arr.push({
              ...current,
            });
          }
        } else if (current.type === "button" || current.type === "line") {
          // 按钮和水平线都不参与痕迹对比了
        }
      }
    },

    // 因为 text 里边记录了很多种类的痕迹 所以写成个方法专门处理 根据字符串获取组装段落的数组
    newVersionGetParaChildrenByText(
      text,
      lastTraceInfo,
      deleteUser,
      userDeleteStyleList
    ) {
      // TODO 目前需要进来这里的 都是删除的需要恢复的 还需要再继续考量是不是这么回事  因为如果新增的话 rawData 中本来就已经有了 纠结的一个小点 是这里边的内容啥时候变成新增的 这个问题
      const res = [];
      // 使用正则表达式匹配所有以$$$widget开头并以$$$结尾的记录
      // TODO 不光有 $$$widget 还有图片什么的 后期都得在这里处理
      const regex = /\$\$\$[^$]*\$\$\$/g;
      const parts = []; // 拆分成各种种类的一个数组
      let lastIndex = 0;
      let match;

      // 迭代所有匹配结果
      while ((match = regex.exec(text)) !== null) {
        // 将匹配记录前的文字部分添加到数组中
        if (match.index > lastIndex) {
          parts.push(text.slice(lastIndex, match.index));
        }
        // 将匹配记录添加到数组中
        parts.push(match[0]);
        // 更新 lastIndex
        lastIndex = regex.lastIndex;
      }
      // 将最后一个匹配后的文字部分添加到数组中
      if (lastIndex < text.length) {
        parts.push(text.slice(lastIndex));
      }

      for (let i = 0; i < parts.length; i++) {
        let current;
        if (parts[i].includes("$$$widget")) {
          if (parts[i].includes("-caliper-")) {
            current = {
              // 跟等于 text 一样 就改 value 为一个空格
              type: "text",
              keep: true,
              font_id: deleteUser?.font_id || userDeleteStyleList[0].font_id,
              value: " ",
              reduce: true,
              meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
            };
          } else {
            const arr = parts[i].slice(0, -3).split("-");
            const selected = arr[arr.length - 1] === "true";
            current = {
              type: "widget",
              field_id: null,
              disabled: 0,
              field_position: "normal",
              widgetType: parts[i].includes("radio") ? "radio" : "checkbox",
              height: 16,
              params: {},
              id: "widget-c4c639fb",
              border: "solid",
              selected,
              keep: true,
              font_id: deleteUser?.font_id || userDeleteStyleList[0].font_id,
              reduce: true,
              meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
            };
          }
        } else if (parts[i].includes("$$$image")) {
          const imageId = parts[i].split("$$$")[1];
          const src = this.traceImageMap[imageId]?.src;
          if (src) {
            current = {
              type: "image",
              src,
              field_id: null,
              field_position: "normal",
              width: 150,
              height: 50,
              ori_width: 150,
              ori_height: 50,
              id: "image-" + Math.random(),
              url: "",
              keep: true,
              font_id: deleteUser?.font_id || userDeleteStyleList[0].font_id,
              reduce: true,
              meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
            };
          } else {
            current = {
              type: "text",
              keep: true,
              font_id: deleteUser?.font_id || userDeleteStyleList[0].font_id,
              value: " ",
              reduce: true,
              meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
            };
          }
        } else {
          current = {
            type: "text",
            keep: true,
            font_id: deleteUser?.font_id || userDeleteStyleList[0].font_id,
            value: parts[i],
            reduce: true,
            meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
          };
          !current.value && (current.continue = true);
        }
        res.push(current);
      }
      return res;
    },

    // 抽离出来处理段落的逻辑
    // 表格里边的和表格外边的 处理逻辑应该是一样的 但是要单独处理 将所有紧挨着的段落都放在一块 到下一个处理的时机的时候处理
    newVersionHandleParaLevelCompare(
      lastTraceInfo,
      resRawData,
      addUser,
      userAddStyleList,
      deleteUser,
      userDeleteStyleList,
      basePara
    ) {
      // 所有新增的段落痕迹都不参与下标的计算，如果遇到了痕迹下标就要优先 splice 然后才是继续循环 continue 的项
      // 当删除变成新增的时候 应该将所有 keep 的 copy 一下 放到一个数组里边 等循环到下标不等于上一次的下标+1 的时候再处理保存的 keep 的痕迹 同时清空数组
      // 如果全部循环结束了 保存 keep 的数组里边还有内容 也要继续处理

      // keeps 和  cellKeeps 都是在处理新增的痕迹的时候往里放内容的
      const keeps = []; // 新增时记录的 后边版本中删除的痕迹 需要保留下来
      let lastRecordIndex = -1; // 我得记录下来到哪儿了这个下标因为有可能已经结束了 需要处理 deletes 了 但是既不会再次走 type === 1 里边的情况 也不会走 type === -1 里边的情况了 也得处理 记录最后的下标

      const cellKeeps = [];
      let cellLastRecordIndex = -1; // 因为连续的段落我要统一处理 所以我要判断是否连续 循环到的每个段落的下标都要记录下来 这次记录的下标 +1 不等于下次痕迹里边的下标了就说明不连续了

      let lastCell = {}; // 只记录表格里边的单元格

      const deletes = []; // 保存所有删除的段落
      let firstDeleteIndex = -1; // 这个是删除痕迹恢复的时候 splice 用的下标
      let lastDeleteIndex = -1; // 记录最后删除的下标

      const cellDeletes = [];
      let cellFirstDeleteIndex = -1;
      let cellLastDeleteIndex = -1;
      for (let i = 0; i < lastTraceInfo.paras.length; i++) {
        const paraLevelTrace = lastTraceInfo.paras[i]; // 这里边有表格有段落 段落有的是在外边的有的是在表格里边的 有属性 isInCell 做了标记
        if (paraLevelTrace.type === 1) {
          // 因为我是要处理单元格里边的 所以判断这么写没问题 而且写在这里就可以
          if (cellDeletes.length > 0 && lastCell.children) {
            // 一旦到新增这里 判断条件都不用那么多了 只要现在这个判断条件就可以
            // 不在同一个单元格了或者记录的下标不连续了就要统一处理上一波记录的内容
            lastCell.children.splice(cellFirstDeleteIndex, 0, ...cellDeletes);
            cellDeletes.length = 0;
            cellFirstDeleteIndex = -1;
            cellLastDeleteIndex = -1;
          }
          // 新增的也有可能在后边版本中给删除了 所以是不能直接修改 rawData 中的数据的
          if (paraLevelTrace.isInCell) {
            // 一旦循环到单元格里边的段落就要处理完 单元格外边的
            if (keeps.length > 0) {
              resRawData.content.splice(lastRecordIndex + 1, 0, ...keeps);
              keeps.length = 0;
              lastRecordIndex = -1;
            }
            const table = resRawData.content.find(
              (table) => table.id === paraLevelTrace.tableId
            );
            if (table) {
              const cell = table.cells.find(
                (cell) => cell.id === paraLevelTrace.cellId
              );
              if (cell && cell.children) {
                const paraIndex = cell.children.findIndex(
                  (para) => para.id === paraLevelTrace.id
                );
                const para = cell.children[paraIndex];
                const copied = JSON.parse(JSON.stringify(para));
                // 因为是新增 我先直接修改样式 但是这个新增有可能是后边版本中删除的 所以 rawData 中可能是删除的痕迹 是需要保存下来的 所以 copied 是这次新增在后边删除的痕迹
                para.continue = true;
                para.children.forEach((obj) => {
                  obj.continue = true;
                  obj.add = true;
                  obj.font_id = addUser?.font_id || userAddStyleList[0].font_id;
                  obj.meta = JSON.parse(JSON.stringify(lastTraceInfo.meta));
                });

                // 从单元格外 走到这个单元格里 就要处理单元格外的
                if (
                  cellLastRecordIndex + 1 !== paraIndex ||
                  (lastCell.id !== cell.id && lastCell.children) // 因为单元格外边的时候将 lastCell 重新赋值成了 空对象
                ) {
                  cell.children.splice(
                    cellLastRecordIndex + 1,
                    0,
                    ...cellKeeps
                  );
                  cellKeeps.length = 0;
                }

                if (copied.keep) {
                  copied.children.forEach((o) => (o.continue = true));
                  copied.id = Math.random();
                  cellKeeps.push(copied);
                  copied.continue = true;
                  copied.keep = false;
                  cellLastRecordIndex = paraIndex;
                }

                lastCell = cell;
              }
            }
          } else {
            // 一旦循环到单元格外边的 就要将单元格里边的都处理完
            if (lastCell.children && cellKeeps.length > 0) {
              lastCell.children.splice(
                cellLastRecordIndex + 1,
                0,
                ...cellKeeps
              );
              cellKeeps.length = 0;
              cellLastRecordIndex = -1;
            }
            lastCell = {}; // 不是表格了 lastCell 就不能有值了 保证 lastCell 只记录单元格

            let index = resRawData.content.findIndex(
              (para) => para.id === paraLevelTrace.id
            );
            const paraLevelContent = resRawData.content[index];
            if (!paraLevelContent) continue; // 避免报错吧
            paraLevelContent.continue = true; // 整个表格都不参与 里边的段落就没有必要 continue 了 表格里边的段落就会走上边的逻辑了
            const copied = JSON.parse(JSON.stringify(paraLevelContent));
            if (paraLevelTrace.structure === "table") {
              for (const cell of paraLevelContent.cells) {
                // 跟下边删除大部分重复代码 但是这里的所有内容在痕迹里边都记录了 下边删除需要重新创建内容
                for (let j = 0; j < cell.children.length; j++) {
                  const para = cell.children[j];
                  for (let n = 0; n < para.children.length; n++) {
                    const obj = para.children[n];
                    if (obj.continue) {
                      continue;
                    }
                    // 整个表格是新增的 但是里边的内容可能已经在后边的版本中进行过了修改 所以不能简单的全部都改为新增
                    // 认为 里边所有 obj.keep 的都是这次新增的
                    const copy = JSON.parse(JSON.stringify(obj));
                    if (obj.keep) {
                      obj.keep = false;
                      copy.continue = true;
                      copy.add = true;
                      copy.font_id =
                        addUser?.font_id || userAddStyleList[0].font_id;
                      copy.meta = JSON.parse(
                        JSON.stringify(lastTraceInfo.meta)
                      );
                      para.children.splice(n, 0, copy);
                      n++;
                    } else {
                      obj.add = true;
                      obj.continue = true;
                      obj.font_id =
                        addUser?.font_id || userAddStyleList[0].font_id;
                      obj.meta = JSON.parse(JSON.stringify(lastTraceInfo.meta));
                    }
                  }
                }
              }
            } else if (paraLevelTrace.structure === "para") {
              paraLevelContent.children.forEach((obj) => {
                obj.continue = true;
                obj.add = true;
                obj.font_id = addUser?.font_id || userAddStyleList[0].font_id;
                obj.meta = JSON.parse(JSON.stringify(lastTraceInfo.meta));
              });
            }

            if (lastRecordIndex + 1 !== index) {
              // 不用判断 lastCell 了 因为一开始就判断了
              // 说明这个时候就要处理 deletes 里边的段落了 因为这个时候段落就已经不连续了
              resRawData.content.splice(lastRecordIndex + 1, 0, ...keeps);
              keeps.length = 0;
              // 这个位置应该是每次 keep 的时候记录下来的 直到不一样了 就使用最后一次记录的 keep 位置
            }

            // 表格不一样 表格的话 删除整个表格还是新增整个表格 都是操作的这一个表格
            if (copied.keep && copied.type !== "table") {
              copied.id = Math.random(); // 随机生成一个 id 因为不改的话 就跟修改成新增的当前这个段落 id 重复了 鼠标悬停上去 显示操作人和时间就不对了
              keeps.push(copied);
              copied.continue = true;
              copied.keep = false;
              lastRecordIndex = index;
            }
          }
        } else if (paraLevelTrace.type === -1) {
          if (deletes.length === 0) {
            // 当 deletes 是个空数组的时候 说明是第一次 所以要记录第一个的下标 到时候 splice 的时候将连续的都插到这个位置的后边
            firstDeleteIndex = paraLevelTrace["i"];
          }
          // 都到删除了 新增里边保存的数据都要进行处理 1
          if (lastCell.children && cellKeeps.length > 0) {
            lastCell.children.splice(cellLastRecordIndex + 1, 0, ...cellKeeps);
            cellKeeps.length = 0;
            cellLastRecordIndex = -1;
          }
          // 都到删除了 新增里边保存的数据都要进行处理 2
          if (keeps.length > 0) {
            resRawData.content.splice(lastRecordIndex, 0, ...keeps);
            keeps.length = 0;
          }
          // 因为我是要处理单元格里边的 所以判断这么写没问题 而且写在这里就可以
          if (
            (cellDeletes.length > 0 &&
              lastCell.children &&
              lastCell.id !== paraLevelTrace.cellId) ||
            (cellLastDeleteIndex !== -1 &&
              cellLastDeleteIndex + 1 !== paraLevelTrace["i"])
          ) {
            // 不在同一个单元格了或者记录的下标不连续了就要统一处理上一波记录的内容
            lastCell.children.splice(cellFirstDeleteIndex, 0, ...cellDeletes);
            cellDeletes.length = 0;
            cellFirstDeleteIndex = -1;
            cellLastDeleteIndex = -1;
          }
          // 因为是要展示痕迹的中间过程 所以所有痕迹都要保存下来 只是参不参与下标计算的问题
          // 那么这次删除的数据 再跟更早的版本对比的时候就有可能变成新增的
          if (paraLevelTrace.structure === "table") {
            paraLevelTrace.keep = true; // 所有删除的痕迹都要保留
            for (const cell of paraLevelTrace.cells) {
              const cellChildren = [];
              for (const para of cell.children) {
                const newPara = JSON.parse(JSON.stringify(basePara));
                newPara.keep = true;
                newPara.id = para.id;
                const text = para.value;
                // 因为 text 里边包含了所有的痕迹记录不仅仅是纯文字 所以要根据记录返回段落所需的 obj 数组 ↓
                const paraChildren = this.newVersionGetParaChildrenByText(
                  text,
                  lastTraceInfo,
                  deleteUser,
                  userDeleteStyleList
                );
                newPara.children = paraChildren;
                // 因为 text 里边包含了所有的痕迹记录不仅仅是纯文字 所以要根据记录返回段落所需的 obj 数组 ↑
                cellChildren.push(newPara);
              }
              cell.children = cellChildren;
            }
            paraLevelTrace.type = "table";
            // TODO 这个位置不对 因为 resRawData 经过若干个版本的痕迹对比之后 里边的内容就会很多了 但是痕迹里边记录的删除位置只是当时那个版本的位置 所以不能简单的根据记录的位置 splice
            if (lastDeleteIndex + 1 !== paraLevelTrace["i"]) {
              // 说明不连续了
              resRawData.content.splice(firstDeleteIndex, 0, ...deletes);
              deletes.length = 0;
            }
            lastDeleteIndex = paraLevelTrace["i"];
            deletes.push(paraLevelTrace);
          } else if (paraLevelTrace.structure === "para") {
            const newPara = JSON.parse(JSON.stringify(basePara));
            newPara.keep = true;
            newPara.id = paraLevelTrace.id;
            const text = paraLevelTrace.value;
            // 我应该将所有删除的 并且连续都统一进行处理
            const paraChildren = this.newVersionGetParaChildrenByText(
              text,
              lastTraceInfo,
              deleteUser,
              userDeleteStyleList
            );
            newPara.children = paraChildren;
            if (paraLevelTrace.isInCell) {
              // 应该先判断先处理上一波的
              const table = resRawData.content.find(
                (table) => table.id === paraLevelTrace.tableId
              );
              if (table) {
                const cell = table.cells.find(
                  (cell) => cell.id === paraLevelTrace.cellId
                );
                if (cell && cell.children) {
                  if (cellDeletes.length === 0) {
                    cellFirstDeleteIndex = paraLevelTrace["i"];
                  }
                  cellDeletes.push(newPara);
                  cellLastDeleteIndex = paraLevelTrace["i"];
                  lastCell = cell;
                }
              }
            } else {
              if (lastDeleteIndex + 1 !== paraLevelTrace["i"]) {
                // 说明不连续了
                resRawData.content.splice(firstDeleteIndex, 0, ...deletes);
                deletes.length = 0;
              }
              lastDeleteIndex = paraLevelTrace["i"];
              deletes.push(newPara);
            }
          }
        }
      }
      // 如果都循环完了 还没处理完 就要统一处理一下
      if (keeps.length > 0) {
        resRawData.content.splice(lastRecordIndex + 1, 0, ...keeps);
        keeps.length = 0;
      }
      if (deletes.length > 0) {
        resRawData.content.splice(firstDeleteIndex, 0, ...deletes);
        deletes.length = 0;
      }
      if (cellKeeps.length > 0) {
        lastCell.children.splice(cellLastRecordIndex + 1, 0, ...cellKeeps);
        cellKeeps.length = 0;
      }
      if (cellDeletes.length > 0) {
        lastCell.children.splice(cellFirstDeleteIndex, 0, ...cellDeletes);
        cellDeletes.length = 0;
      }
    },

    // 获取比较数据
    newVersionGetComparisonData(
      rawData,
      rawData2,
      showPersonInfo,
      createDate,
      userId
    ) {
      if (showPersonInfo) {
        this.instance.editor.view_mode = "person";
      } else {
        this.instance.editor.view_mode = "noPerson";
      }
      rawData = this.instance.utils.useRawDataByConfig(rawData);
      rawData2 = this.instance.utils.useRawDataByConfig(rawData2);

      const vL1 = rawData?.meta?.versionList;
      const vL2 = rawData2?.meta?.versionList;

      if (vL1?.length && vL2?.length) {
        const lV1 = vL1[0].version;
        const lV2 = vL2[0].version;
        if (
          this.instance.utils.versionDiff(lV1, "10.9.1") < 0 ||
          this.instance.utils.versionDiff(lV2, "10.9.1") < 0
        ) {
          // 只要有一个版本比较低就走原来的版本对比
          this.$editor.info("旧版痕迹对比，不展示中间过程");
          return {
            oldData: this.compareTraces(
              rawData,
              rawData2,
              showPersonInfo,
              createDate,
              userId
            ),
          };
        }
      }

      // 我始终都是基于 rawData 修改 rawData.content 里边的每个对象
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      if (Object.prototype.toString.call(rawData) !== "[object Object]") {
        rawData = JSON.parse(rawData);
      }
      if (Object.prototype.toString.call(rawData2) !== "[object Object]") {
        rawData2 = JSON.parse(rawData2);
      }
      this.editorId = getUUID("editor");
      let swapPositionRawData1 = rawData;
      let swapPositionRawData2 = rawData2;

      const traceInfo1 = rawData?.meta?.traceInfo;
      const traceInfo2 = rawData2?.meta?.traceInfo;
      if (traceInfo1 && traceInfo2) {
        if (traceInfo1.length > traceInfo2.length) {
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        if (!traceInfo1) {
          // traceInfo1 不存在 不管traceInfo2 有没有 都不用交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData, rawData2];
        } else if (!traceInfo2) {
          // traceInfo1 存在 那么 traceInfo2 没有的话 才需要交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData];
        }
      }

      // 创建删除和新增的样式 ↓
      // 获取修改用户信息
      const userInfoList = rawData2?.meta?.userInfo || [];
      // 各用户展示颜色，数组后两位，null代表默认背景颜色，red代表删除颜色，其他颜色则为多用户各自颜色

      // 用户信息和对应的样式id的数组
      const userAddStyleList = [];
      const userDeleteStyleList = [];
      if (showPersonInfo && userInfoList && userInfoList.length) {
        for (let j = 0; j < userInfoList.length; j++) {
          this.fontStyleList[j % 10].fontStyle.dblUnderLine = true;
          userAddStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id: this.fontStyleList[j % 10].fontId,
          });
          userDeleteStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id:
              this.fontStyleList[(j % 10) + this.bgColorList.length].fontId,
          });
        }
      }
      if (!userAddStyleList.length) {
        this.fontStyleList[0].fontStyle.dblUnderLine = true;
        userAddStyleList.push({
          font_id: this.fontStyleList[0].fontId,
        });
      }
      if (!userDeleteStyleList.length) {
        userDeleteStyleList.push({
          font_id: this.fontStyleList[0 + this.bgColorList.length].fontId,
        });
      }
      // 创建删除和新增的样式 ↑

      swapPositionRawData2 = JSON.parse(JSON.stringify(swapPositionRawData2));

      // 接下来就是 在 swapPositionRawData2 的基础上还原成 swapPositionRawData1

      const preTraceInfo = swapPositionRawData1?.meta?.traceInfo || [];
      const nextTraceInfo = swapPositionRawData2?.meta?.traceInfo || [];

      const needTraceInfo = nextTraceInfo.slice(
        preTraceInfo.length,
        nextTraceInfo.length + 1
      ); // 需要处理的痕迹对比 要截取两个版本之间的对比痕迹 进行恢复

      for (let i = 0; i < needTraceInfo.length; i++) {
        if (typeof needTraceInfo[i] === "string") {
          needTraceInfo[i] = this.instance.utils.uncompressData(
            needTraceInfo[i]
          );
        }
      }

      this.getSimpleParaArr(swapPositionRawData1.content, swapPositionRawData1);
      this.getSimpleParaArr(swapPositionRawData2.content, swapPositionRawData2);
      swapPositionRawData2["groups"] = [];
      return {
        swapPositionRawData2,
        needTraceInfo,
        userAddStyleList,
        userDeleteStyleList,
      };
    },

    newVersionHandleRawData(resRawData) {
      for (let i = 0; i < resRawData.content.length; i++) {
        const paraOrTable = resRawData.content[i];
        if (paraOrTable.type === "p") {
          let recordIndex = 0;
          for (let o = 0; o < paraOrTable.children.length + recordIndex; o++) {
            const obj = paraOrTable.children[o];
            if (obj.type === "field") {
              const arr = [];
              this.putProcessedObj2Arr(obj, arr, resRawData);
              paraOrTable.children.splice(o, 1, ...arr);
              o += arr.length - 1; // 减1 是因为后边还要执行 o++
              // 只要遇到文本域就要将里边的纯文本拿出来放到一个 obj 里边 然后将不是纯文本的放到一个 obj 里边 但是要组成一个 obj 数组 然后放到 paraOrTable.children 里边去 替换掉这个 obj
              // 我要递归进去
            } else if (obj.type === "image") {
              const src = this.traceImageMap[obj.id]?.src;
              if (!src) {
                obj.type = "text";
                obj.value = " ";
              } else {
                obj.src = src;
              }
            } else if (obj.type === "widget" && obj.widgetType === "caliper") {
              obj.type = "text";
              obj.value = " ";
            } else if (obj.type === "button" || obj.type === "line") {
              // 按钮 和 分割线都不进行痕迹展示了
              paraOrTable.children.splice(o, 1);
              o--;
            } else if (obj.type === "text" && !obj.value) {
              // 空的对象就不再继续走了 要不然新增的地方会有问题 会把空的当成新增的出现死循环
              obj.continue = true;
            }
          }
        } else if (paraOrTable.type === "table") {
          // 从表格里边找到该段落
          const cells = paraOrTable.cells;
          for (let j = 0; j < cells.length; j++) {
            const paras = cells[j].children; // 这是段落集合
            for (let p = 0; p < paras.length; p++) {
              // 在这个段落里边找文本域
              let recordIndex = 0;
              for (let o = 0; o < paras[p].children.length + recordIndex; o++) {
                const obj = paras[p].children[o];
                if (obj.type === "field") {
                  const arr = [];
                  this.putProcessedObj2Arr(obj, arr, resRawData);
                  paras[p].children.splice(o, 1, ...arr);
                  o += arr.length - 1; // 减1 是因为后边还要执行 o++
                  // 只要遇到文本域就要将里边的纯文本拿出来放到一个 obj 里边 然后将不是纯文本的放到一个 obj 里边 但是要组成一个 obj 数组 然后放到 paraOrTable.children 里边去 替换掉这个 obj
                  // 我要递归进去
                } else if (obj.type === "image") {
                  const src = this.traceImageMap[obj.id]?.src;
                  if (!src) {
                    obj.type = "text";
                    obj.value = " ";
                  } else {
                    obj.src = src;
                  }
                } else if (
                  obj.type === "widget" &&
                  obj.widgetType === "caliper"
                ) {
                  obj.type = "text";
                  obj.value = " ";
                } else if (obj.type === "button" || obj.type === "line") {
                  paras[p].children.splice(o, 1);
                  o--;
                } else if (obj.type === "text" && !obj.value) {
                  // 空的对象就不再继续走了 要不然新增的地方会有问题 会把空的当成新增的出现死循环
                  obj.continue = true;
                }
              }
            }
          }
        }
      }
    },

    newVersionHandleTraceInfo(resRawData, createDate, userId) {
      // 不能没有 traceinfo 就 return 掉 resRawData 那样的话下边逻辑不走就不显示操作人和操作时间了
      if (!resRawData.meta?.traceInfo) {
        resRawData["meta"]["traceInfo"] = [];
      }
      for (let i = 0; i < resRawData.content.length; i++) {
        let paraOrTable = resRawData.content[i];
        if (paraOrTable.type === "p") {
          let para = paraOrTable;
          const traceInfo = {
            id: para.id,
            structure: "para",
            change: [],
          };
          let p = 0; // 记录 para.children 中每个字的下标
          for (let c = 0; c < para.children.length; c++) {
            const obj = para.children[c];
            if (!obj.meta) {
              obj.meta = {
                date: createDate,
                userId,
              };
            }
            !obj.meta.date && (obj.meta["date"] = createDate);
            !obj.meta.userId && (obj.meta["userId"] = userId);
            // 有的 obj 会既有 add 又有 reduce 但是 add 要靠前
            if (obj.add) {
              traceInfo.change.push({
                0: 1,
                1: p,
                2: obj.type === "text" ? obj.value : " ",
                meta: obj.meta,
              });
            } else if (obj.reduce) {
              traceInfo.change.push({
                0: -1,
                1: p,
                2: obj.type === "text" ? obj.value : " ",
                meta: obj.meta,
              });
            } else {
              traceInfo.change.push({
                0: 0,
                1: p,
                2: obj.type === "text" ? obj.value : " ",
                meta: obj.meta,
              });
            }
            if (obj.type === "text") {
              p += obj.value.length;
            } else {
              p++;
            }
          }

          resRawData.meta.traceInfo.push(traceInfo);
        } else if (paraOrTable.type === "table") {
          paraOrTable = JSON.parse(JSON.stringify(paraOrTable));
          for (const cell of paraOrTable.cells) {
            const arr = [];
            for (let j = 0; j < cell.children.length; j++) {
              let para = cell.children[j];
              const traceInfo = {
                id: para.id,
                structure: "para",
                change: [],
              };
              let p = 0;
              for (let c = 0; c < para.children.length; c++) {
                const obj = para.children[c];
                if (obj.type === "widget") {
                  obj.value = " ";
                }
                if (obj.add) {
                  traceInfo.change.push({
                    0: 1,
                    1: p,
                    2: obj.type === "text" ? obj.value : " ",
                    meta: obj.meta,
                  });
                } else if (obj.reduce) {
                  traceInfo.change.push({
                    0: -1,
                    1: p,
                    2: obj.type === "text" ? obj.value : " ",
                    meta: obj.meta,
                  });
                } else {
                  traceInfo.change.push({
                    0: 0,
                    1: p,
                    2: obj.type === "text" ? obj.value : " ",
                    meta: obj.meta,
                  });
                }
                if (obj.type === "text") {
                  p += obj.value.length;
                } else {
                  p++;
                }
              }
              arr.push(traceInfo);
            }
            cell.children = arr;
          }
          resRawData.meta.traceInfo.push(paraOrTable);
        }
      }
    },

    newVersionCompareTraces(
      rawData1,
      rawData2,
      showPersonInfo = false,
      createDate,
      userId
    ) {
      // 先处理获取所需数据 ↓
      let {
        oldData,
        swapPositionRawData2,
        needTraceInfo,
        userAddStyleList,
        userDeleteStyleList,
      } = this.newVersionGetComparisonData(
        rawData1,
        rawData2,
        showPersonInfo,
        createDate,
        userId
      );
      if (oldData) {
        return oldData;
      }
      // 先处理获取所需数据 ↑

      const basePara = {
        id: "para-95bb6872",
        type: "p",
        align: "left",
        deepNum: 0,
        islist: false,
        isOrder: false,
        indentation: 0,
        dispersed_align: false,
        before_paragraph_spacing: 0,
        restart_list_index: false,
        row_ratio: 1.6,
        page_break: false,
        children: [],
        title_length: 0,
        content_padding_left: 0,
        vertical_align: "top",
        level: 0,
        listNumStyle: "number",
        itemsWidth: [],
      };
      const resRawData = JSON.parse(JSON.stringify(swapPositionRawData2));

      this.newVersionHandleRawData(resRawData);

      while (needTraceInfo.length) {
        resRawData.meta.traceInfo.length = 0;
        const lastTraceInfo = needTraceInfo.pop();

        const deleteUser = userDeleteStyleList.find(
          (user) => user.id === lastTraceInfo.meta.userId
        );
        const addUser = userAddStyleList.find(
          (user) => user.id === lastTraceInfo.meta.userId
        );

        lastTraceInfo.paras.length > 0 &&
          this.newVersionHandleParaLevelCompare(
            lastTraceInfo,
            resRawData,
            addUser,
            userAddStyleList,
            deleteUser,
            userDeleteStyleList,
            basePara
          );

        for (const paraId in lastTraceInfo.traces) {
          const traces = lastTraceInfo.traces[paraId];
          let currentTraceIndex = 0;
          let currentTrace = traces[currentTraceIndex];
          let para = resRawData.content.find((para) => para.id === paraId);

          if (!para) {
            OUT_LOOP: for (let i = 0; i < resRawData.content.length; i++) {
              const paraOrTable = resRawData.content[i];
              if (paraOrTable.type === "table") {
                // 从表格里边找到该段落
                const cells = paraOrTable.cells;
                for (let j = 0; j < cells.length; j++) {
                  const paras = cells[j].children; // 这是段落集合
                  para = paras.find((p) => p.id === paraId);
                  if (para) {
                    break OUT_LOOP;
                  }
                }
              }
            }
          }

          const readPara = JSON.parse(JSON.stringify(para));

          let index = 0; // 段落中每个字的下标
          let paraChildrenIndex;
          if (readPara.children.length === 0) {
            const newObjs = [];
            const restTraces = traces.slice(currentTraceIndex); // TODO currentTraceIndex 不需要 -1 因为后边都是用的 ++currentTraceIndex
            for (const t of restTraces) {
              const text = t["2"];
              const paraChildren = this.newVersionGetParaChildrenByText(
                text,
                lastTraceInfo,
                deleteUser,
                userDeleteStyleList
              );
              newObjs.push(...paraChildren);
            }
            para.children.splice(paraChildrenIndex, 1, ...newObjs);
          }
          for (let i = 0; i < readPara.children.length && currentTrace; i++) {
            let initIndex = index;
            const obj = readPara.children[i];
            const newObjs = [];
            if (obj.continue) {
              if (i === readPara.children.length - 1) {
                const restTraces = traces.slice(currentTraceIndex); // TODO currentTraceIndex 不需要 -1 因为后边都是用的 ++currentTraceIndex
                for (const t of restTraces) {
                  const text = t["2"];
                  const paraChildren = this.newVersionGetParaChildrenByText(
                    text,
                    lastTraceInfo,
                    deleteUser,
                    userDeleteStyleList
                  );
                  newObjs.push(...paraChildren);
                }
                para.children.splice(paraChildrenIndex, 1, ...newObjs);
              }
              paraChildrenIndex++;
              continue;
            }
            let processedIndex = 0;
            let objValueIndex = 0;
            while (currentTrace) {
              // 因为图片 按钮 单选框 复选框 等等 在 rawData 中是一个 obj 都会处理完了 所以 新增的时候 startsWith 是没问题的 应该还是有优化空间的
              // 因为 while 循环 currentTrace 可以每次都修改
              // if (currentTrace["2"].startsWith("$$$image")) {
              //   const picCount = currentTrace["2"].split("$$$image").length;
              //   currentTrace["2"] = "";
              //   for (let n = 0; n < picCount / 2; n++) {
              //     currentTrace["2"] += " ";
              //   }
              // }

              if (currentTrace["1"] === index) {
                if (currentTrace["0"] === -1) {
                  const pre =
                    obj.type === "text"
                      ? {
                          ...obj,
                          value:
                            obj.value.slice(processedIndex, objValueIndex) ||
                            "",
                        }
                      : {
                          type: "text",
                          value: "",
                          continue: true,
                        };
                  !pre.value && (pre.continue = true);
                  const text = currentTrace["2"];

                  pre.value.length > 0 && newObjs.push(pre);
                  const res = this.newVersionGetParaChildrenByText(
                    text,
                    lastTraceInfo,
                    deleteUser,
                    userDeleteStyleList
                  );
                  newObjs.push(...res);

                  processedIndex = objValueIndex;
                  currentTrace = traces[++currentTraceIndex];
                  if (!currentTrace) {
                    // 如果这个段落的痕迹都已经处理过了 那么就可以跳出循环了
                    // 并且还要再补上后边的
                    if (obj.type === "text") {
                      const last = {
                        ...obj,
                        value: obj.value.slice(objValueIndex),
                      };
                      !last.value && (last.continue = true);
                      newObjs.push(last);
                    } else {
                      const last = {
                        ...obj,
                      };
                      newObjs.push(last);
                    }
                    break;
                  }
                } else if (currentTrace["0"] === 1) {
                  if (
                    obj.type !== "text" ||
                    currentTrace["2"].startsWith("$$$")
                  ) {
                    // 这个判断条件：是因为卡尺改了 obj 是卡尺的时候 type 为 text 但是 currentTrace 里边是 $$$widget 所以不能简单的用 obj.type !== "text" 做例子
                    const regex = /\$\$\$[^$]*\$\$\$/g;
                    const matches = currentTrace["2"].match(regex);

                    if (matches && matches.length > 0) {
                      // 获取第一个记录
                      const firstRecord = matches[0];

                      // 移除第一个记录并得到剩下的字符串
                      const remainingText = currentTrace["2"].replace(
                        firstRecord,
                        ""
                      );
                      if (firstRecord.includes("-caliper-")) {
                        newObjs.push({
                          type: "text",
                          value: " ",
                          keep: false, // 已经变成新增了  keep 就不需要保留了
                          font_id:
                            addUser?.font_id || userAddStyleList[0].font_id,
                          continue: true, // 所有新增的内容都不计入下次痕迹对比的下标计算中 就当这儿的对象不存在 意思是过滤的 index 这个 index 的值在循环到当前 obj 的时候不能有任何变化
                          add: true,
                          meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                        });
                      } else {
                        const arr = firstRecord.slice(0, -3).split("-");
                        const selected = arr[arr.length - 1] === "true";
                        newObjs.push({
                          ...obj,
                          selected,
                          keep: false, // 已经变成新增了  keep 就不需要保留了
                          font_id:
                            addUser?.font_id || userAddStyleList[0].font_id,
                          continue: true, // 所有新增的内容都不计入下次痕迹对比的下标计算中 就当这儿的对象不存在 意思是过滤的 index 这个 index 的值在循环到当前 obj 的时候不能有任何变化
                          add: true,
                          meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                        });
                      }
                      let nextTrace;
                      if (remainingText.length > 0) {
                        nextTrace = {
                          ...currentTrace,
                          1: currentTrace["1"] + 1,
                          2: remainingText,
                        };
                      } else {
                        nextTrace = traces[++currentTraceIndex];
                      }
                      // 减一就不用说了因为下边要 ++ radios 是新增了多少个的数组 因为 obj 一个对应一个单选框 但是痕迹里边可能记录了好几个 (挨着的新增删除都记录到一个里边了)
                      // 意思就是说将一个痕迹里边的单选就当做一个字
                      currentTrace = nextTrace;
                    }
                    if (obj.keep) {
                      // 复选框连续 勾选 取消勾选 再勾选 再取消勾选 一直反复下去 就得走这里
                      obj.continue = true;
                      newObjs.push(obj);
                    }
                    index++;
                    break;
                    // 因为一次要处理一个 obj 即便一次痕迹记录了很多个单选框复选框 也被我拆分成了很多次痕迹 所以要打段 走下边的 splice 方法替换掉这个 obj
                  }
                  const pre = {
                    ...obj,
                    value: obj.value.slice(processedIndex, objValueIndex),
                  };
                  !pre.value && (pre.continue = true);
                  const current = {
                    ...obj,
                    keep: false, // 已经变成新增了  keep 就不需要保留了
                    font_id: addUser?.font_id || userAddStyleList[0].font_id,
                    value: currentTrace["2"].slice(
                      // 我最多就处理这么多个 剩下的应该就交给下次处理痕迹
                      0,
                      // obj.value.length - processedIndex // TODO 这里应该怎么计算呢 常锐测出的那个错误 1 和 5 对比的时候 得是  objValueIndex
                      obj.value.length - objValueIndex
                    ),
                    continue: true, // 所有新增的内容都不计入下次痕迹对比的下标计算中 就当这儿的对象不存在 意思是过滤的 index 这个 index 的值在循环到当前 obj 的时候不能有任何变化
                    add: true,
                    meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                  };
                  newObjs.push(pre, current);
                  if (obj.keep) {
                    // 如果需要保留的话 就应该放进去
                    newObjs.push({
                      ...obj,
                      continue: true,
                      keep: false,
                      value: obj.value.slice(
                        objValueIndex,
                        objValueIndex + current.value.length
                      ),
                    });
                  }
                  processedIndex = objValueIndex + current.value.length;
                  let nextTrace;
                  if (currentTrace["2"].length > current.value.length) {
                    nextTrace = {
                      ...currentTrace,
                      1: currentTrace["1"] + current.value.length,
                      2: currentTrace["2"].slice(current.value.length),
                    };
                  } else {
                    nextTrace = traces[++currentTraceIndex];
                  }
                  // 不管是修改还是单纯的新增内容 index 都应该直接过好几个字
                  // 如果是修改的话 会先走删除 走删除的时候会判断是否跟后边痕迹位置一样 决定 index 是否++
                  index += current.value.length - 1; // 因为下边还有 index++ 和 j++ 所以这里减 1
                  objValueIndex += current.value.length - 1;
                  currentTrace = nextTrace;

                  if (!currentTrace) {
                    // 如果这个段落的痕迹都已经处理过了 那么就可以跳出循环了
                    // 并且还要再补上后边的
                    const last = {
                      ...obj,
                      value: obj.value.slice(processedIndex),
                    };
                    !last.value && (last.continue = true);
                    newObjs.push(last);
                    break;
                  }
                }
              }

              if (currentTrace && currentTrace["1"] !== index) {
                // 下一个痕迹的位置跟当前痕迹的位置一样的话就是修改 index 就不能 ++ 否则下一个痕迹永远也对应不上 index 了
                if (obj.type === "text") {
                  if (obj.value.length) {
                    index++;
                    objValueIndex++;
                  }
                } else {
                  index++;
                }
                const length = obj.type === "text" ? obj.value.length : 1;
                if (index > initIndex + length - 1) {
                  // 此时该下一个 obj 了 当前 obj 到头了 但是当前 obj 剩余的内容要继续放上去
                  let last;
                  if (obj.type === "text") {
                    last = {
                      ...obj,
                      value: obj.value.slice(processedIndex),
                    };
                    last.value && newObjs.push(last);
                  } else {
                    newObjs.push({
                      ...obj,
                    });
                  }
                  if (i >= readPara.children.length - 1) {
                    // TODO 什么时候会走这里 还没整明白
                    const text = currentTrace["2"];
                    const res = this.newVersionGetParaChildrenByText(
                      text,
                      lastTraceInfo,
                      deleteUser,
                      userDeleteStyleList
                    );
                    newObjs.push(...res);
                  }
                  break;
                }
              }
            }
            paraChildrenIndex = paraChildrenIndex || i; // 第一次是 i 但是下次就不一定是 i 了 所以处理过一次就要修改 paraChildrenIndex 的值
            if (newObjs.length > 0) {
              para.children.splice(paraChildrenIndex, 1, ...newObjs);
            }

            paraChildrenIndex += newObjs.length || 1; // 至少得增加 1 个吧
          }
        }

        const font_style = {};
        for (let i = 0; i < this.fontStyleList.length; i++) {
          font_style[this.fontStyleList[i].fontId] =
            this.fontStyleList[i].fontStyle;
        }

        resRawData.fontMap = Object.assign({}, resRawData.fontMap, font_style);
        swapPositionRawData2 = JSON.parse(JSON.stringify(resRawData));
      }
      this.newVersionHandleTraceInfo(resRawData, createDate, userId);

      return resRawData;
    },
    getParaDiffData(lastTraceInfo, allChangeParas, contentRes) {
      let allTraceParaId = [];
      lastTraceInfo.forEach((trace) => {
        const allParaId = trace.allParaId;
        if (allParaId && allParaId.length) {
          allParaId.forEach((id) => {
            allTraceParaId.push(id);
          });
        }
      });
      let paraTraceData = [];
      allTraceParaId = Array.from(new Set(allTraceParaId));

      for (let i = 0; i < allChangeParas.length; i++) {
        const changePara = allChangeParas[i];
        const addOrDelInfo = {};
        addOrDelInfo.id = changePara.id;
        if (addOrDelInfo.id.startsWith("para-")) {
          addOrDelInfo.structure = "para";
          addOrDelInfo.change = [];

          // 处理 change 里面的信息
          const changeInfo = {};
          changeInfo[0] = changePara[0] ?? changePara.type;
          let offset = 0;
          if (paraTraceData.length) {
            paraTraceData.forEach((para) => {
              if (para.id === changePara.id) {
                offset += changePara.value.length;
              }
            });
          }
          changeInfo[1] = offset;
          changeInfo[2] = changePara.value;
          changeInfo.meta = changePara.meta;

          // 到这里 change 的信息组装完毕
          addOrDelInfo.change.push(changeInfo);
          paraTraceData.push(addOrDelInfo);
        }
      }
      //把同名的段落改动情况合并到一个段落信息里
      paraTraceData = this.mergeParaChange(paraTraceData);
      if (paraTraceData.length) {
        paraTraceData.forEach((data) => contentRes.push(data));
      }

      //现在拿到了合并后的段落信息和改变之后的表格信息，如果最后一次也变动了表格就把所有改动合并到现在的表格里
      contentRes = this.sortRecordsById(contentRes, allTraceParaId);
      //把组装好的信息返回出去
      return contentRes;
    },
    sortRecordsById(records, order) {
      // 创建一个字典，用于快速查找顺序数组中的位置
      const orderIndexMap = order.reduce((acc, id, index) => {
        acc[id] = index;
        return acc;
      }, {});
      // 对 records 进行排序
      return records.sort((a, b) => {
        // 获取 a 和 b 的 id 在 order 中的位置
        const indexA = orderIndexMap[a.id];
        const indexB = orderIndexMap[b.id];
        // 如果 a 和 b 的 id 都存在于 order 中，按顺序排列
        if (indexA !== undefined && indexB !== undefined) {
          return indexA - indexB;
        }
        // 如果 a 的 id 存在于 order 中而 b 的 id 不存在，a 优先
        if (indexA !== undefined) {
          return -1;
        }
        // 如果 b 的 id 存在于 order 中而 a 的 id 不存在，b 优先
        if (indexB !== undefined) {
          return 1;
        }
        // 如果 a 和 b 的 id 都不存在于 order 中，保持原样
        return 0;
      });
    },
    mergeParaChange(data) {
      if (!data.length) return [];
      const mergedData = {};
      data.forEach((record) => {
        const recordId = record.id;
        if (recordId.startsWith("para-")) {
          if (!mergedData[recordId]) {
            // 如果这个 id 还没有被添加到字典中，初始化一个新的记录
            mergedData[recordId] = {
              id: recordId,
              structure: record.structure,
              change: [],
            };
            // 将当前记录的 change 合并到对应的 id 下
          } else if (recordId.startsWith("table-")) {
            return data;
          }
        }
        mergedData[recordId].change = mergedData[recordId].change.concat(
          record.change
        );
      });
      // 将合并后的字典转换回列表
      return Object.values(mergedData);
    },
    handlePastTracePara(lastTraceInfo) {
      let allChange = []; // 初始化存储所有变化的数组
      let allParaChange = {};
      const traceInfoCopy = lastTraceInfo.slice(0, lastTraceInfo.length - 1); // 创建一个不包含最后一个元素的副本

      traceInfoCopy.forEach((perTraceInfo) => {
        const paras = perTraceInfo.paras; // 获取段落信息
        const meta = perTraceInfo.meta; // 获取元数据

        if (paras.length) {
          for (let i = 0; i < paras.length; i++) {
            const para = paras[i];
            const paraId = para.id;
            if (allParaChange[paraId]) {
              allParaChange[paraId].push({ ...para, meta });
            } else {
              allParaChange[paraId] = [];
              allParaChange[paraId].push({ ...para, meta });
            }
          }
        }
      });
      for (let key in allParaChange) {
        if (allParaChange[key].length === 2) {
          allParaChange[key].forEach((change) => {
            allChange.push(change);
          });
        }
      }

      return allChange; // 返回包含所有变化的数组
    },
    // 获取最后的修改记录 如果整个表格的增加和删除 会把表格里边的段落拿出来平铺 返回
    getLastModificationRecord(rawData) {
      const lastTraceInfo =
        rawData.meta.traceInfo[rawData.meta.traceInfo.length - 1];
      const paras = lastTraceInfo.paras;
      for (let i = 0; i < paras.length; ) {
        const para = paras[i];
        const tableParas = [];
        if (para.cells) {
          for (const cell of para.cells) {
            cell.children.forEach((o) => {
              tableParas.push({ ...o, type: para.type });
            });
          }
          paras.splice(i, 1, ...tableParas);
          i += tableParas.length;
        } else {
          i++;
        }
      }
      const traces = lastTraceInfo.traces;
      return { paras, traces };
    },
    getMeta(traceInfo, id, type, createDate, userId) {
      if (!traceInfo) return null;
      for (const trace of traceInfo) {
        // 如果拿对比后的结果做对比 直接return null 不返回人的信息
        if (trace.change) return null;
      }
      let meta = null;
      for (let t = traceInfo.length - 1; t >= 0; t--) {
        const everyRecord = traceInfo[t];
        if (!everyRecord.paras.length) {
          meta = traceInfo[0].meta;
          if (createDate) {
            meta.date = createDate;
            meta.userId = userId;
          }
        }
        for (let p = everyRecord.paras.length - 1; p >= 0; p--) {
          const para = everyRecord.paras[p];
          if (para.id === id && para.type === type) {
            meta = everyRecord.meta;
            break;
          }
        }
      }
      return meta;
    },
    showCompareTraces(data, rawData2, showPersonInfo, compareType = "content") {
      //获取修改用户信息
      const userInfoList = rawData2?.meta?.userInfo || [];
      //各用户展示颜色，数组后两位，null代表默认背景颜色，red代表删除颜色，其他颜色则为多用户各自颜色

      //用户信息和对应的样式id的数组
      const userAddStyleList = [];
      const userDeleteStyleList = [];

      if (showPersonInfo && userInfoList && userInfoList.length) {
        for (let j = 0; j < userInfoList.length; j++) {
          this.fontStyleList[j % 10].fontStyle.dblUnderLine = true;
          userAddStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id: this.fontStyleList[j % 10].fontId,
          });
          userDeleteStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id:
              this.fontStyleList[(j % 10) + this.bgColorList.length].fontId,
          });
        }
      }
      //需要组合的rawData中的content
      const content = [];
      const header = [];
      //需要组合的rawData.fontMap中的font_style
      const font_style = {};
      //循环获取到的对比数据
      for (let i = 0; i < data.length; i++) {
        const para_traces = data[i];
        if (para_traces.structure === "para") {
          if (compareType === "header") {
            let originPara = rawData2.header.find(
              (p) => p.id === para_traces.id
            );
            if (!originPara) {
              originPara = {
                align: "left",
                before_paragraph_spacing: 0,
                row_ratio: 1.6,
                title_length: 0,
                content_padding_left: 0,
                vertical_align: "top",
              };
            }
            header.push({
              id: para_traces.id,
              type: "p",
              align: originPara.align,
              deepNum: 0,
              islist: false,
              isOrder: false,
              indentation: 0,
              dispersed_align: false,
              before_paragraph_spacing: originPara.before_paragraph_spacing, // 段落前的间距 表示1倍的行高
              row_ratio: originPara.row_ratio, // 行间距
              page_break: false,
              children: [],
              level: 0,
              title_length: originPara.title_length,
              content_padding_left: originPara.content_padding_left,
              vertical_align: originPara.vertical_align,
            });
            //给content的段落children赋值
            this.paraCyclicAssignment(
              header[i],
              para_traces.change,
              userAddStyleList,
              userDeleteStyleList
            );
          } else {
            if (compareType === "header") {
              const originPara = rawData2.header.find(
                (p) => p.id === para_traces.id
              );
              //添加段落样式，children暂时为[]
              header.push({
                id: para_traces.id,
                type: "p",
                align: originPara.align,
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: originPara.before_paragraph_spacing, // 段落前的间距 表示1倍的行高
                row_ratio: originPara.row_ratio, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: originPara.title_length,
                content_padding_left: originPara.content_padding_left,
                vertical_align: originPara.vertical_align,
              });
              //给content的段落children赋值
              this.paraCyclicAssignment(
                header[i],
                para_traces.change,
                userAddStyleList,
                userDeleteStyleList
              );
            } else {
              let originPara = rawData2.content.find(
                (p) => p.id === para_traces.id
              );
              if (!originPara) {
                originPara = {
                  align: "left",
                  before_paragraph_spacing: 0,
                  row_ratio: 1.6,
                  title_length: 0,
                  content_padding_left: 0,
                  vertical_align: "top",
                };
              }
              //添加段落样式，children暂时为[]
              content.push({
                id: para_traces.id,
                type: "p",
                align: originPara.align,
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: originPara.before_paragraph_spacing, // 段落前的间距 表示1倍的行高
                row_ratio: originPara.row_ratio, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: originPara.title_length,
                content_padding_left: originPara.content_padding_left,
                vertical_align: originPara.vertical_align,
              });
              //给content的段落children赋值
              this.paraCyclicAssignment(
                content[i],
                para_traces.change,
                userAddStyleList,
                userDeleteStyleList
              );
            }
          }
        } else if (para_traces.structure === "table") {
          //如果type是表格，则循环data数据，获取data的表格结构，拼接为table
          const table = {
            cells: [],
            col_size: para_traces.col_size,
            id: para_traces.id,
            min_row_size: para_traces.min_row_size,
            name: para_traces.name,
            notAllowDrawLine: para_traces.notAllowDrawLine,
            row_size: para_traces.row_size,
            type: para_traces.structure,
          };
          for (let j = 0; j < para_traces.cells.length; j++) {
            const cell = para_traces.cells[j];
            table.cells[j] = {
              children: [],
              colspan: cell.colspan,
              id: cell.id,
              is_show_slash_down: cell.is_show_slash_down,
              is_show_slash_up: cell.is_show_slash_up,
              padding_bottom: cell.padding_bottom,
              padding_left: cell.padding_left,
              padding_right: cell.padding_right,
              set_cell_height: cell.set_cell_height,
              padding_top: cell.padding_top,
              pos: cell.pos,
              rowspan: cell.rowspan,
              split_parts: cell.split_parts,
              vertical_align: cell.vertical_align,
            };
            for (let k = 0; k < cell.children.length; k++) {
              const p = cell.children[k];
              table.cells[j].children[k] = {
                id: p.id,
                type: "p",
                align: p.align,
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: p.before_paragraph_spacing, // 段落前的间距 表示1倍的行高
                row_ratio: p.row_ratio, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: p.title_length,
                content_padding_left: p.content_padding_left,
                vertical_align: p.vertical_align,
              };
              this.paraCyclicAssignment(
                table.cells[j].children[k],
                p.change,
                userAddStyleList,
                userDeleteStyleList
              );
            }
          }
          if (compareType === "header") {
            header.push(table);
          } else {
            content.push(table);
          }
        }
      }
      //给fontMap赋值样式
      for (let i = 0; i < this.fontStyleList.length; i++) {
        font_style[this.fontStyleList[i].fontId] =
          this.fontStyleList[i].fontStyle;
      }
      const rawData = JSON.parse(JSON.stringify(rawData2));
      if (compareType === "header") {
        rawData.header = header;
      } else {
        rawData.content = content;
      }
      rawData.fontMap = Object.assign({}, rawData.fontMap, font_style);
      if (rawData?.meta?.traceInfo) {
        rawData.meta.traceInfo = [...data];
      } else {
        rawData["meta"] = {
          traceInfo: [],
        };
      }
      rawData.groups = [];
      return rawData;
    },
    //段落内容循环赋值的方法
    paraCyclicAssignment(para, data, userAddStyleList, userDeleteStyleList) {
      //循环给p.children赋值
      for (let n = 0; n < data.length; n++) {
        const e = data[n];
        let val = e[2] ? e[2].replace(/\n/g, "") : "";
        const splitValArr = val.split("$$$");

        for (let i = 0; i < splitValArr.length; i++) {
          val = splitValArr[i];
          const splitVal = val.split("-");
          let booleanValue;
          if (splitVal.length && splitVal[0] === "widget") {
            // 重新拼接成字符串
            if (splitVal[splitVal.length - 1] === "true") {
              booleanValue = true;
            } else if (splitVal[splitVal.length - 1] === "false") {
              booleanValue = false;
            }
          }
          let image;
          if (val.startsWith("image-")) {
            image = this.traceImageMap[val];
            if (!image) val = "";
          }
          const caliper = this.traceCaliperMap[val];
          if (caliper) {
            caliper.selected = booleanValue;
            let font_id = "";
            if (e[0] === -1) {
              if (userDeleteStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userDeleteStyleList.length; j++) {
                  if (e.meta.userId === userDeleteStyleList[j].id) {
                    font_id = userDeleteStyleList[j].font_id;
                  }
                }
              }
            } else if (e[0] === 1) {
              if (userAddStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userAddStyleList.length; j++) {
                  if (e.meta.userId === userAddStyleList[j].id) {
                    font_id = userAddStyleList[j].font_id;
                  }
                }
              }
            } else {
              font_id = caliper.font_id;
            }
            caliper.font_id = font_id;
            para.children.push(caliper);
            continue;
          } else if (image) {
            let font_id = "";
            if (e[0] === -1) {
              if (userDeleteStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userDeleteStyleList.length; j++) {
                  if (e.meta.userId === userDeleteStyleList[j].id) {
                    font_id = userDeleteStyleList[j].font_id;
                  }
                }
              }
            } else if (e[0] === 1) {
              if (userAddStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userAddStyleList.length; j++) {
                  if (e.meta.userId === userAddStyleList[j].id) {
                    font_id = userAddStyleList[j].font_id;
                  }
                }
              }
            } else {
              font_id = image.font_id;
            }
            image.font_id = font_id;
            para.children.push(image);
            continue;
          }
          if (e[0] === 0) {
            //如果对比没有变化，则font_id赋予没改变的样式
            para.children.push({
              type: "text",
              value: val,
              font_id: this.fontStyleList[this.fontStyleList.length - 1].fontId,
            });
          } else if (e[0] === 1) {
            //如果新增则赋予新增样式
            if (userAddStyleList.length > 0) {
              let font_id = "";
              //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
              for (let j = 0; j < userAddStyleList.length; j++) {
                if (e.meta.userId === userAddStyleList[j].id) {
                  font_id = userAddStyleList[j].font_id;
                }
              }
              para.children.push({
                type: "text",
                value: val,
                font_id: font_id,
              });
            } else {
              para.children.push({
                type: "text",
                value: val,
                font_id: this.fontStyleList[0].fontId,
              });
            }
          } else if (e[0] === -1) {
            //如果新增则赋予新增样式
            if (userDeleteStyleList.length > 0) {
              let font_id = "";
              //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
              for (let j = 0; j < userDeleteStyleList.length; j++) {
                if (e.meta.userId === userDeleteStyleList[j].id) {
                  font_id = userDeleteStyleList[j].font_id;
                }
              }
              para.children.push({
                type: "text",
                value: val,
                font_id: font_id,
              });
            } else {
              para.children.push({
                type: "text",
                value: val,
                font_id:
                  this.fontStyleList[(this.fontStyleList.length - 1) / 2]
                    .fontId,
              });
            }
          }
        }
      }
    },
  },
};
export default traceContrast;

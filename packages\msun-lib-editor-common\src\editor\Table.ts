import Cell from "./Cell";
import Renderer from "./Renderer";
import Editor from "./Editor";
import Paragraph from "./Paragraph";
import { getLCM, versionDiff } from "./Utils";
import {
  deleteTwoDimArrElement,
  insertTblInsLinebreak,
  getCellsOnRowAndCol,
  getCellIndex,
  someCells2Table,
  isCell,
  isInViewport,
} from "./Helper";
import {
  arrDeduplication,
  deepClone,
  getRawDataByConfig,
  initCell,
  isPage,
  isParagraph,
  isRow,
  isTable,
  sort,
  uuid ,
} from "./Utils";
import Character from "./Character";
import Group from "./Groups";
import { Config } from "./Config";
import { InsertRowCarryDataType } from "./EditorConfig";
import Row from "./Row";
import PathUtils, { Path } from "./Path";
import XField from "./XField";
import Page from "./Page";
import { Direction, PacsLayoutParameter, RowLineType, SourceOfAddRowOrCol } from "./Definition";
// import EditorLocalTest from "../../localtest";
import EditorHelper from "./EditorHelper";
import { DEFINITION, ShapeMode, ViewMode, SkipMode } from "./Constant";
import Image from "./Image";
import Caret from "./Caret";

export enum BreakType {
  soft,
  hard,
}

export interface NotAllowDrawLineType {
  row: number[][]; // 这是不让画的线
  col: number[][];
  changeOpacityRow: number[][]; // 这是修改透明度的线
  changeOpacityCol: number[][]; // 这是修改透明度的线
}

export default class Table {
  id: string;

  fixed_table_header_num: number = 0; // 固定表头行的数量

  rowLineType: RowLineType = RowLineType.VOID;

  name: string | null = null;

  group_id: string | null;

  notAllowDrawLine: NotAllowDrawLineType = {
    // 保存不让画的线
    row: [], // 这是不让画的线
    col: [],
    changeOpacityRow: [], // 这是修改透明度的线
    changeOpacityCol: [], // 这是修改透明度的线
  };

  isCanNotDelTbl: boolean = false; // 是否 不能删除 该表格 为true 就不能删 默认可以删除

  left: number;

  left_col: number = 0; // 最靠近拖动线的左侧单元格的起始列,整个表格最左侧为0

  right_col: number = 0; // 紧挨着拖动线的右侧单元格的末尾列

  top: number;

  row_size: number[];

  min_row_size: number[];

  col_size: number[];

  children: Cell[] = [];

  parent: Cell;

  x: number = 0; // 偏移坐标

  y: number = 0; // 偏移坐标

  padding_left: number = 0;

  padding_top: number = 0;

  padding_right: number = 0;

  padding_bottom: number = 0;

  cell_index: number = 0;

  page_number: number = 0; // 从 1 开始，0代表未赋初始值

  newPage: boolean = false; // 跟分组分页一样 分页用的

  page_index: number = -1;

  para_index: number = 1;

  page_break_line: number = 0;

  page_break: boolean = false; // 表格是否拆分

  page_break_type: BreakType = BreakType.soft;

  origin: Table | null = null;

  split_parts: Table[] = [];

  split_line_arr: number[] = []; // 保存了每条分割线  在表格内距离第一页表格顶部边界的距离(就是说不包括页眉页脚和每页边之间的距离)

  resize_horizontal_line: number = 0; // 水平调整线距离表格顶部的距离(如果是分页表格，就是距离当前被分页表格的顶部距离)

  resize_vertical_line: number = 0;

  editableInFormMode: boolean = false;

  tableFiexedStyle: boolean = false;

  imageList: unknown;

  exist: undefined | boolean;

  cellMap: Map<String, Cell> = new Map<String, Cell>();

  skipMode: SkipMode = SkipMode.ROW;

  // TODO 遗留的小问题是：如果表格没有顶着页眉 在表格上方回车 表格就会分页 而下一页又不能输入任何内容
  fullPage: boolean = false; // 撑满一整页

  editor: Editor;

  get start_path(): Path {
    return [this.cell_index, 0, 0, 0];
  }

  get group(): Group | undefined {
    let group;
    if (this.group_id) {
      group = this.editor.current_cell.getGroupById(this.group_id);
    }
    return group;
  }

  get end_path(): Path {
    const cell_index = this.children.length - 1;
    const cell = this.children[cell_index];
    const row_index = cell.children.length - 1;
    const row = cell.children[row_index];
    const char_index = row.children.length;
    const model_path: Path = [
      this.cell_index,
      cell_index,
      row_index,
      char_index,
    ];
    return model_path;
  }

  static  createTableLayout(parameter: {editor: Editor, numberOfCellsPerRow: number[]}): Table | undefined {
    const { editor, numberOfCellsPerRow } = parameter;
    // TODO 统一的 min_col_size 要做处理 不能用统一的 排版的表格就要为 0
    const table = editor.createTable(
      numberOfCellsPerRow.length,
      getLCM(numberOfCellsPerRow),
      { isImageTable: true }
    );
    if (!table) return;

    // 此时还不知道 应该怎么分配 col_size 得到知道内容的时候根据内容宽度和内容间距才知道怎么分配 col_size 理论上应该是两边无限大 中间无限小
    numberOfCellsPerRow.forEach((retainedCellsNumber, index) => {
      table.mergeSingleRowCells(index, retainedCellsNumber);
    });
    return table;

  }
  
  static judgeIsCanDeleteRowOrCol(editor: Editor): boolean {
    const model_path = editor.selection.anchor;
    if (model_path.length < 3) {
      return false;
    }
    const out_cell_children = editor.current_cell.children;
    const table = out_cell_children[model_path[0]] as Table;
    if (table.isCanNotDelTbl) {
      // 如果表格都不能删除 那么也不让删除行
      return false;
    }
    return true;
  }

  // static judgeIsCanDeleteCol (editor: Editor): boolean {
  //   // 判断能否删除整列 返回true能删除 否则不能删除
  //   const cursor_path = editor.selection.anchor;
  //   if (cursor_path.length < 3) {
  //     return false;
  //   }
  //   if (!editor.selection.isCollapsed) {
  //     // 选区情况下 不允许删除 TODO zc后期可添加选区为整列时，再删除整列的判断
  //     return false;
  //   }
  //   const out_cell_children = editor.current_cell.children;
  //   const table = out_cell_children[cursor_path[0]] as Table;

  //   if (table.isCanNotDelTbl) {
  //     // 如果表格不能删除 那么也不让删除列
  //     return false;
  //   }

  //   const cells = table.children as Cell[];

  //   const current_cell = cells[cursor_path[1]] as Cell;

  //   const init_start_col_index = current_cell.start_col_index; // 初始 起始列

  //   const init_end_col_index = current_cell.end_col_index; // 初始 末尾列

  //   // const col_span = current_cell.colspan; // col_span的数量 就是要删除掉的col_size里边的元素数量

  //   // 得找到要出删除的这一列 最大的colspan数，然后右侧的单元格位置 要左移最大的colspan数
  //   const max_colspan_arr: number[] = [];

  //   // TODO 如果不是整齐的一列或者多列 不让删除,下个版本可添加拆分后删除逻辑
  //   let rowspan_nums = 0;
  //   for (let i = 0, len = cells.length; i < len; i++) {
  //     const cell = cells[i];
  //     const start_col_index = cell.start_col_index;
  //     const end_col_index = cell.end_col_index;
  //     if (
  //       start_col_index === init_start_col_index &&
  //       end_col_index === init_end_col_index
  //     ) {
  //       max_colspan_arr.push(cell.colspan);
  //       rowspan_nums += cell.rowspan;
  //     }
  //   }
  //   if (rowspan_nums !== table.row_size.length) {
  //     return false;
  //   }
  //   return true;
  // }

  static judgeIsCanInsertCol(editor: Editor) {
    const page_size_width = editor.config.getPageSize().width;
    const path = editor.selection.anchor;
    if (path.length < 3) {
      return false;
    }
    const table = editor.current_cell.children[path[0]] as Table;
    const max_cols = table.col_size.length; // 该表格 现在有多少列

    const tbl_width =
      page_size_width -
      editor.config.page_padding_left -
      editor.config.page_padding_right;

    const new_col_size = tbl_width / (max_cols + 1); // 加上新单元格后 每个单元格的平均宽度(不是每个单元格的实际宽度) 也就是新插入列的宽度

    // 新插入的列 宽度都不到配置的最小宽度了 就不允许插入了
    if (new_col_size < Config.min_col_size) {
      return false;
    }
    return true;
  }

  static judgeIsCanDeleteBlankRow(direction: Direction, editor: Editor) {
    const model_path = editor.selection.anchor;
    const para_start = editor.selection.para_start;
    const table = editor.current_cell.children[model_path[0]];
    if (isTable(table)) {
      const rows = editor.current_cell.children;
      const paragraphs = editor.current_cell.paragraph;
      let delete_element: any;
      if (direction === Direction.up) {
        if ((rows[model_path[0] - 1] as Row)?.children?.length !== 0) {
          // 加?.是因为 可能啥也没有 啥也没有 就不会等于0 就return fasle没问题
          return false;
        }
        // 走到这儿 表格上方 就必然是空行了
        if (isTable(rows[model_path[0] - 2])) {
          return false;
        }
        delete_element = paragraphs[para_start[0] - 1];

        const delete_element_prev = paragraphs[para_start[0] - 2];
        if (
          delete_element.group_id &&
          delete_element.group_id !== paragraphs[para_start[0]].group_id
        ) {
          // 上方空行在其他分组内 需要判断能不能删除(分组内只有这一个空行的时候) 否则不需要判断
          if (!delete_element_prev) {
            // 空行上边没有东西了 不能删除
            return false;
          }
          if (!delete_element_prev.group_id) {
            // 空行上方不在分组内 并且空行也不在当前分组内 不能删除
            return false;
          }
          if (
            delete_element_prev.group_id &&
            delete_element_prev.group_id !== delete_element.group_id
          ) {
            // 空行上方也在分组内 且不是同一个分组
            return false;
          }
        }
        return true;
      } else if (direction === Direction.down) {
        if ((rows[model_path[0] + 1] as Row)?.children?.length !== 0) {
          return false;
        }
        // 走到这儿 表格下方 必然就是空行了
        if (isTable(rows[model_path[0] + 2])) {
          return false;
        }
        delete_element = paragraphs[para_start[0] + 1];
        const delete_element_next = paragraphs[para_start[0] + 2];
        if (
          delete_element.group_id &&
          delete_element.group_id !== paragraphs[para_start[0]].group_id
        ) {
          // 下方空行在其他分组内 需要判断能不能删除(分组内只有这一个空行的时候) 否则不需要判断
          if (!delete_element_next) {
            // 空行下边没有东西了 不能删除
            return false;
          }
          if (!delete_element_next.group_id) {
            // 空行下方不在分组内 并且空行也不在当前分组内 不能删除
            return false;
          }
          if (
            delete_element_next.group_id &&
            delete_element_next.group_id !== delete_element.group_id
          ) {
            // 空行下方也在分组内 且不是同一个分组
            return false;
          }
        }
        return true;
      }
    }
    return false;
  }

  static judgeFocusCellIsCanSplit(editor: Editor) {
    const selectedCells = editor.selection.selected_cells;
    if (selectedCells.length > 1) {
      return false;
    }
    const cursorPath = editor.selection.focus; // 光标所对应的模型数据路径
    const rootTable = editor.current_cell.children[cursorPath[0]]; // 找到第一层数据 Table
    if (isTable(rootTable)) {
      const currentCell = rootTable.children[cursorPath[1]]; // 找到光标所在的单元格
      // 既没有合并行 也没有合并列的单元格 不允许拆分
      if (currentCell.rowspan <= 1 && currentCell.colspan <= 1) {
        return false;
      }
      return true;
    }
    return false;
  }

  /**
   * 判断选中的单元格是否可以合并
   * @param editor Editor
   * @returns 是否可以合并
   */
  static canMergeCells(editor: Editor, selectedCells?: any) {
    let selected_cells;
    if (!selectedCells) {
      // 分组如果锁定 则不能编辑
      if (!editor.operableOrNot(["cell", "group"])) return false;
      selected_cells = editor.selection.selected_cells;
    } else {
      selected_cells = selectedCells;
    }

    if (selected_cells.length < 2) return false; // 选区只有一个单元格 不能合并

    const split_cells = []; // 保存所有被拆分的小单元格

    // 2. 将每个单元格都进行拆分
    for (const { cell } of selected_cells) {
      // 注释掉 因为 pacs 调用 mergeCell 会有不能合并的情况 再遇到不能合并的就单独处理那个数据
      // if (cell.split_parts.length > 0) {
      //   // 有分页也不让合并
      //   return false;
      // }
      const splited_cell_arr = cell.splitCell(cell.parent as Table, true);
      split_cells.push(...splited_cell_arr);
    }

    // 新思路 ↓
    // 找到最小起始行最小起始列 和 最大起末尾行最大末尾列 并且从表格的row_size和col_size中截取对象的行和列数
    // 行和列的双层循环就能循环所有单元格的位置，如果存在没有的情况 马上return出去 就不能合并 否则 循环走完 就说明能合并
    // 新思路 ↑

    // 3. 将每一行的单元格 和 每一列的单元格 都分别存储到一个单独的对象中
    const { rows_cells_obj } = getCellsOnRowAndCol(split_cells);

    // 4. 循环每一行的对象 每一行单元格数量一致 才能合并 否则不能合并
    const initRowCellsNum: number = (Object as any).values(rows_cells_obj)[0]
      .length; // 初始化一个变量 保存
    for (const rowNum in rows_cells_obj) {
      const current_row_cells = rows_cells_obj[rowNum];
      if (current_row_cells.length !== initRowCellsNum) {
        return false;
      }
      // 每一行上 行号不连续的 也不能合并 (中间纵向合并单元格，在中间横向选择，可能就会出现行号不连续)
      let compare_cell = current_row_cells[0];
      for (let i = 1; i < current_row_cells.length; i++) {
        const now_cell = current_row_cells[i];
        if (now_cell.start_col_index !== compare_cell.end_col_index + 1) {
          console.log("出现了行号不连续的情况");
          return false;
        }
        compare_cell = now_cell;
      }
    }
    // 还会有每一行上单元格数量一样，但不规则的情况，但是现在选区好像选不了 暂时忽略
    return true;
  }

  /**
   * 判断在表单模式下是否可编辑
   */
  static judgeIsEditableInFormMode(path: Path, editor: Editor) {
    const rowOrTable = editor.current_cell.children[path[0]];
    if (isTable(rowOrTable)) {
      return rowOrTable.editableInFormMode;
    }
  }

  /**
   * 设置表格在表单模式下是否可编辑
   * @param tableName 表格的 name 值
   * @param editable 是否可编辑
   * @param editor Editor
   */
  static setEditableInFormMode(
    editable: boolean,
    tableName: string,
    editor: Editor
  ) {
    let tables: Table[] = [];
    if (tableName) {
      tables = editor.getTablesByName(tableName);
    } else {
      const table = this.getFocusTable(editor);
      table && (tables = [table]);
    }
    tables.forEach((table) => {
      table.editableInFormMode = editable;
    });
    editor.render();
  }

  // TODO 也没大明白 这个判断是干啥的
  static splitCellJudge(
    cell_: Cell,
    start_row_index: number,
    end_row_index: number,
    start_col_index: number,
    end_col_index: number
  ) {
    const cells = cell_.splitCell(cell_.parent as Table, true);
    cells.forEach((cell) => {
      if (
        cell.position[0] >= start_row_index &&
        cell.position[0] <= end_row_index &&
        ((cell.position[1] >= start_col_index &&
          cell.position[1] <= end_col_index) ||
          (cell.position[1] >= end_col_index &&
            cell.position[1] <= start_col_index))
      ) {
        return true;
      }
    });
    return false;
  }

  static deleteBlankRow(direction: Direction, editor: Editor) {
    let model_path = [...editor.selection.anchor];
    let para_start = [...editor.selection.para_start];
    const paragraphs = editor.current_cell.paragraph;

    const para = paragraphs[para_start[0]];
    if (isParagraph(para) && para.isEmpty) {
      // 如果当前元素不是表格 并且当前段时空段
      const preTable = paragraphs[para_start[0] - 1];
      const nextTable = paragraphs[para_start[0] + 1];
      if (isTable(preTable) && isTable(nextTable)) return // 前后都是表格 肯定不能删了
      if (isTable(preTable)) {
        editor.selection.setCursorPosition([model_path[0] - 1, 0, 0, 0]);
        model_path = [model_path[0] - 1, 0, 0, 0]
        para_start = [para_start[0] - 1, 0, 0, 0]
        direction = Direction.down
      }
      if (isTable(nextTable)) {
        editor.selection.setCursorPosition([model_path[0] + 1, 0, 0, 0]);
        model_path = [model_path[0] + 1, 0, 0, 0]
        para_start = [para_start[0] + 1, 0, 0, 0]
        direction = Direction.up
      }
    }

    let delete_element: any;
    if (direction === Direction.up) {
      if (!editor.judgeCanDelEmptyParaOnTblSide(Direction.up)) {
        return;
      }
      delete_element = paragraphs[para_start[0] - 1];
    } else if (direction === Direction.down) {
      if (!editor.judgeCanDelEmptyParaOnTblSide(Direction.down)) {
        return;
      }
      delete_element = paragraphs[para_start[0] + 1];
    }
    // TODO 这里如果是能走到 delete_element必定是undefined和paragraph
    if (isParagraph(delete_element)) {
      delete_element.remove();
      if (direction === Direction.up) {
        editor.selection.setCursorPosition([
          model_path[0] - 1,
          model_path[1],
          model_path[2],
          model_path[3],
        ]);
      }
    }
    // TODO 删除上方空行，上方有元素？上方段落最后行开头:当前表格
    // TODO 删除下方空行，下方有元素？下方开头:当前表格
    editor.update();
    editor.render();
    return true;
  }

  static deleteRow(editor: Editor) {
    // 分组如果锁定 则不能编辑
    if (editor.selection.getFocusGroup()?.lock) return false;
    const isCanDeleteRow = Table.judgeIsCanDeleteRowOrCol(editor);
    if (!isCanDeleteRow) {
      return false;
    }

    const out_cell_children = editor.current_cell.children;
    const model_path = editor.selection.start;
    const table = out_cell_children[model_path[0]] as Table;
    table.deleteRowByCursor(model_path, editor);
    // TODO 更新的参数 单独计算
    editor.update(); // ...editor.getUpdateParamsByContainer(table)
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static deleteCol(editor: Editor) {
    // 分组如果锁定 则不能编辑
    if (editor.selection.getFocusGroup()?.lock) return false;
    const cursor_path = editor.selection.anchor;
    // 判断能不能删除列
    const isCanDeleteCol = Table.judgeIsCanDeleteRowOrCol(editor);
    if (!isCanDeleteCol) {
      return false;
    }

    const out_cell_children = editor.current_cell.children;
    const table = out_cell_children[cursor_path[0]] as Table;
    const model_path = editor.selection.start;

    table.deleteColByCursor(model_path, editor);

    // TODO 单独计算参数
    editor.update(); // 加参数 多页 删除列时 错乱 ...editor.getUpdateParamsByContainer(table)
    editor.scroll_by_focus(); // TODO 不需要
    editor.render();
    return true;
  }

  // 添加行或者列
  static insertRowOrCol(editor: Editor, direction: Direction) {
    if (editor.selection.getFocusGroup()?.lock) return false; // 分组如果锁定 则不能编辑
    const cursor_path = editor.selection.start; // 光标路径
    if (cursor_path.length < 4) {
      // 光标没在表格内 直接return掉
      return false;
    }

    const selection = editor.selection;
    if (
      !selection.isCollapsed &&
      !selection.isOneTable &&
      !selection.isOnlyCell
    ) {
      // 选区情况 并且 不是完整的表格 也不是若干个表格
      return false;
    }

    const current_table = editor.current_cell.children[cursor_path[0]] as Table; // 当前光标所在的表格
    current_table.addRowOrCol(cursor_path, direction, editor);

    editor.update(...editor.getUpdateParamsByContainer(current_table)); // 加参数会导致插入的时候，表格消失(或者错位) 现在好像好了 ？？？
    editor.render();
    return true;
  }

  static appendRowAndColByName(
    name: string,
    rowNum: number,
    colNum: number,
    editor: Editor
  ) {
    const tables = editor.getTablesByName(name);
    for (const table of tables) {
      rowNum && table.addRows({ row_num: rowNum });
      colNum && table.addCols({ col_num: colNum, editor });
      table.sortingCells(); // 不排序，移动光标的时候会显示不对
    }
    editor.update();
    editor.render();
  }

  // 创建原生表格对象
  static generateRawTable(
    row_num: number,
    col_num: number,
    height: number,
    editor: Editor
  ) {
    // 初始的表格 原生对象
    const init_table_raw: any = {
      type: "table",
      id: uuid("table"),
      col_size: [],
      row_size: [],
      min_row_size: Array(row_num).fill(Config.min_row_size),
      cells: [],
    };
    // 创建原生talbe对象 ↓
    const total_cells_num = col_num * row_num; // 共需生成这么多的单元格
    let cell_position_row = 0; // 单元格的位置 初始 第0行
    let cell_position_col = 0; // 单元格的位置 初始 第0列

    const total_tbl_width =
      editor.page_size.width -
      editor.config.page_padding_left -
      editor.config.page_padding_right;

    const col_size = total_tbl_width / col_num; // 每个单元格的宽度
    init_table_raw.col_size = Array(col_num).fill(col_size);

    const row_size = Config.min_row_size;
    init_table_raw.row_size = Array(row_num).fill(row_size);

    for (let i = 0; i < total_cells_num; i++) {
      const obj = {
        pos: [cell_position_row, cell_position_col],
        colspan: 1,
        rowspan: 1,
        children: [
          {
            type: "p",
            align: "left",
            isList: false, // 判断是否为list
            children: [
              {
                type: "text",
                style: {
                  height: height,
                  family: "仿宋",
                },
                value: "",
              },
            ],
          },
        ],
      };
      cell_position_col++; // 自定义 按照 每行从左到右的顺序生成单元格 所以没生成一个单元格 列的位置就要加1
      if (cell_position_col >= col_num) {
        // 如果加1以后列的位置 等于要生成表格总列数 说明是该换行了 行加1列重置为0
        cell_position_col = 0;
        cell_position_row++;
      }
      init_table_raw.cells.push(obj);
    }
    return init_table_raw;
    // 创建原生table对象 ↑
  }

  /**
   * 在表格上边或者下边插入空行
   * @param direction
   * @param editor Editor
   * @returns
   */
  static insertBlankRow(direction: Direction, editor: Editor) {
    // 分组锁定 不能插入空行
    if (editor.selection.getFocusGroup()?.lock) return false;

    const model_path = editor.selection.anchor;
    const table = editor.current_cell.children[model_path[0]];
    if (isTable(table)) {
      const para_start = editor.selection.para_start;
      const paragraphs = editor.current_cell.paragraph;

      const para_id = uuid("para");
      const group_id = table.group_id;
      const new_paragraph = new Paragraph(
        para_id,
        editor.current_cell,
        group_id
      );
      // 新new出来的段落为空的时候，需要添加一个默认的换行符
      const font = editor.fontMap.add(editor.config.default_font_style);
      const new_character = new Character(font, "\n");
      new_paragraph.characters.push(new_character);
      if (direction === Direction.up) {
        paragraphs.splice(para_start[0], 0, new_paragraph);
        editor.current_cell.updateParaIndex();
        model_path[0] = table.cell_index + 1;
        editor.selection.setCursorPosition([...model_path]);
        new_paragraph.updateChildren(table.cell_index, 0);
      } else if (direction === Direction.down) {
        paragraphs.splice(para_start[0] + 1, 0, new_paragraph);
        editor.current_cell.updateParaIndex();
        model_path[0] = table.cell_index;
        new_paragraph.updateChildren(table.cell_index + 1, 0);
      }
      group_id &&
        editor.current_cell.getGroupById(group_id)?.refreshContentParaId();
      // TODO 参数
      editor.update();
      editor.render();
      return true;
    }
  }

  static insertTable(
    row_num: number,
    col_num: number,
    editor: Editor,
    params?: {
      isImageTable?: boolean;
      newPage?: boolean;
      name?: string;
      skipMode?: SkipMode;
      opacityOfLines?: number // 0 是完全透明 1 是不透明
    }
  ) {
    // 文本域内 不能插入表格
    if (editor.selection.getFocusField()) {
      editor.event.emit("message", {
        type: "warning",
        msg: "文本域内不允许插入表格！",
      });
      return false;
    }

    // 分组锁定 不能插入表格
    if (editor.selection.getFocusGroup()?.lock) return false;

    // 光标在表格内 不能插入表格
    const cursor_path = editor.selection.start;
    if (PathUtils.isTablePath(cursor_path)) {
      return false;
    }

    // 删除没成功 不能插入表格
    if (!editor.selection.isCollapsed) {
      const del_result = editor.delete_backward();
      if (!del_result) {
        return false;
      }
    }

    // 创建表格没有成功  不能插入表格
    const table = editor.createTable(row_num, col_num, params);
    if (!table) return false;

    if (params && params.opacityOfLines === 0) {
      for (let i = 0; i < row_num; i++) {
        for (let j = 0; j < col_num; j++) {
          // 增加透明的横线
          table.notAllowDrawLine.changeOpacityRow.push([i, j])
          if (i === row_num - 1) {
            table.notAllowDrawLine.changeOpacityRow.push([i + 1, j])
          }
          // 增加透明度的竖线
          table.notAllowDrawLine.changeOpacityCol.push([j, i]);
          if (j === col_num - 1) {
            table.notAllowDrawLine.changeOpacityCol.push([j + 1, i]);
          }
        }
      }
    }
    table.insert();
    return table;
  }

  insert() {
    const out_cell = this.editor.current_cell; // 缓存 避免重复访问
    const currentPara = this.editor.selection.getFocusParagraph(); // 必须在初始的时候获取当前row
    const row = currentPara.children[0];
    const current_row_cell_index = row.cell_index; // 这三个在这儿先存好 避免可能出现的值改变
    const current_row_page_number = row.page_number;
    const current_row_page_index = row.page_index;

    const sel = this.editor.selection;
    let para_path = sel.para_anchor;
    const para_and_tbl_arr = out_cell.paragraph;

    const current_para = para_and_tbl_arr[para_path[0]] as Paragraph; // 光标所在的位置 只能是paragraph
    let index: number = current_para.para_index;
    const isLast = index === out_cell.paragraph.length - 1;

    // 验证是否需要插入换行符
    if (
      insertTblInsLinebreak(para_path, this.editor.selection) ||
      (!PathUtils.isStartPathInPara(para_path) &&
        PathUtils.isEndPathInPara(para_path, current_para))
    ) {
      // 临时注释 改为 index++ 为了解决 pacs 文本域在文档的最后一行 将光标置到文本域最后 插入图片版本 图片排版跑到文本域上方去的问题
      // // 补丁 如果在最后一行 并且上边不是表格的时候就不 ++ 了
      // // 如果表格下方只有两个空段 在最后一个空段插入表格 就不换位置了 wps 也是这样 只不过 wps 没有多插入一个空行
      // if (!(isLast && !isTable(out_cell.paragraph[index - 1]))) {
      //   index++;
      // }
      
      index++
    }
    const group_id = this.editor.selection.getFocusGroup()?.id || null;
    this!.group_id = group_id;
    out_cell.insertTableByParagraphIndex(this, index);
    para_path = [index, 0, 0, 0]; // 因为又插入了一个表格 所以index 还要++
    sel.setCursorPosition(this.editor.paraPath2ModelPath(para_path));
    this.editor.update(
      current_row_cell_index,
      current_row_page_number,
      current_row_page_index
    );
    this.editor.scroll_by_focus();
    this.editor.render(); // 虽然focus里边有render 这里也得调用
    this.editor.focus();
  }

  // 删除表格
  static deleteTableWithCaret(editor: Editor) {
    if (editor.selection.getFocusGroup()?.lock) return false; // 分组如果锁定 则不能编辑

    const cursor_path = editor.selection.start[0];
    const root_cell_children = editor.current_cell.children;
    const tblOrRow = root_cell_children[cursor_path];

    if (isTable(tblOrRow) && !tblOrRow.isCanNotDelTbl) {
      const prev_cursor = cursor_path - 1;
      const next_cursor = cursor_path + 1;
      const prev = root_cell_children[prev_cursor] as Row;
      const next = root_cell_children[next_cursor] as Row;

      // const current_cell_index = tblOrRow.cell_index;
      // const current_page_index = tblOrRow.page_index;
      // const current_page_number = tblOrRow.page_number;
      const current_group = editor.selection.getFocusGroup();
      tblOrRow.remove({ current_group });

      const selection = editor.selection;
      if (!prev && !next) {
        selection.setCursorPosition([0, 0]);
      }
      if (prev && !next) {
        // 如果删除的表格前边有东西 并且 后边没有东西
        if (root_cell_children[prev_cursor + 1]) {
          selection.setCursorPosition([prev_cursor + 1, 0]);
        } else {
          selection.setCursorPosition([prev_cursor, prev.children.length]);
        }
      }

      // TODO 上两个改用elseif，下面两个判断好像可以是同一个else
      if (!prev && next) {
        // 如果删除的表格 前边没有东西 后边有东西
        selection.setCursorPosition([cursor_path, 0]);
      }

      if (prev && next) {
        // 如果删除的表格 前后都有东西
        selection.setCursorPosition([cursor_path, 0]);
      }
      // editor.selection.setCursorPosition([cursor_path, 0]); // 这是在假设 表格下方 永远都有东西
      for (let i = cursor_path; i < root_cell_children.length; i++) {
        root_cell_children[i].cell_index = i;
        // TODO zc editor.current_cell.updateRowBounding (这里只需要更新cell_index，可以不需要调这个方法)
      }
      // TODO 上面各种情况里可以计算对应的值
      editor.update(); // current_cell_index, current_page_number, current_page_index   多页 删除表格的时候 参数有问题
      editor.scroll_by_focus();
      editor.render();
      return true;
    }
    return false;
  }

  // 设置是否可以删除
  static setIsCanBeDeleted(canNot: boolean, editor: Editor) {
    const table = this.getFocusTable(editor);
    table && (table.isCanNotDelTbl = canNot);
  }

  /**
   * 固定表头
   * @returns
   */
  static fixedHeader({
    name,
    fixedHeaderNum,
    editor,
  }: {
    name?: string;
    fixedHeaderNum?: number;
    editor: Editor;
  }) {
    let tables: Table[] = [];
    if (name) {
      const arr = editor.getTablesByName(name);
      arr && (tables = arr);
    } else {
      const t = editor.selection.getFocusTable();
      if (editor.selection.isCollapsed || editor.selection.isOnlyCell) {
        t && tables.push(t);
      }
    }
    if (!tables.length) {
      editor.event.emit("message", { type: "warning", msg: "没有表格" });
      return; // 如果没有表格 也同时说明 光标没在表格里边 就直接 return
    }

    if (fixedHeaderNum === 0) {
      // 如果是取消固定表头的话 下边的逻辑就都不用走了
      tables.forEach((table) => {
        table.fixed_table_header_num = fixedHeaderNum;
      });
      editor.update();
      editor.render();
      return;
    }

    // 有选区的情况下 找到 endRow 最大的单元格 然后没有单元格包含该单元格 就可以固定
    const table = tables[0];
    let maxEndRowIndex = 0; // 初始值为0
    if (fixedHeaderNum) {
      // 如果人家传了  就用人家传的 (当然传递的值 也不一定能用)
      maxEndRowIndex = fixedHeaderNum - 1;
    } else {
      // 如果没传 就用选区的 就用默认值 1
      const para_end = editor.selection.para_end;
      if (para_end.length > 3 && !editor.selection.isCollapsed) {
        // 要么用传的表头行数 要么就默认为1 如果想要光标指定就必须是选区 因为可能大部分固定表头都是固定一行 而要固定表头的表格又是光标位置处的表格(他们不能任意点击任意单元格了，这样不好)
        if (editor.current_cell.children[para_end[0]] === table) {
          maxEndRowIndex = table.children[para_end[1]].end_row_index;
        }
      } else {
        maxEndRowIndex = 0;
      }
    }
    maxEndRowIndex < 0 && (maxEndRowIndex = 0); // 边界情况考虑
    for (const table of tables) {
      if (maxEndRowIndex >= table.row_size.length - 1) {
        editor.event.emit("message", {
          type: "warning",
          msg: "整个表格都在表头里边了,不能固定表头",
        });
        return;
      }
      for (const cell of table.children) {
        if (
          cell.start_row_index <= maxEndRowIndex &&
          cell.end_row_index > maxEndRowIndex
        ) {
          editor.event.emit("message", {
            type: "warning",
            msg: "有跨行单元格不能固定表头",
          });
          return;
        }
      }
      table.fixed_table_header_num = fixedHeaderNum || maxEndRowIndex + 1;
    }

    editor.update();
    editor.render();
  }

  static drawSlashOnFocusCell(
    direction: Direction,
    editor: Editor,
    count: number = 1
  ) {
    const cursor_path = editor.selection.anchor;
    if (cursor_path.length < 3) {
      return;
    }
    const current_table = editor.current_cell.children[cursor_path[0]];
    const current_cell = current_table.children[cursor_path[1]] as Cell;
    const selected_cells = editor.selection.selected_cells;
    // 因为 Number(true) 的结果是 1 Number(false) 的结果是 0 正好符合
    if (selected_cells.length > 0) {
      for (const { cell } of selected_cells) {
        if (direction === Direction.up) {
          // 斜下
          Number(cell.is_show_slash_up) === count
            ? (cell.is_show_slash_up = 0)
            : (cell.is_show_slash_up = count);
        }
        if (direction === Direction.down) {
          // 斜上
          Number(cell.is_show_slash_down) === count
            ? (cell.is_show_slash_down = 0)
            : (cell.is_show_slash_down = count);
        }
      }
    } else {
      if (direction === Direction.up) {
        Number(current_cell.is_show_slash_up) === count
          ? (current_cell.is_show_slash_up = 0)
          : (current_cell.is_show_slash_up = count);
      }
      if (direction === Direction.down) {
        Number(current_cell.is_show_slash_down) === count
          ? (current_cell.is_show_slash_down = 0)
          : (current_cell.is_show_slash_down = count);
      }
    }
    editor.update();
    editor.render();
    return true;
  }

  // 根据 name 名获取表格
  static getTablesByName(name: string, editor: Editor) {
    const tables: Table[] = [];
    const paragraphTableArr = editor.current_cell.paragraph;
    for (let i = 0, len = paragraphTableArr.length; i < len; i++) {
      const paragraphOrTable = paragraphTableArr[i];
      if (isTable(paragraphOrTable) && paragraphOrTable.name === name) {
        tables.push(paragraphOrTable);
      }
    }
    return tables;
  }

  // 根据 id 获取表格
  static getTableById(editor: Editor, id: string): Table | void {
    const root = editor.root_cell.paragraph;
    for (const table of root) {
      if (isTable(table) && table.id === id) return table;
    }

    const header = editor.header_cell.paragraph;
    for (const table of header) {
      if (isTable(table) && table.id === id) return table;
    }

    const footer = editor.footer_cell.paragraph;
    for (const table of footer) {
      if (isTable(table) && table.id === id) return table;
    }
  }

  /**
   * 获取拆分后 cur_row 所在的表格，而且只能是 cur_row 所在的单元格没有被拆分的时候调用该方法
   * @param origin_tbl 原始表格
   * @param cur_row 当前行
   * @returns 被拆分后的表格
   */
  static getSplitTable(origin_tbl: Table, cur_row: Row) {
    let split_table!: Table;
    let current_cell_index: number = -1;
    const current_cell_id = cur_row.parent.id;
    for (let i = 0; i < origin_tbl.split_parts.length; i++) {
      const tbl = origin_tbl.split_parts[i];
      const cell = tbl.cellMap.get(current_cell_id);
      if (cell) {
        split_table = cell.parent!;
        current_cell_index = getCellIndex(cell);
        return { split_table, current_cell_index };
      }
    }
    return { split_table, current_cell_index };
  }

  static splitTableByFullRowsWithAnchors ({ editor }: {editor: Editor}) {
    const selection = editor.selection;
    if (selection.isCollapsed) {
      // 没有选区
      const table = editor.selection.getFocusTable();
      if (!table) return false;
      const range = selection.getCellRange();
      const { start_row_index, end_row_index } = range ? { start_row_index : range.minRow, end_row_index: range.maxRow } : selection.getFocusCell()!;
      table.splitTableByFullRowsWithAnchors({ start_row_index, end_row_index });
      return true;
    } else {
      const selectedCells = [...selection.selected_cells];
      if (selectedCells && selectedCells.length) {
        let table = selectedCells[0].cell.parent;
        let cellsOfSaveTable: {cell: Cell}[] = [];
        for (const { cell } of selectedCells) {
          if (!table) return false;
          if (cell.parent === table) {
            cellsOfSaveTable.push({ cell });
          } else {
            const range = selection.getCellRange({ cells: cellsOfSaveTable })!;
            const { start_row_index, end_row_index } = { start_row_index : range.minRow, end_row_index: range.maxRow };
            table.splitTableByFullRowsWithAnchors({ start_row_index, end_row_index });

            cellsOfSaveTable = [{ cell }];
            table = cell.parent;
          }
        }
        if (cellsOfSaveTable.length && table) {
          const range = selection.getCellRange({ cells: cellsOfSaveTable })!;
          const { start_row_index, end_row_index } = { start_row_index : range.minRow, end_row_index: range.maxRow };
          table.splitTableByFullRowsWithAnchors({ start_row_index, end_row_index });
        }
      }
      return true;
    }
  }
  
  /**
   * 获取光标位置处的表格
   * @param editor Editor
   * @returns 光标位置处的表格
   */
  static getFocusTable(editor: Editor): Table | undefined {
    // TODO 该方法跟 selection 中的 getFocusTable 重复了 那个感觉不如这个 没必要获取 row
    const rowOrTableIndex = editor.selection.focus[0];
    const rowAndTableArr = editor.current_cell.children;
    const tblOrRow = rowAndTableArr[rowOrTableIndex];
    if (isTable(tblOrRow)) {
      return tblOrRow;
    }
  }

  static getStrWithFocusTable(editor: Editor) {
    const path = editor.selection.anchor;
    const current_table = editor.current_cell.children[path[0]];
    let str = "";
    if (isTable(current_table)) {
      str = current_table.getStr();
    }
    return str;
  }

  static setTablesLock({
    tables,
    isLock = true,
    editor,
  }: {
    editor: Editor;
    tables?: Table[];
    isLock?: boolean;
  }) {
    if (!tables) {
      const current_table = editor.selection.getFocusTable();
      tables = current_table ? [current_table] : [];
    }
    tables.forEach((table) => {
      Cell.updateCellsAttr({ cells: table.children, attr: { lock: isLock }, editor });
    });
  }

  static splitCellWithCaret(editor: Editor) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    const selectedCells = editor.selection.selected_cells;
    if (selectedCells.length > 1) {
      // 选区情况下 不让拆分
      return false;
    }
    const cursorPath = editor.selection.focus; // 光标所对应的模型数据路径
    const rootTable = editor.current_cell.children[cursorPath[0]]; // 找到第一层数据 Table
    if (isTable(rootTable)) {
      const currentCell = rootTable.children[cursorPath[1]]; // 找到光标所在的单元格
      // 既没有合并行 也没有合并列的单元格 不允许拆分
      if (currentCell.rowspan <= 1 && currentCell.colspan <= 1) {
        return false;
      }

      const cells = currentCell.splitCell(rootTable);
      const retainParagraphCell = cells[0]; // 保留段落的单元格
      retainParagraphCell.id = currentCell.id; // ID 也保留一下
      const now_paragraph = currentCell.paragraph; // 里边可能会有Paragraph，也可能有Table
      now_paragraph.forEach((para) => {
        if (isParagraph(para)) {
          para.cell = retainParagraphCell;
        }
      });

      retainParagraphCell.paragraph = now_paragraph;
      rootTable.children.splice(cursorPath[1], 1, ...cells);
      rootTable.children.forEach((cell) => cell.typesetting());
      cursorPath.splice(2, 2, 0, 0); // 调整光标位置,不需要调用updateCaretByViewPath
      editor.update(...editor.getUpdateParamsByContainer(rootTable));
      editor.scroll_by_focus();
      editor.render();
      return true;
    } else {
      return false;
    }
  }

  static mergeCells(editor: Editor, pointCells?: Cell[]): boolean {
    let selected_cells_box: any;
    if (pointCells && pointCells.length > 1) {
      selected_cells_box = pointCells.map((c: Cell) => {
        return {
          para_path: c.paraPath!,
          cell: c

        }
      })
    } else {
      selected_cells_box = editor.selection.selected_cells
    }
    if (!this.canMergeCells(editor, selected_cells_box)) return false;

    // if (EditorLocalTest.useLocal) {
    //   if (editor.selection.isOnlyCell) {
    //     return editor.focusElement.table!.newMergeCells(editor);
    //   } else {
    //     return false;
    //   }
    // }

    // 1.获取所有选中的单元格相关数据
    // const selected_cells_box = editor.selection.selected_cells;
    const cells = []; // 保存所有被拆分的小单元格

    // 2. 将每个单元格都进行拆分
    for (const { cell } of selected_cells_box) {
      const splited_cell_arr = cell.splitCell(cell.parent as Table, true);
      cells.push(...splited_cell_arr);
    }

    // 3. 将每一行的数据 和 每一列的单元格 都分别存储到一个单独的对象中
    const { rows_cells_obj, cols_cells_obj } = getCellsOnRowAndCol(cells);

    const out_cell_paragraphs = editor.current_cell.paragraph; // 段落数据结构
    const firstPath = selected_cells_box[0].para_path; // 要保留的第一个单元格paraPath路径
    const rootTable: Table = out_cell_paragraphs[firstPath[0]] as Table; // 要合并单元格所在的表格

    const firstCell = selected_cells_box[0].cell; // 要保留的第一个单元格
    const firstCellParaPath = firstCell.paraPath;
    // 收集选中单元格的所有 paragraph 如果单元格内只有一个段落并且值还是\n的话 就不收集了
    const paragraphs: (Paragraph | Table)[] = [];
    const fields: XField[] = [];
    selected_cells_box.forEach((c: any) => {
      const current_cell = c.cell;
      // 处理文本域 ↓
      const current_cell_fields = current_cell.fields;
      current_cell_fields?.forEach((field: XField) => {
        field.cell = firstCell;
      });
      fields.push(...current_cell_fields);
      // 处理文本域 ↑
      const now_paragraph = current_cell.paragraph as Paragraph[]; // 每个单元格里边的paragraph数组 (考虑合并表格的话)里边可能会有Paragraph，也可能有Table
      const now_paragraph_length = now_paragraph.length;
      const first_character_value = now_paragraph[0].characters[0].value;
      now_paragraph.forEach((para) => {
        para.cell = firstCell;
        // if (!(now_paragraph_length === 1 && first_character_value === "\n")) {
        //   paragraphs.push(para);
        // }
        if (now_paragraph_length !== 1 || first_character_value !== "\n") {
          // 上边的注释 可能比较好理解 跟该写法一样
          paragraphs.push(para);
        }
      });
    });
    // 修改被保留的单元格colspan 和 rowspan 并且插入数据
    firstCell.colspan = (Object as any).values(rows_cells_obj)[0].length; // 一行上的单元格数量
    firstCell.rowspan = (Object as any).values(cols_cells_obj)[0].length; // 一列上的单元格数量
    firstCell.fields = fields;
    // 过滤掉空段落 并且赋值
    // 如果都是空格或者换行 不能都过滤掉了 也要留一个换行符
    if (paragraphs.length !== 0) {
      firstCell.paragraph = paragraphs;
    }

    // 修改模型数据
    // 找到不要的线 ↓
    const row_num_arr = Object.keys(rows_cells_obj);
    const first_row = +row_num_arr[0];
    const col_num_arr = Object.keys(cols_cells_obj);
    const first_col = col_num_arr[0];
    for (const cell of cells) {
      if (cell.position[0] > first_row) {
        // 这些横线就不要了
        const arr = [cell.position[0], cell.position[1]];
        rootTable.notAllowDrawLine.row.push(arr);
      }
      if (cell.position[1] > +first_col) {
        // 这些竖线就不要了
        const arr = [cell.position[1], cell.position[0]];
        rootTable.notAllowDrawLine.col.push(arr);
      }
    }
    // 找到不要的线 ↑

    // 我要找到四条依赖的透明度的线 修改透明度
    const baseUp = [firstCell.start_row_index, firstCell.start_col_index];
    const optUp = rootTable.notAllowDrawLine.changeOpacityRow.find(p => p[0] === baseUp[0] && p[1] === baseUp[1]);

    const baseRight = [firstCell.end_col_index + 1, firstCell.start_row_index];
    const optRight = rootTable.notAllowDrawLine.changeOpacityCol.find(p => p[0] === baseRight[0] && p[1] === baseRight[1]);

    const baseDown = [firstCell.end_row_index + 1, firstCell.start_col_index];
    const optDown = rootTable.notAllowDrawLine.changeOpacityRow.find(p => p[0] === baseDown[0] && p[1] === baseDown[1]);

    const baseLeft = [firstCell.start_col_index, firstCell.start_row_index]
    const optLeft = rootTable.notAllowDrawLine.changeOpacityCol.find(p => p[0] === baseLeft[0] && p[1] === baseLeft[1]);
    const upMap = new Map();
    const downMap = new Map();
    for (let i = 0; i < firstCell.colspan; i++) {
      if (optUp && i > 0) {
        const up = [optUp[0], optUp[1] + i];
        rootTable.notAllowDrawLine.changeOpacityRow.push(up)
      }
      upMap.set([baseUp[0], baseUp[1] + i].toString(), true);
      if (optDown && i > 0) {
        const down = [optDown[0], optDown[1] + i];
        rootTable.notAllowDrawLine.changeOpacityRow.push(down);
      }
      downMap.set([baseDown[0], baseDown[1] + i].toString(), true);
    }
    const leftMap = new Map();
    const rightMap = new Map();
    for (let i = 0; i < firstCell.rowspan; i++) {
      if (optRight && i > 0) {
        const right = [optRight[0], optRight[1] + i];
        rootTable.notAllowDrawLine.changeOpacityCol.push(right);
      }
      rightMap.set([baseRight[0], baseRight[1] + i].toString(), true);
      if (optLeft && i > 0) {
        const left = [optLeft[0], optLeft[1] + i];
        rootTable.notAllowDrawLine.changeOpacityCol.push(left);
      }
      leftMap.set([baseLeft[0], baseLeft[1] + i].toString(), true);
    }
    for (let i = 0; i < rootTable.notAllowDrawLine.changeOpacityRow.length; i++) {
      const p = rootTable.notAllowDrawLine.changeOpacityRow[i];
      if (!optUp && upMap.has(p.toString())) {
        rootTable.notAllowDrawLine.changeOpacityRow.splice(i, 1);
        i--;
      }
      if (!optDown && downMap.has(p.toString())) {
        rootTable.notAllowDrawLine.changeOpacityRow.splice(i, 1);
        i--;
      }
    }
    for (let i = 0; i < rootTable.notAllowDrawLine.changeOpacityCol.length; i++) {
      const p = rootTable.notAllowDrawLine.changeOpacityCol[i];
      if (!optRight && rightMap.has(p.toString())) {
        rootTable.notAllowDrawLine.changeOpacityCol.splice(i, 1);
        i--;
      }
      if (!optLeft && leftMap.has(p.toString())) {
        rootTable.notAllowDrawLine.changeOpacityCol.splice(i, 1);
        i--;
      }
    }
    // 最后找到不要的单元格删掉 update 和 render一下就可以了
    for (let i = 0; i < rootTable.children.length; i++) {
      for (let j = 0; j < selected_cells_box.length; j++) {
        if (
          rootTable.children[i] === selected_cells_box[j].cell &&
          selected_cells_box[j] !== selected_cells_box[0]
        ) {
          rootTable.children.splice(i, 1);
          i--; // 因为rootTable 是动态的
        }
      }
    }
    editor.selection.clearSelectedInfo(); // 清空选区
    firstCell.typesetting();
    // 重置光标 在合并后的单元格末尾处
    const paragraph_index = firstCell.paragraph.length - 1;
    const character_index =
      (firstCell.paragraph[firstCell.paragraph.length - 1] as Paragraph)
        .characters.length - 1;
    firstCellParaPath.splice(
      1,
      3,
      getCellIndex(firstCell),
      paragraph_index,
      character_index
    );
    editor.selection.setCursorPosition(
      editor.paraPath2ModelPath(firstCellParaPath)
    );
    editor.update(...editor.getUpdateParamsByContainer(rootTable));
    editor.render();
    return true;
  }

  /**
   * 改变表格线
   * @param editor
   * @returns
   */
  static changeTableLines(editor: Editor) {
    const resize_table = editor.internal.resize_cell!.parent!; // 所以这个 resize_table 不是原始表格 而是分页之后 split_parts 里边的表格
    let origin_table = resize_table; // 表格分页时 按照父级的那个整体表格来计算拖动边线 否则在表格没有分页时 就按照当前表格来计算
    if (resize_table.origin) {
      origin_table = resize_table.origin;
    }
    const left_col = origin_table.left_col;
    const right_col = origin_table.right_col;
    // 调整单元格尺寸
    editor.resetCellSize(
      editor.internal.resize_cell!,
      editor.internal.resize_cell_position,
      left_col,
      right_col
    );
    editor.internal.resize_cell = null;
    editor.update(...editor.getUpdateParamsByContainer(origin_table));
    return true;
  }

  /**
   * 拖动表格线
   * @param editor Editor
   * @param x 拖动表格线的位置
   * @param y 拖动表格线的位置
   */
  static dragLine(editor: Editor, x: number, y: number) {
    // 生成虚拟表格线
    const resize_cell = editor.internal.resize_cell!;
    const vertical_change =
      y + editor.scroll_top - editor.internal.pointer_down_position.y; // 垂直拖动 拖动的时候的高度 正值是往下 负值是往上
    const horizontal_change = x - editor.internal.pointer_down_position.x; // 水平拖动 鼠标移动的坐标 减去 鼠标落下的坐标 就是线应该移动的距离 正为往右，负为往左，0为不动
    const resize_table = resize_cell.parent!;
    const config_min_row_size = Config.min_row_size;
    const config_min_col_size = Config.min_col_size;
    // 需要计算该表格在第几页
    const footer_top =
      editor.pages[
        editor.is_edit_hf_mode
          ? editor.internal.current_page!.number - 1
          : resize_table.page_number - 1
      ].footer.footer_outer_top -
      editor.config.content_margin_footer -
      resize_table.top;

    // 垂直上下拖动
    // resize_row_index 要修改的行号 row_index 单元格的行号 据此查找拖动线上下的单元格
    // init_line_val 点中的拖动线初始距离表格顶部的距离 即点中单元格的top或者bottom
    const resize_vertical_line = (
      resize_row_index: number,
      init_line_val: number
    ) => {
      let max_top = config_min_row_size; // 虚拟线距离表格顶部应该最大的距离 就是往上不能再拖了
      resize_table.children.forEach((cell) => {
        // 找到所有跟要修改的行 行号一致的单元格
        // 从这些单元格里边找 往上拖动的的最大值 max_top
        // 最小高度为Config配置文件里 最小的行高 20
        if (cell.end_row_index === resize_row_index) {
          let t =
            (cell.getContentHeight() < config_min_row_size * cell.rowspan
              ? config_min_row_size * cell.rowspan
              : cell.getContentHeight()) + cell.top;

          if (cell.set_cell_height.type === "scroll") {
            t = Math.min(t, cell.set_cell_height.height!);
          }

          // const t = config_min_row_size * cell.rowspan + cell.top; // 因为会重新typesetting 所以这样写 意义不太大
          if (t > max_top) {
            max_top = t;
          }
        }
      });
      const mouse_move_val = init_line_val + vertical_change; // 鼠标按住线拖动时 距离表格顶部的距离 不加限制的情况下 该值为表格线拖动的值
      // 如果在mouse_move_val和footer_top之间就用mouse_move_val否则根据是往上还是往下拖动决定用mouse_move_val还是footer_top
      resize_table.resize_horizontal_line =
        mouse_move_val >= max_top && mouse_move_val <= footer_top
          ? mouse_move_val
          : vertical_change > 0 // vertical_change大于0 是往下拖
            ? footer_top
            : max_top;
    };

    // 水平左右拖动
    const resize_horizontal_line = (
      resize_col_index: number, // 点在线左侧时 是点中单元格的末尾列 点在右侧时 是点中单元格的 起始列
      init_line_val: number,
      direction: Direction
    ) => {
      let max_left = config_min_col_size; // 虚拟线距离表格左侧应该最大的距离

      const min_right_arr: number[] = []; // 保存符合单元格的 右侧距离表格左侧的距离 在这里找最小值 就是往右能拖动的最大值
      const left_arr: number[] = []; // 数组 为了从这里边找到 距离拖动线最近的左侧的表格线
      const right_arr: number[] = []; // 数组 为了从这里边找到 距离拖动线最近的右侧的表格线
      let calc_tbl = resize_table; // 表格分页时 按照父级的那个整体表格来计算拖动边线 否则在表格没有分页时 就按照当前表格来计算
      if (resize_table.origin) {
        calc_tbl = resize_table.origin;
      }
      calc_tbl.children.forEach((cell) => {
        // ①：往左拖动的时候
        // 找到所有 末尾列 列号 跟当前单元格 末尾列 列号一致的单元格 找到他们里边元素最宽值
        const line_left_col = // 线左侧的列 列号
          direction === Direction.left // 点在了左侧单元格
            ? resize_col_index
            : resize_col_index - 1;
        if (cell.end_col_index === line_left_col) {
          // 不应该算上padding
          left_arr.push(cell.start_col_index);
          const t = config_min_col_size * cell.colspan + cell.left;
          if (t > max_left) {
            max_left = t;
          }
        } else if (
          cell.start_col_index <= line_left_col &&
          cell.end_col_index > line_left_col
        ) {
          // 还得找到 起始列 小于等于 line_left_col 并且末尾列大于(已经判断等于了 所以找大于的)line_left_col的单元格
          // (点在线左侧单元格逻辑的注释)用 点中单元格的末尾列列号 减去 当前单元格的起始列的列号 然后+1 再 * config_min_col_size + cell.left
          left_arr.push(cell.start_col_index);
          const t =
            (line_left_col - cell.start_col_index + 1) * config_min_col_size +
            cell.left;
          if (t > max_left) {
            max_left = t;
          }
        }
        const cell_min_size = cell.getCellMinSize();
        max_left = max_left > cell_min_size ? max_left : cell_min_size;
        // ②：往右拖动的时候
        // 找到所有 起始列  跟当前单元格 末尾列+1一致的单元格
        // 应该找往右拖动的最小值(不能再往右拖了)
        const line_right_col = // 线右侧的列 列号
          direction === Direction.left // 点在左侧单元格
            ? resize_col_index + 1 // 点中单元格 末尾列 列号+1
            : resize_col_index;
        if (cell.position[1] === line_right_col) {
          const max_right = cell.right - config_min_col_size * cell.colspan;
          right_arr.push(cell.end_col_index);
          min_right_arr.push(max_right);
        } else if (
          cell.start_col_index < line_right_col &&
          cell.end_col_index >= line_right_col
        ) {
          right_arr.push(cell.end_col_index);
          // 还得找到 起始列 小于 line_right_col 并且末尾列大于等于line_right_col的单元格
          // 用当前单元格末尾列的列号 - line_right_col + 1的结果*config_min_col_size 再用cell.right减去算出来的结果  // 点中单元格的末尾列列号 减去 当前单元格的起始列的列号 然后+1 再 * config_min_col_size + cell.left
          const t =
            cell.right -
            (cell.end_col_index - line_right_col + 1) * config_min_col_size;
          min_right_arr.push(t);
        }
        min_right_arr.push(calc_tbl.width - cell_min_size);
      });
      const mouse_move_val = init_line_val + horizontal_change; // 点中单元格的右边线距离表格左侧的距离 + 鼠标移动的距离(正负0三种情况) 所以val 就是点中的线 距离表格左侧的距离
      const max_right = Math.min(...min_right_arr);
      calc_tbl.left_col = Math.max(...left_arr);
      calc_tbl.right_col = Math.min(...right_arr);
      const resize_vertical_line =
        mouse_move_val >= max_left && mouse_move_val <= max_right
          ? mouse_move_val
          : horizontal_change > 0
            ? max_right
            : max_left;
      // 表格分页时 每个页面上的表格 都要有虚拟表格线
      if (resize_table.origin && resize_table.origin!.split_parts.length > 0) {
        resize_table.origin!.split_parts.forEach((tbl) => {
          // 绘制虚拟线的时候 那里边的editor 指的是split_parts里边的editor 所以都要赋值
          tbl.resize_vertical_line = resize_vertical_line;
        });
        resize_table.origin.resize_vertical_line = resize_vertical_line; // 因为修改row_size的时候是修改的完整的表格上的row_size
      } else {
        resize_table.resize_vertical_line = resize_vertical_line;
      }
    };

    if (editor.internal.resize_cell_position === Direction.up) {
      // 鼠标落在了线下方单元格
      const resize_row_index = resize_cell.position[0] - 1; // 将要修改行的行号  就是要修改的row_size下标
      resize_vertical_line(resize_row_index, resize_cell.top);
    } else if (editor.internal.resize_cell_position === Direction.down) {
      // 鼠标落在线上方的单元格
      const resize_row_index =
        resize_cell.position[0] + resize_cell.rowspan - 1; // 点中单元格的末尾行的行号 同时也是要修改的行row_size的下标
      resize_vertical_line(resize_row_index, resize_cell.bottom);
    } else if (editor.internal.resize_cell_position === Direction.left) {
      // 说明点在了左侧的单元格
      const resize_col_index = resize_cell.end_col_index; // 点中单元格的末尾列 列号
      resize_horizontal_line(
        resize_col_index,
        resize_cell.right,
        Direction.left
      );
    } else {
      // 说明点在了竖线右侧的单元格
      const resize_col_index = resize_cell.start_col_index; // 当前单元格起始列
      resize_horizontal_line(
        resize_col_index,
        resize_cell.left,
        Direction.right
      );
    }

    editor.render();
  }

  static toggleLines(
    line_show_type: string,
    editor: Editor,
    count: number = 1
  ) {
    if (
      editor.selection.focus.length < 3 &&
      editor.selection.anchor.length < 3
    ) {
      return false;
    }
    // 获取操作目标单元格
    const selectedCells = editor.selection.getMaxMinCell();
    let show_line = false;
    let cells: Cell[] = [];
    let row_lines: number[][] = [];
    let col_lines: number[][] = [];
    switch (line_show_type) {
      case "under_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          // 获取选区的最下方的单元格，设置
          for (let i = 0; i < selectedCells.max_row_cells.length; i++) {
            const cell = selectedCells.max_row_cells[i];
            // [x+1,y]
            const line_position: any = [
              cell.position[0] + cell.rowspan,
              cell.position[1] + cell.colspan - 1,
            ];
            // judgeLineShow这个方法的结果代表line_position这个线是否在表格不画的数组里
            show_line = cell.parent!.judgeLineShow("row", line_position);
            // 如果有一条线在原表格中不画的数组里，那么本次设置统一为 画它 true
            if (show_line) break;
          }
          cells = selectedCells.max_row_cells;
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          // [x+1,y]
          const line_position: any = [
            current_cell.position[0] + current_cell.rowspan,
            current_cell.position[1] + current_cell.colspan - 1,
          ];
          show_line = current_cell.parent!.judgeLineShow("row", line_position);
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(Direction.down, show_line, cells);
        break;
      case "top_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          // 获取选区的最上方的单元格，设置
          for (let i = 0; i < selectedCells.min_row_cells.length; i++) {
            const cell = selectedCells.min_row_cells[i];
            // [x,y]
            const line_position: any = [cell.position[0], cell.position[1]];
            // judgeLineShow这个方法的结果代表line_position这个线是否在表格不画的数组里
            show_line = cell.parent!.judgeLineShow("row", line_position);
            // 如果有一条线在原表格中不画的数组里，那么本次设置统一为 画它 true
            if (show_line) break;
          }
          cells = selectedCells.min_row_cells;
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          // [x,y]
          const line_position: any = [
            current_cell.position[0],
            current_cell.position[1],
          ];
          show_line = current_cell.parent!.judgeLineShow("row", line_position);
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(Direction.up, show_line, cells);
        break;
      case "left_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          // 获取选区的最上方的单元格，设置
          for (let i = 0; i < selectedCells.min_col_cells.length; i++) {
            const cell = selectedCells.min_col_cells[i];
            // [y,x]
            const line_position: any = [cell.position[1], cell.position[0]];
            // judgeLineShow这个方法的结果代表line_position这个线是否在表格不画的数组里
            show_line = cell.parent!.judgeLineShow("col", line_position);
            // 如果有一条线在原表格中不画的数组里，那么本次设置统一为 画它 true
            if (show_line) break;
          }
          cells = selectedCells.min_col_cells;
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          // [y,x]
          const line_position: any = [
            current_cell.position[1],
            current_cell.position[0],
          ];
          show_line = current_cell.parent!.judgeLineShow("col", line_position);
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(Direction.left, show_line, cells);
        break;
      case "right_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          // 获取选区的最上方的单元格，设置
          for (let i = 0; i < selectedCells.max_col_cells.length; i++) {
            const cell = selectedCells.max_col_cells[i];
            // [y,x]
            const line_position: any = [
              cell.position[1] + cell.colspan,
              cell.position[0] + cell.rowspan - 1,
            ];
            // judgeLineShow这个方法的结果代表line_position这个线是否在表格不画的数组里
            show_line = cell.parent!.judgeLineShow("col", line_position);
            // 如果有一条线在原表格中不画的数组里，那么本次设置统一为 画它 true
            if (show_line) break;
          }
          cells = selectedCells.max_col_cells;
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          // [y,x]
          const line_position: any = [
            current_cell.position[1] + current_cell.colspan,
            current_cell.position[0] + current_cell.rowspan - 1,
          ];
          show_line = current_cell.parent!.judgeLineShow("col", line_position);
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(Direction.right, show_line, cells);
        break;
      case "no_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          editor.selection.selected_cells.forEach((e) => {
            cells.push(e.cell);
          });
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(null, false, cells);
        break;
      case "all_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          editor.selection.selected_cells.forEach((e) => {
            cells.push(e.cell);
          });
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          cells.push(current_cell);
        }
        editor.toggleCellSingleLineShowHide(null, true, cells);
        break;
      case "out_line":
        if (selectedCells) {
          // 如果是选区 进入该代码块
          // 获取边线
          row_lines = editor.selection.getSelectedOutLine().row_lines;
          col_lines = editor.selection.getSelectedOutLine().col_lines;
          // 当前表格
          const current_table = selectedCells.min_row_cells[0].parent!;
          // 竖线数组中 是否有隐藏的线
          for (let i = 0; i < col_lines.length; i++) {
            const col_line = col_lines[i];
            show_line = current_table.judgeLineShow("col", col_line);
            if (show_line) break;
          }
          // 横线数组中 是否有隐藏的线
          if (!show_line) {
            for (let j = 0; j < row_lines.length; j++) {
              const row_line = row_lines[j];
              show_line = current_table.judgeLineShow("row", row_line);
              if (show_line) break;
            }
          }
          // 设置绘制与否
          if (show_line) {
            current_table.showSomeLine("row", row_lines);
            current_table.showSomeLine("col", col_lines);
          } else {
            // 隐藏
            current_table.notAllowDrawLine.changeOpacityRow.push(...row_lines);
            current_table.notAllowDrawLine.changeOpacityCol.push(...col_lines);
          }
          editor.update();
          editor.render();
        } else {
          // 单个单元格
          const focus_path = editor.selection.focus;
          const current_table = editor.current_cell.children[focus_path[0]];
          const current_cell = current_table.children[focus_path[1]] as Cell;
          // [y,x]
          const line_position: any = [
            current_cell.position[0],
            current_cell.position[1],
          ];
          show_line = current_cell.parent!.judgeLineShow("row", line_position);
          cells.push(current_cell);
          editor.toggleCellSingleLineShowHide(null, show_line, cells);
        }
        break;
      case "inside_line":
        if (selectedCells) {
          // 选区内所有的线
          const selected_all_row =
            editor.selection.getSelectedAllLine().row_lines;
          const selected_all_col =
            editor.selection.getSelectedAllLine().col_lines;
          // 选区内四周的线
          const selected_out_row =
            editor.selection.getSelectedOutLine().row_lines;
          const selected_out_col =
            editor.selection.getSelectedOutLine().col_lines;
          // 选区中间的线
          const changed_row_result: number[][] = deleteTwoDimArrElement(
            selected_all_row,
            selected_out_row
          );
          const changed_clo_result: number[][] = deleteTwoDimArrElement(
            selected_all_col,
            selected_out_col
          );
          // 表格
          const current_table = selectedCells.min_row_cells[0].parent!;
          // 判断是否展示
          for (let i = 0; i < changed_row_result.length; i++) {
            const row_line = changed_row_result[i];
            show_line = current_table.judgeLineShow("row", row_line);
            if (show_line) break;
          }
          // 竖线数组中 是否有隐藏的线
          if (!show_line) {
            for (let j = 0; j < changed_clo_result.length; j++) {
              const col_line = changed_clo_result[j];
              show_line = current_table.judgeLineShow("col", col_line);
              if (show_line) break;
            }
          }
          // 设置绘制与否
          if (show_line) {
            current_table.showSomeLine("row", changed_row_result);
            current_table.showSomeLine("col", changed_clo_result);
          } else {
            // 隐藏
            current_table.notAllowDrawLine.changeOpacityRow.push(
              ...changed_row_result
            );
            current_table.notAllowDrawLine.changeOpacityCol.push(
              ...changed_clo_result
            );
          }
          editor.update();
          editor.render();
        }
        break;
      case "inside_flat_line":
        if (selectedCells) {
          // 选区内所有的横线
          const selected_all_row =
            editor.selection.getSelectedAllLine().row_lines;
          // 选区内四周的横线
          const selected_out_row =
            editor.selection.getSelectedOutLine().row_lines;
          // 选区中间的横线
          const changed_row_result: number[][] = deleteTwoDimArrElement(
            selected_all_row,
            selected_out_row
          );
          // 表格
          const current_table = selectedCells.min_row_cells[0].parent!;
          // 判断是否展示
          for (let i = 0; i < changed_row_result.length; i++) {
            const row_line = changed_row_result[i];
            show_line = current_table.judgeLineShow("row", row_line);
            if (show_line) break;
          }
          // 设置绘制与否
          if (show_line) {
            current_table.showSomeLine("row", changed_row_result);
          } else {
            // 隐藏
            current_table.notAllowDrawLine.changeOpacityRow.push(
              ...changed_row_result
            );
          }
          editor.update();
          editor.render();
        }
        break;
      case "inside_vertical_line":
        if (selectedCells) {
          // 选区内所有的线
          const selected_all_col =
            editor.selection.getSelectedAllLine().col_lines;
          // 选区内四周的线
          const selected_out_col =
            editor.selection.getSelectedOutLine().col_lines;
          // 选区中间的线
          const changed_clo_result: number[][] = deleteTwoDimArrElement(
            selected_all_col,
            selected_out_col
          );
          // 表格
          const current_table = selectedCells.min_row_cells[0].parent!;
          // 竖线数组中 是否有隐藏的线
          for (let j = 0; j < changed_clo_result.length; j++) {
            const col_line = changed_clo_result[j];
            show_line = current_table.judgeLineShow("col", col_line);
            if (show_line) break;
          }
          // 设置绘制与否
          if (show_line) {
            current_table.showSomeLine("col", changed_clo_result);
          } else {
            // 隐藏
            current_table.notAllowDrawLine.changeOpacityCol.push(
              ...changed_clo_result
            );
          }
          editor.update();
          editor.render();
        }
        break;
      case "inside_inclined_top_line":
        // 右斜下
        editor.drawSlashOnCell(Direction.up, count);
        break;
      case "inside_inclined_bottom_line":
        // 右斜上
        editor.drawSlashOnCell(Direction.down, count);
        break;
      case "tableLine_inside_inclined_left_bottom":
        // 左斜上
        editor.drawSlashOnCell(Direction.up, count);
        break;
      case "tableLine_inside_inclined_left_top":
        // 左斜下
        editor.drawSlashOnCell(Direction.down, count);
        break;
    }
    return true;
  }

  static getCaretPathInEmptyArea(
    editor: Editor,
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row)[],
    path: Path = []
  ) {
    if (editor.editFloatModelMode) {
      x += editor.currentFloatModel!.originPosition[0];
      y += editor.currentFloatModel!.originPosition[1];
    }
    for (let i = 0; i < containers.length; i++) {
      const container = containers[i];
      if (isCell(container) && container.contain(x, y)) {
        x -= container.left;
        y -= container.top;
        const last_row = container.children[container.children.length - 1];
        if (last_row) {
          path.push(i);
          x -= last_row.left;
          y -= container.top;
          path.push(container.children.length - 1);
          path.push((last_row as Row).offset_near(x));
          return {
            row: last_row as Row,
            view_path: path,
            x: x,
            y: y,
          };
        }
        else {
          const originCellParts = container.origin?.split_parts
          if (originCellParts && originCellParts.length) {

            for (let j = originCellParts.length - 1; j >= 0; j--) {
              const originCellPart = originCellParts[j];
              if (originCellPart.children.length) {
                path[0] = originCellPart.parent!.page_number - 1
                path[1] = originCellPart.parent!.page_index;
                path.push(originCellPart.index);
                const origin_last_row = originCellPart.children[originCellPart.children.length - 1];
                x -= origin_last_row.left;
                y -= originCellPart.top;
                path.push(originCellPart.children.length - 1);
                path.push((origin_last_row as Row).offset_near(x));
                return {
                  row: origin_last_row as Row,
                  view_path: path,
                  x: x,
                  y: y,
                };
              }
            }
          }
        }
      } else if (isPage(container)) {
        if (container.contain(x, y)) {
          path.push(i)
          x -= container.left;
          y -= container.top;

          let last_row: any;

          if (
            editor.is_edit_hf_mode &&
            editor.current_cell.hf_part === "header"
          ) {
            last_row =
              container.header.children[container.header.children.length - 1];
          } else if (editor.is_edit_hf_mode &&
            editor.current_cell.hf_part === "footer") {
            last_row =
              container.header.children[container.footer.children.length - 1];
          } else {
            last_row = container.children[container.children.length - 1];
          }

          if (last_row && isRow(last_row)) {
            const empty_top = last_row.top + last_row.height;
            if (y >= empty_top) {
              x -= last_row.left;
              y -= last_row.top;
              path.push(container.children.length - 1);
              path.push((last_row as Row).offset_near(x));

              return {
                row: last_row as Row,
                view_path: path,
                x: x,
                y: y,
              };
            }
          }

          let children;
          if (
            editor.is_edit_hf_mode &&
            editor.current_cell.hf_part === "header"
          ) {
            children = container.header.children;
          } else if (editor.is_edit_hf_mode &&
            editor.current_cell.hf_part === "footer") {
            children = container.footer.children;
          } else {
            children = container.children;
          }
          const res = editor.getCaretPathInTableEmptyArea(x, y, children, path);
          return res;
        }
      } else if (isTable(container)) {
        if (container.contain(x, y)) {
          path.push(i)
          x -= container.left;
          y -= container.top;

          return editor.getCaretPathInTableEmptyArea(
            x,
            y,
            container.children,
            path
          );
        }
      }
    }
  }

  /**
   * 切换单元格的 单条线 是否显示隐藏
   * @param direction 要切换的线的方向 要切换哪条线
   * @param is_show 是否展示
   * @param cells 单元格集合
   * @param editor Editor
   * @returns 是否执行成功
   */
  static toggleCellsLine(
    direction: Direction | null,
    is_show: boolean,
    cells: Cell[],
    editor: Editor
  ) {
    // 若干单元格
    cells.forEach((cell) => {
      cell.toggleLineShowHide(direction, is_show);
    });
    editor.update();
    editor.render();
    return true;
  }

  /**
   * 指定行转原始数据
   * @param editor
   * @param table
   * @param rowIndexArray
   */
  static assignRow2RawData(table: Table, rowIndexArray: number[]) {
    const editor = table.editor;
    const newRootCell = new Cell(editor, [0, 0], 1, 1, null, "trans");
    const allCells = [];
    for (let i = 0, len = rowIndexArray.length; i < len; i++) {
      allCells.push(...table.getCellsByRowIndex(rowIndexArray[i]));
    }
    const newTable = someCells2Table(allCells, "row");
    newRootCell.paragraph.push(newTable);
    // const rawData = rawDataTrans.modelDataToRawData(
    //   editor.header_cell,
    //   newRootCell,
    //   editor.footer_cell,
    //   editor.packSaveDocumentInfo()
    // );
    const rawData = editor.event.emit(
      "modelData2RawData",
      editor.header_cell,
      newRootCell,
      editor.footer_cell,
      editor.packSaveDocumentInfo()
    );
    return getRawDataByConfig(editor, rawData);
  }

  static attrJudgeUndefinedAssign(newTable: any, raw: any) {
    if (raw.name !== undefined) newTable.name = raw.name;
    if (raw.notAllowDrawLine !== undefined) {
      newTable.notAllowDrawLine = raw.notAllowDrawLine;
    }
    if (raw.fixed_table_header_num !== undefined) {
      newTable.fixed_table_header_num = raw.fixed_table_header_num;
    }
    if (raw.editableInFormMode !== undefined) {
      newTable.editableInFormMode = raw.editableInFormMode;
    }
    if (raw.rowLineType !== undefined) {
      newTable.rowLineType = raw.rowLineType;
    }
    if (raw.tableFiexedStyle !== undefined) {
      newTable.tableFiexedStyle = raw.tableFiexedStyle;
    }
    if (raw.recordWidth !== undefined) {
      newTable.recordWidth = raw.recordWidth;
    }
    if (raw.recordHeight !== undefined) {
      newTable.recordHeight = raw.recordHeight;
    }
    if (raw.maxTableHeight !== undefined) {
      newTable.maxTableHeight = raw.maxTableHeight;
    }
    if (raw.needSerialNum !== undefined) {
      newTable.needSerialNum = raw.needSerialNum;
    }
    if (raw.meta !== undefined) {
      newTable.meta = raw.meta;
    }
    if (raw.imageList !== undefined) {
      newTable.imageList = raw.imageList;
    }
    if (raw.newPage !== undefined) {
      newTable.newPage = raw.newPage;
    }
    if (raw.skipMode !== undefined) {
      newTable.skipMode = raw.skipMode;
    }
    if (raw.fullPage !== undefined) {
      newTable.fullPage = raw.fullPage;
    }
  }

  static multiTableSplicing(editor : Editor){
    let isHandle = false;
    const judgeIsAllAnchorField = (para:Paragraph)=>{
      const rows = para.children;
      if(rows.length>1){
        return false;
      }
      const chars = rows[0].children;
      let fields : XField[] = [];
      for (let i = 0 ,len = chars.length; i <  len; i++) {
        if(!chars[i].field_id) return false;
        const field = rows[0].parent.getFieldById(chars[i].field_id!);
        if(!field || field.type !== "anchor"){
          return false;
        }
        fields.push(field)
      }
      return fields;
    }

    const delAnchorFields = (para:any)=>{
      if(para){
        const fields = judgeIsAllAnchorField(para);
        if(fields){
          editor.contextState.resetFontState();
          para.characters[para.characters.length - 1].font = editor.contextState.font;
          editor.removeFields(fields)
          isHandle = true;
          return true;
        }
      }
    }

    const insertAnchorField = (para:any)=>{
      if(para.characters.length === 1 && isTable(para.previousParagraph)){
        editor.insertField({ type:"anchor" ,name:"autoSet" })
        isHandle = true;
      }
    }
    if(editor.selection.isCollapsed){
      const table = editor.selection.getFocusTable();
      if(table){
        delAnchorFields(table.previousPara)
        delAnchorFields(table.nextPara)
      }else{
        const para = editor.selection.getFocusParagraph();
        // 首先当前段落中没有其他字符
        insertAnchorField(para)
      }
    }else{
      const startIndex = editor.selection.start[0];
      const endIndex = editor.selection.end[0];
      let isInsertAnchor = null;
      for (let i = startIndex ; i < endIndex+1; i++) {
        const container = editor.current_cell.children[i];
        if(isRow(container)){
          const para = container.paragraph;
          editor.selection.setCursorPosition([i,0]);
          if(para.characters.length === 1){
            if(isInsertAnchor === null){
              isInsertAnchor = true;
            }
            if(isInsertAnchor){
              insertAnchorField(para)
            }
          }else{
            if(isInsertAnchor !== true && delAnchorFields(para)){
              if(isInsertAnchor === null){
                isInsertAnchor = false;
              }
            }

          }
        }
      }
    }
    return isHandle;
  }

  constructor(
    editor: Editor,
    id: string,
    group_id: string | null,
    col_size: number[],
    row_size: number[],
    min_row_size: number[],
    parent: Cell,
    left: number,
    right: number,
    top: number,
    newPage: boolean,
    skipMode: SkipMode
  ) {
    this.editor = editor;

    this.id = id;

    this.group_id = group_id;

    this.parent = parent;

    this.left = left + this.padding_left;

    this.top = top;

    this.row_size = [...row_size];

    this.newPage = newPage;

    this.skipMode = skipMode;

    // this.min_row_size = [...raw.min_row_size]; // 原来的逻辑

    this.min_row_size = min_row_size.map((v: number) =>
      v < Config.min_row_size ? Config.min_row_size : v
    );
    const calc_width = (col_size as number[]).reduce(
      (total, current) => total + current,
      0
    );

    this.col_size = (col_size as number[]).map(
      (item) => (item / calc_width) * (right - left)
    );
  }

  get height() {
    return this.row_size.reduce((total, current) => total + current);
  }

  get width() {
    return (
      this.parent.width - this.parent.padding_left - this.parent.padding_right
    );
  }

  get bottom() {
    return this.top + this.height;
  }

  get right() {
    return this.left + this.width;
  }

  /**
   * 上一个容器
   */
  get previous_container() {
    return this.parent.children[this.cell_index - 1];
  }

  /**
   * 下一个容器
   */
  get next_container() {
    return this.parent.children[this.cell_index + 1];
  }

  // 表格的前一段 一定存在
  public get previousPara(): Paragraph | null {
    let previousPara: Paragraph | null = null;
    previousPara = this.parent.paragraph[this.para_index - 1] as Paragraph;
    return previousPara;
  }

  // 表格的后一段同样一定存在
  public get nextPara(): Paragraph | null {
    let nextPara: Paragraph | null = null;
    nextPara = this.parent.paragraph[this.para_index + 1] as Paragraph;
    return nextPara;
  }

  // 可看到的行数
  get view_rows_num() {
    return this.getViewPositionArr().length;
  }

  // 可看到的列数
  get view_cols_num() {
    return this.getViewPositionArr(false).length;
  }

  setCellsGroupKey(cells: Cell[], groupKey: string = uuid("groupKey")) {
    for (const cell of cells) {
      if (isCell(cell)) {
        cell.groupKey = groupKey;
      }
    }
  }

  getGroupCells() {
    const obj: any = {};
    let isEmpty = true;
    for (const cell of this.children) {
      if (cell.groupKey) {
        if (!obj[cell.groupKey]) {
          isEmpty = false;
          obj[cell.groupKey] = [cell];
        } else {
          obj[cell.groupKey].push(cell);
        }
      }
    }
    if (isEmpty) return null
    return obj;
  }

  // 设置线的透明度 0 是完全透明 1 是不透明
  setOpacityOfCellsBorder({ cells, direction, opacity }: {cells: Cell[], direction: "top" | "right" | "bottom" | "left", opacity: number}) {
    if (!cells || !direction || opacity === undefined || cells.length === 0) return
    const map = new Map();
    for (let i = 0; i < cells.length; i++) {
      const c = cells[i];
      if (c.parent !== this) continue
      if (opacity === 0) {
        if (direction === "top") {
          for (let j = c.start_col_index; j <= c.end_col_index; j++ ){
            this.notAllowDrawLine.changeOpacityRow.push([c.start_row_index, j]);
          }
        }
        if (direction === "right") {
          for (let j = c.start_row_index; j <= c.end_row_index; j++) {
            this.notAllowDrawLine.changeOpacityCol.push([c.end_col_index + 1, j]);
          }
        }
        if (direction === "bottom") {
          for (let j = c.start_col_index; j <= c.end_col_index; j++ ){
            this.notAllowDrawLine.changeOpacityRow.push([c.end_row_index + 1, j]);
          }
        }
        if (direction === "left") {
          for (let j = c.start_row_index; j <= c.end_row_index; j++) {
            this.notAllowDrawLine.changeOpacityCol.push([c.start_col_index, j]);
          }
        }
      }
      if (opacity === 1) {
        if (direction === "top") {
          for (let j = c.start_col_index; j <= c.end_col_index; j++ ){
            map.set([c.start_row_index, j].toString(), true);
          }
        }
        if (direction === "right") {
          for (let j = c.start_row_index; j <= c.end_row_index; j++) {
            map.set([c.end_col_index + 1, j].toString(), true);
          }
        }
        if (direction === "bottom") {
          for (let j = c.start_col_index; j <= c.end_col_index; j++ ){
            map.set([c.end_row_index + 1, j].toString(), true);
          }
        }
        if (direction === "left") {
          for (let j = c.start_row_index; j <= c.end_row_index; j++) {
            map.set([c.start_col_index, j].toString(), true);
          }
        }
      }
    }
    if (direction === "top" || direction === "bottom") {
      for (let i = 0; i < this.notAllowDrawLine.changeOpacityRow.length; i++) {
        if (map.has(this.notAllowDrawLine.changeOpacityRow[i].toString())) {
          this.notAllowDrawLine.changeOpacityRow.splice(i, 1);
          i--;
        }
      }
    }

    if (direction === "right" || direction === "left") {
      for (let i = 0; i < this.notAllowDrawLine.changeOpacityCol.length; i++) {
        if (map.has(this.notAllowDrawLine.changeOpacityCol[i].toString())) {
          this.notAllowDrawLine.changeOpacityCol.splice(i, 1);
          i--;
        }
      }
    }

    this.editor.refreshDocument();

    // for (let i = 0; i < cells.length; i++) {
    //   const { cell, top, right, bottom, left } = cells[i];
    //   if (cell.parent !== this) {
    //     this.editor.event.emit("message", {
    //       type: "warning",
    //       msg: "该单元格不属于当前表格",
    //     });
    //     continue;
    //   }
    //   if (top === 0) {
    //     console.log("没走");
    //     for (let j = cell.start_col_index; j <= cell.end_col_index; j++) {
    //       this.notAllowDrawLine.changeOpacityRow.push([cell.start_row_index, j]);
    //       console.log([cell.start_row_index, j])
    //       console.log("走了吧");
    //     }
    //   }
    //   if (right === 0) {}
    //   if (bottom === 0) {}
    //   if (left === 0) {}
    // }

  }

  // 设置该表格边线的透明度
  setOpacityOfBorder({ direction, opacity } :{direction: "top" | "right" | "bottom" | "left", opacity: number}) {
    if (!direction || opacity === undefined) return;
    if (opacity === 0) {
      if (direction === "top") {
        for (let i = 0; i < this.col_size.length; i++) {
          this.notAllowDrawLine.changeOpacityRow.push([0, i]);
        }
      }
      if (direction === "right") {
        for (let i = 0; i < this.row_size.length; i++) {
          this.notAllowDrawLine.changeOpacityCol.push([this.col_size.length, i]);
        }
      }
      if (direction === "bottom") {
        for (let i = 0; i < this.col_size.length; i++) {
          this.notAllowDrawLine.changeOpacityRow.push([this.row_size.length, i]);
        }
      }
      if (direction === "left") {
        for (let i = 0; i < this.row_size.length; i++) {
          this.notAllowDrawLine.changeOpacityCol.push([0, i]);
        }
      }
    }
    if (opacity === 1) {
      const map = new Map();
      if (direction === "top") {
        for (let i = 0; i < this.col_size.length; i++) {
          map.set("0" + "," + i, true);
        }
      }
      if (direction === "right") {
        for (let i = 0; i < this.row_size.length; i++) {
          map.set(this.col_size.length + "," + i, true);
        }
      }
      if (direction === "bottom") {
        for (let i = 0; i < this.col_size.length; i++) {
          map.set(this.row_size.length + "," + i, true);
        }
      }
      if (direction === "left") {
        for (let i = 0; i < this.row_size.length; i++) {
          map.set("0" + "," + i, true);
        }
      }
      if (direction === "top" || direction === "bottom") {
        for (let i = 0; i < this.notAllowDrawLine.changeOpacityRow.length; i++) {
          const str = this.notAllowDrawLine.changeOpacityRow[i].toString();
          if (map.has(str)) {
            this.notAllowDrawLine.changeOpacityRow.splice(i, 1);
            i--;
          }
        }
      }
      if (direction === "right" || direction === "left") {
        for (let i = 0; i < this.notAllowDrawLine.changeOpacityCol.length; i++) {
          const str = this.notAllowDrawLine.changeOpacityCol[i].toString();
          if (map.has(str)) {
            this.notAllowDrawLine.changeOpacityCol.splice(i, 1);
            i--;
          }
        }
      }
    }
    this.editor.refreshDocument();
  }

  // TODO 先满足薛工要求 后边再考虑 跳过中间某几列设置列宽 还有表格的合并拆分 等各种情况
  setColWidth(width: number[]){
    const originParaAnchor = [...this.editor.selection.para_anchor];
    const tableWidth = this.width;
    if (!width || width.length === 0) {
      this.editor.event.emit("message", {
        type: "warning",
        msg: "请传入设置的列宽数组",
      });
      return
    }
    let resetEachColWith = Config.min_col_size;
    let alreadySetWidth = 0;
    let flag = true;
    for (let i = 0; i < this.col_size.length; i++) {
      if (i < width.length && flag && i < this.col_size.length - 1) {
        alreadySetWidth += width[i];
        if (tableWidth - alreadySetWidth < (this.col_size.length - i - 1) * Config.min_col_size) {
          // 假设这个 col_size 按照 width 里边的分配了 那么剩下的列数 就是 (this.col_size.length - i - 1)
          // 进来这里说明当前 col_size 按照 width 分配是不合理的 所以不能按照 width 传参进行分配 要重新进行计算
          flag = false;
          resetEachColWith = (tableWidth - (alreadySetWidth - width[i])) / (this.col_size.length - i);
          this.col_size[i] = resetEachColWith < Config.min_col_size ? Config.min_col_size : resetEachColWith;
        } else {
          this.col_size[i] = width[i];
          resetEachColWith = (tableWidth - alreadySetWidth) / (this.col_size.length - i - 1);
        }
        // 这些单元格 有可能变大 也有可能变小
      } else {
        this.col_size[i] = resetEachColWith < Config.min_col_size ? Config.min_col_size : resetEachColWith;
      }
    }
    for (let i = 0; i < this.children.length; i++) {
      this.children[i].typesetting();
    }
    const modelPath = this.editor.paraPath2ModelPath(originParaAnchor);
    this.editor.selection.setCursorPosition(modelPath)
    this.editor.refreshDocument();
  }

  /**
   * 合并单元格
   * @param rowIndex 行号
   * @param retainedCellNumber 该行要保留下来的单元格数量
   */
  mergeSingleRowCells(rowIndex: number, retainedCellNumber: number) {
    const cells = this.children; // 必须得在这儿取 因为牵扯到引用类型 原先是想解耦 获取每一行的单元格 就处理该行的单元格就可以了，但是不行
    if (cells.length === retainedCellNumber) return; // 全部保留就没必要往下走了(这也只能判断创建一行表格的情况)
    const mergeNumber = this.col_size.length / retainedCellNumber; // 需要合并的单元格数量
    const unDeleteableCellMap: Map<Cell, true> = new Map(); // 因为 colspan 为 1 的时候会误删
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      if (cell.position[0] === rowIndex) {
        // 一切操作都应该是在这儿
        if (cell.position[1] % mergeNumber === 0) {
          cell.colspan = mergeNumber;
          unDeleteableCellMap.set(cell, true);
        }
      }
    }
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      if (
        cell.position[0] === rowIndex &&
        cell.colspan === 1 &&
        !unDeleteableCellMap.has(cell)
      ) {
        this.notAllowDrawLine.col.push([cell.position[1], rowIndex]);
        cells.splice(i, 1);
        i--;
      }
    }
  }

  // 获取每一行上的单元格
  getCellsOnEachRow(): Cell[][] {
    const cells: Cell[][] = [];
    let tempCells: Cell[] = [];
    let rowIndex = 0;
    for (const cell of this.children) {
      if (cell.position[0] === rowIndex) {
        tempCells.push(cell);
      } else {
        cells.push(tempCells);
        rowIndex = cell.position[0];
        tempCells = [cell];
      }
    }
    cells.push(tempCells);
    return cells;
  }

  /**
   *
   * @param datas 要追加的数据
   * @param excludes 要排除的列
   * @returns
   */
  append(
    datas: any[],
    excludes: { startColIndex: number; colspan: number }[]
  ): void {
    datas = datas.reverse();

    const map = new Map();
    excludes.forEach((it) => {
      map.set(it.startColIndex, it.colspan);
    });

    for (const cell of this.children) {
      if (cell.getStr() !== "\n" || cell.meta.appended) continue; // 如果不为空 并且已经追加了就跳过 因为有可能追加的内容是空 所以用标记来识别
      if (map.has(cell.start_col_index)) continue;
      if (!datas.length) return;
      const item = datas.pop();
      cell.meta.appended = true;
      if (item.type === "text") {
        cell.appendText(item.value);
      } else if (item.type === "field") {
        cell.appendElements([item.value]);
        if (item.newText) {
          item.value.replaceText(item.newText);
        }
      }
    }
  }

  /**
   * 格式化表格和数据
   * @param data 每个单元格中要放入的数据
   * @param widthHeight [宽, 高]
   * @param spacingWidth 间距
   */
  formData(
    data: PacsLayoutParameter[],
    numberOfCellsPerRow: number[],
    widthHeight: number[],
    spacingWidth: number,
    spacingHeight: number,
  ) {
    // TODO 先完成功能再优化吧
    this.sortingCells();
    const tableWidth = this.width;
    const [width, height] = widthHeight;

    const numberOfRows = numberOfCellsPerRow.length;
    if (numberOfRows === 1) {
      if (numberOfCellsPerRow[0] === 3) {
        this.col_size[1] = width;
        this.col_size[0] = this.col_size[2] = (tableWidth - width) / 2;
      } else if (numberOfCellsPerRow[0] === 4) {
        this.col_size[1] = this.col_size[2] = width + spacingWidth / 2;
        this.col_size[0] = this.col_size[3] =
          (tableWidth - this.col_size[1] * 2) / 2;
      }
    } else if (numberOfRows === 2) {
      const [a, b] = numberOfCellsPerRow;
      if ((a === 2 && b === 3) || (a === 3 && b === 2)) {
        this.col_size[2] = this.col_size[3] = width / 2;
        this.col_size[0] =
          this.col_size[1] =
          this.col_size[4] =
          this.col_size[5] =
          (tableWidth - width) / 4;
      } else if (a === 3 && b === 3) {
        // 不存在合并单元格的情况
        this.col_size[1] = width;
        this.col_size[0] = this.col_size[2] = (tableWidth - width) / 2;
      } else if ((a === 4 && b === 2) || (a === 2 && b === 4)) {
        this.col_size[1] = this.col_size[2] = width + spacingWidth / 2;
        this.col_size[0] = this.col_size[3] =
          (tableWidth - this.col_size[1] * 2) / 2;
      } else if ((a === 3 && b === 4) || (a === 4 && b === 3)) {
        // TODO 可以根据这个思路 修改 col_size 逐渐往两边修改 先写完这个写死的 然后再回过头来修改
        this.col_size[4] =
          this.col_size[5] =
          this.col_size[6] =
          this.col_size[7] =
          width / 4;
        this.col_size[3] = this.col_size[8] =
          width + spacingWidth - this.col_size[4] * 2;
        this.col_size[0] =
          this.col_size[1] =
          this.col_size[2] =
          this.col_size[9] =
          this.col_size[10] =
          this.col_size[11] =
          (tableWidth - width - this.col_size[3] * 2) / 6;
      } else if (a === 4 && b === 4) {
        this.col_size[1] = this.col_size[2] = width + spacingWidth;
        this.col_size[0] = this.col_size[3] =
          (tableWidth - this.col_size[1] * 2) / 2;
      }
    } else if (numberOfRows === 3) {
      const [a, b, c] = numberOfCellsPerRow;
      if (
        (a === 2 && b === 3 && c === 3) ||
        (a === 3 && b === 2 && c === 3) ||
        (a === 3 && b === 3 && c === 2)
      ) {
        // TODO 也可以根据这个找到规律 回头改
        this.col_size[2] = this.col_size[3] = width / 2;
        this.col_size[0] =
          this.col_size[1] =
          this.col_size[4] =
          this.col_size[5] =
          (tableWidth - this.col_size[3] * 2) / 4;
      } else if (a === 3 && b === 3 && c === 3) {
        this.col_size[1] = width;
        this.col_size[0] = this.col_size[2] = (tableWidth - width) / 2;
      } else if (
        (a === 2 && b === 3 && c === 4) ||
        (a === 3 && b === 2 && c === 4) ||
        (a === 4 && b === 3 && c === 2)
      ) {
        // TODO 也可以根据这个找规律
        // 先格努中间三个单元格 中间单元格的宽度计算 col_size
        this.col_size[4] =
          this.col_size[5] =
          this.col_size[6] =
          this.col_size[7] =
          width / 4;
        // 再计算下方单元格 已经计算好 4， 5， 6， 7 了所以 应该在处理 3， 8
        this.col_size[3] = this.col_size[8] =
          width + spacingWidth - this.col_size[4] * 2;
        // 最后计算 0， 1， 2 和 9， 10， 11
        this.col_size[0] =
          this.col_size[1] =
          this.col_size[2] =
          this.col_size[9] =
          this.col_size[10] =
          this.col_size[11] =
          (tableWidth - width - this.col_size[3] * 2) / 6;
      } else if (
        (a === 4 && b === 4 && c === 3) ||
        (a === 4 && b === 3 && c === 4) ||
        (a === 3 && b === 4 && c === 4) ||
        (a === 3 && b === 3 && c === 4)
      ) {
        // 先计算单元格最少的那一行 最终间的单元格合并到的 col_size
        this.col_size[4] =
          this.col_size[5] =
          this.col_size[6] =
          this.col_size[7] =
          width / 4;
        // 再计算剩下的 中间的两个单元格 合并用到的 col_size  width + spacing 是这个单元格需要的总宽度 再减去已经分配过的
        this.col_size[3] = this.col_size[8] =
          width + spacingWidth - this.col_size[4] * 2;
        this.col_size[0] =
          this.col_size[1] =
          this.col_size[2] =
          this.col_size[9] =
          this.col_size[10] =
          this.col_size[11] =
          (tableWidth - (width + spacingWidth) * 2) / 6;
      } else if (a === 4 && b === 4 && c === 4) {
        this.col_size[1] = this.col_size[2] = width + spacingWidth;
        this.col_size[0] = this.col_size[3] =
          (tableWidth - this.col_size[1] * 2) / 2;
      }
    }
    const font = this.editor.contextState.getFontState();
    for (let i = 0; i < this.children.length; i++) {
      const cell = this.children[i];
      cell.clearPadding();
      const { src, serialNum, selectField, meta } = data[i];
      const image = new Image(src, width, height, font, meta);
      this.editor.imageMap.addOnload(image);
      cell.appendElements([image]);
      (serialNum || selectField) && cell.insertEmptyParagraph(0);
      if (serialNum) {
        const serialNumField = this.editor.createElement("field") as XField;
        cell.appendElements([serialNumField]);
        serialNumField.replaceText(serialNum);
      }
      selectField && cell.appendElements([selectField]);
      const cellWidth = cell.width;
      if (numberOfRows === 1) {
        // 因为只有一行就不存在合并单元格的情况
        if (numberOfCellsPerRow[0] === 2) {
          // 在这一行只展示两个单元格的时候
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (numberOfCellsPerRow[0] === 3) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (numberOfCellsPerRow[0] === 4) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        }
      } else if (numberOfRows === 2) {
        if (spacingHeight) {
          if (cell.start_row_index === 0) {
            cell.padding_bottom = spacingHeight / 2;
          } else {
            cell.padding_top = spacingHeight / 2;
          }
        }
        const [a, b] = numberOfCellsPerRow;
        if (a === 2 && b === 2) {
          if (i === 0 || i === 2) {
            // cell.padding_left = (tableWidth - width * 2 - spacing) / 2;
            // cell.padding_left = (tableWidth / 2 - width - spacing / 2);
            cell.padding_left = cellWidth - spacingWidth - width; // 因为要设置居中 所以上边两种计算方式都不对
          } else if (i === 1 || i === 3) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 2 && b === 3) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 1) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 2) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 4) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 3) {
          if (i === 0 || i === 3) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2 || i === 5) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 2) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 2 && b === 4) {
          // i === 3 || i === 4 的时候直接就居中
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 1) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 2) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 5) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 4 && b === 2) {
          // i === 1 || i === 2 的时候就居中可以
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 5) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 4) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 6) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 4 && b === 3) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 6) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 4 && b === 4) {
          if (i === 0 || i === 4) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3 || i === 7) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        }
      } else if (numberOfRows === 3) {
        const [a, b, c] = numberOfCellsPerRow;
        if (spacingHeight) {
          if (i < a * 1) {
            cell.padding_bottom = spacingHeight / 2;
          } else if (i < a * 1 + b * 1) {
            cell.padding_top = cell.padding_bottom = spacingHeight / 2;
          } else {
            cell.padding_top = spacingHeight / 2;
          }
        }
        if (a === 2 && b === 3 && c === 3) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 1) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 2 || i === 5) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 4 || i === 7) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 2 && c === 3) {
          if (i === 0 || i === 5) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2 || i === 7) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 3 && b === 3 && c === 2) {
          if (i === 0 || i === 3) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2 || i === 5) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 6) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 7) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 3 && b === 3 && c === 3) {
          if (i === 0 || i === 3 || i === 6) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2 || i === 5 || i === 8) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 2 && b === 3 && c === 4) {
          if (i === 0 || i === 5) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 1 || i === 8) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 2) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 4) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 2 && c === 4) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3 || i === 5) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 4 || i === 8) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 4 && b === 3 && c === 2) {
          if (i === 0 || i === 7) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3 || i === 8) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 6) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 4 && b === 4 && c === 3) {
          if (i === 0 || i === 4) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3 || i === 7) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 8) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 10) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 4 && b === 3 && c === 4) {
          if (i === 0 || i === 7) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3 || i === 10) {
            cell.padding_right = cellWidth - spacingWidth - width;
          } else if (i === 4) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 6) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          }
        } else if (a === 3 && b === 4 && c === 4) {
          if (i === 0) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3 || i === 7) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 6 || i === 10) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 3 && b === 3 && c === 4) {
          if (i === 0 || i === 3) {
            cell.padding_left = cellWidth - spacingWidth * 2 - width;
          } else if (i === 2 || i === 5) {
            cell.padding_right = cellWidth - spacingWidth * 2 - width;
          } else if (i === 3 || i === 7) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 6) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 9) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        } else if (a === 4 && b === 4 && c === 4) {
          if (i === 0 || i === 4 || i === 8) {
            cell.padding_left = cellWidth - spacingWidth - width;
          } else if (i === 3 || i === 7 || i === 11) {
            cell.padding_right = cellWidth - spacingWidth - width;
          }
        }
      }
      for (const paragraph of cell.paragraph) {
        (paragraph as Paragraph).changeAlign("center");
      }
    }
  }

  // modifyColSize (numberOfCellsPerRow: number[], picWidth: number, spacing: number) {
  //   if (numberOfCellsPerRow.length === 1) {
  //     // 情况1：只有一行的情况
  //     const cellCount = numberOfCellsPerRow[0];
  //     if (cellCount === 1) return;
  //     if (cellCount === 2) {
  //       return;
  //     }
  //     if (cellCount <= 2) return;
  //   }
  //   // this.col_size = this.col_size.map(() => 0);
  //   // TODO [2, 2], [3, 4], [4, 4], [4, 3, 3], [4, 3, 4], [4, 4, 4] 的情况下 以下逻辑是不对的
  //   // 也就是说单元格最多的行 单元格数量为偶数时全部都不对 虽然还没有处理
  //   // 整体思路应该不对 不应该找单元格最多的那一行 单元格的数量 应该找最少的 先处理最少的，已经处理过的 col_size 要加上考虑 比如 [3, 4] 的情况
  //   // [3, 5] 也不对 我不应该找最多的 应该找最小的 然后尽最大可能的把最中间的列设置的尽可能的小
  //   console.log(picWidth, "打印图片宽度");
  //   // TODO pacs 每一种排版里边的图片大小都是一样的 间距都是一样的 但是做的时候不能只考虑给他做的 我先完成这种情况 等抽空再改
  //   // 我知道 col_size 是多少列 我还知道表格的宽度 我还知道 每一行显示几个单元格，那么我就知道每一行合并了几个单元格了
  //   // 应该先修改合并单元格最少最中间的列，让他的 col_size 和内容宽度保持一致，然后再往两边逐渐的修改
  //   // 应该从合并单元格最小的行开始计算 合并单元格最多的行(我就知道先修改哪几个 col_size 了，也就是最中间的单元格由几个 col_size 合并的) 中的合并单元格可以由好几个 col_size 控制
  //   // 还得保留拖拽交换位置的功能
  //   // const minCellCountOnRow = Math.min(...numberOfCellsPerRow);
  //   // console.log(minCellCountOnRow, "打印行上最少的单元格数量");
  //   // 找最多单元格的逻辑 - 不太对 - ↓↓↓↓↓↓↓↓↓↓↓↓
  //   const minCellCountOnRow = Math.min(...numberOfCellsPerRow);
  //   const even = (minCellCountOnRow % 2 === 0);
  //   const centerIndex = even ? minCellCountOnRow / 2 - 1 : Math.floor(minCellCountOnRow / 2); // 中间单元格的下标 偶数个单元格的时候 就是 centerIndex 和 centerIndex + 1
  //   const mergeColSizeCount = this.col_size.length / minCellCountOnRow; // 每行这个数都可能是不一样的
  //   const tableWidth = this.width;
  //   if (minCellCountOnRow <= 2) return; // 最多两列的就没必要分配了
  //   // 先处理中间的吧
  //   if (even) {
  //     // 处理中间单元格合并的 col_size
  //     for (let i = 0; i < mergeColSizeCount * 2; i++) {
  //       console.log("处理奇数情况下");
  //       this.col_size[mergeColSizeCount * centerIndex + i] = picWidth / mergeColSizeCount; // 简化的等差数列求和 mergeColSizeCount * centerIndex + i
  //     };
  //   } else {
  //     // 处理中间单元格合并的 col_size
  //     for (let i = 0; i < mergeColSizeCount; i++) {
  //       console.log("处理奇数情况下");
  //       this.col_size[mergeColSizeCount * centerIndex + i] = picWidth / mergeColSizeCount;
  //     };
  //     // 在其他的 col_size 从中间往外走
  //     for (let i = centerIndex + 1; i < minCellCountOnRow; i++) { // i = centerIndex + 1 跳过 处理好的中间的单元格
  //       if (i === minCellCountOnRow - 1) {
  //         console.log("此时是处理最外层左右两边的单元格的了");
  //         for (let j = 0; j < mergeColSizeCount; j++) {
  //           // 先处理右侧
  //           console.log((tableWidth - (picWidth * (minCellCountOnRow - 2))) / mergeColSizeCount, "最外侧宽度");
  //           this.col_size[mergeColSizeCount * i + j] = (tableWidth - (picWidth * (minCellCountOnRow - 2))) / (mergeColSizeCount * 2); // mergeColSizeCount * 2 是因为左右两侧对称的 不能只算一边
  //           // 再处理左侧 因为是对称的 所以用 this.col_size.length - 1 再减去 右侧的下标就是
  //           this.col_size[this.col_size.length - 1 - (mergeColSizeCount * i + j)] = (tableWidth - (picWidth * (minCellCountOnRow - 2))) / (mergeColSizeCount * 2);
  //         };
  //       } else {
  //         console.log("还没到最外层");
  //         for (let j = 0; j < mergeColSizeCount; j++) {
  //           // 先处理右侧
  //           this.col_size[mergeColSizeCount * i + j] = picWidth / mergeColSizeCount;
  //           // 再处理左侧 因为是对称的 所以用 this.col_size.length - 1 再减去 右侧的下标就是
  //           this.col_size[this.col_size.length - 1 - (mergeColSizeCount * i + j)] = picWidth / mergeColSizeCount;
  //         };
  //       }
  //     }
  //   }
  //   // 找最多单元格的逻辑 - 不太对 - ↑↑↑↑↑↑↑↑↑↑↑↑

  //   console.log(this.col_size, "打印 col_size 数组");
  //   // 还有可能比如说是 两行四列的内容 每行展示单元格数量一样 此时就要以哪个内容宽为准了
  //   // console.log(centerIndex, "中间图片的下标");
  //   // console.log(even, "偶数个");
  //   // const mergeColSizeCount = this.col_size.length / maxCellCount;
  //   // console.log(maxCellCount, "最大的单元格数量", mergeColSizeCount, "合并了几个col_size");
  //   // const tableWidth = this.width;
  //   // console.log(tableWidth, "总宽度");
  //   // console.log("修改col_size");
  //   // this.col_size = [160, 105, 50, 50, 105, 160];
  // }

  // 新的单元格合并逻辑
  /**
   * 新的单元格合并逻辑：就是保留一个单元格，将剩下选中的所有单元格内容都挪到新的单元格中去，再把除要保留的单元格以外的单元格全部删除掉就完了，最后修改一下线和 row_size, min_row_size
   * @returns boolean 是否合并成功
   */
  newMergeCells(editor: Editor): boolean {
    // 1. 先干掉了拆分成小单元格，被留下的单元格的 rowspan 和 colspan 没有计算
    // 2. 线的不显示和透明度就没有处理

    // 1.获取所有选中的单元格相关数据
    const selectedCellsInfo = editor.selection.selected_cells;
    const retainedCell = selectedCellsInfo[0].cell;
    const startParaPath = editor.selection.para_start;

    // 收集选中单元格的所有 paragraph 如果单元格内只有一个段落并且值还是\n的话 就不收集了
    const paragraphs: (Paragraph | Table)[] = [];
    const fields: XField[] = [];
    selectedCellsInfo.forEach(({ cell: current_cell }) => {
      // 处理文本域 ↓
      const current_cell_fields = current_cell.fields;
      current_cell_fields?.forEach((field) => {
        field.cell = retainedCell;
      });
      fields.push(...current_cell_fields);
      // 处理文本域 ↑
      const now_paragraph = current_cell.paragraph as Paragraph[]; // 每个单元格里边的paragraph数组 (考虑合并表格的话)里边可能会有Paragraph，也可能有Table
      const now_paragraph_length = now_paragraph.length;
      const first_character_value = now_paragraph[0].characters[0].value;
      now_paragraph.forEach((para) => {
        para.cell = retainedCell;
        // if (!(now_paragraph_length === 1 && first_character_value === "\n")) {
        //   paragraphs.push(para);
        // }
        if (now_paragraph_length !== 1 || first_character_value !== "\n") {
          // 上边的注释 可能比较好理解 跟该写法一样
          paragraphs.push(para);
        }
      });
    });
    // 修改被保留的单元格colspan 和 rowspan 并且插入数据
    // firstCell.colspan = Object.values(rows_cells_obj)[0].length; // 一行上的单元格数量
    // firstCell.rowspan = Object.values(cols_cells_obj)[0].length; // 一列上的单元格数量
    retainedCell.fields = fields;
    // 过滤掉空段落 并且赋值
    // 如果都是空格或者换行 不能都过滤掉了 也要留一个换行符
    if (paragraphs.length !== 0) {
      retainedCell.paragraph = paragraphs;
    }

    // 最后找到不要的单元格删掉 update 和 render一下就可以了
    for (let i = 0; i < this.children.length; i++) {
      for (let j = 0; j < selectedCellsInfo.length; j++) {
        if (
          this.children[i] === selectedCellsInfo[j].cell &&
          selectedCellsInfo[j] !== selectedCellsInfo[0]
        ) {
          this.children.splice(i, 1);
          i--; // 因为rootTable 是动态的
        }
      }
    }
    editor.selection.clearSelectedInfo(); // 清空选区
    retainedCell.typesetting();
    // 重置光标 在合并后的单元格末尾处
    const paragraph_index = retainedCell.paragraph.length - 1;
    const character_index =
      (retainedCell.paragraph[retainedCell.paragraph.length - 1] as Paragraph)
        .characters.length - 1;
    startParaPath.splice(
      1,
      3,
      getCellIndex(retainedCell),
      paragraph_index,
      character_index
    );
    editor.selection.setCursorPosition(
      editor.paraPath2ModelPath(startParaPath)
    );
    editor.update(...editor.getUpdateParamsByContainer(this));
    editor.render();
    return true;
  }

  contain_vertical(y: number) {
    const res1 = this.top <= y;
    const res2 = y <= this.bottom;
    return res1 && res2;
  }

  contain_horizontal(x: number) {
    const res1 = this.left <= x;
    const res2 = x <= this.right;
    return res1 && res2;
  }

  contain(x: number, y: number) {
    const res1 = this.contain_horizontal(x);
    const res2 = this.contain_vertical(y);
    return res1 && res2;
  }

  insert_raw(node: any, nextNode: any) {
    const cell = new Cell(
      this.editor,
      node.pos,
      node.colspan,
      node.rowspan,
      this
    );
    // judgeUndefinedAssign(cell, node, ["is_show_slash_up", "is_show_slash_down", "id", "lock", "style", "fixedHeight", "padding_left", "padding_right", "padding_top", "padding_bottom"]);
    Cell.attrJudgeUndefinedAssign(cell, node);
    this.children.push(cell);
    this.cellMap.set(cell.id, cell);

    for (let i = 0; i < node.children.length; i++) {
      this.editor.event.emit("transCellInsertRaw", {
        cell,
        node: node.children[i],
        nextNode: node.children[i + 1],
        groups: undefined,
        nextCell: nextNode,
      });
    }
  }

  /**
   * 重置分割属性
   */
  resetSplitAttr() {
    this.split_line_arr = [];
    this.split_parts = [];
    this.page_break = false;
    const cells = this.children;
    for (let i = 0; i < cells.length; i++) {
      cells[i].resetSplitAttr();
    }
  }

  /**
   * 给单元格排序
   * @param moveCell 要移动的单元格
   * @param targetCell 移动到的目标单元格
   * @param relativePosition 移动到目标单元格的位置
   */
  reorderCells(
    moveCell: Cell,
    targetCell: Cell,
    relativePosition: "front" | "behind" | "situ" | "swap"
  ) {
    this.sortingCells(); // 排序是为了保证 循环中替换单元格内容位置是对的
    if (relativePosition === "swap") {
      this.editor.swapCells(moveCell, targetCell);
      return;
    }
    const res = { dragIndex: 0, dropIndex: 0 };
    for (let i = 0; i < this.children.length; i++) {
      // 循环交换单元格内容
      const cell = this.children[i];
      if (cell === moveCell) {
        res.dragIndex = i;
      }
      if (cell === targetCell) {
        res.dropIndex = i;
      }
    }
    const position = moveCell.getRelativePosition(targetCell); // 先获取是往前拖还是往后拖
    if (relativePosition === "front") {
      if (position === "front") {
        // 往前拖  1、2、3、4、5  比如 把 4 拖到 2 的前边
        // 就要把 2 换成 4  把 3 换成 2 把 4 换成 3 结果是 1、4、2、3、5
        const recordDragCell = this.children[res.dragIndex].copy(); // 循环之前先记录 放置替换完 没了
        for (let i = res.dragIndex; i > res.dropIndex; i--) {
          const currentCell = this.children[i];
          const prevCell = this.children[i - 1];
          currentCell.replaceContentWith(prevCell);
          currentCell.typesetting();
        }
        targetCell.replaceContentWith(recordDragCell);
        targetCell.typesetting();
      } else if (position === "behind") {
        // 往后拖 1、2、3、4、5  比如 把 1 拖到 5 的前边
        // 就是把 1 换成了 2 然后把 2 换成了 3 最后 1 到了原来 4 的位置
        const recordDragCell = this.children[res.dragIndex].copy();
        for (let i = res.dragIndex; i < res.dropIndex - 1; i++) {
          const currentCell = this.children[i];
          const nextCell = this.children[i + 1];
          currentCell.replaceContentWith(nextCell);
          currentCell.typesetting();
        }
        this.children[res.dropIndex - 1].replaceContentWith(recordDragCell);
        this.children[res.dropIndex - 1].typesetting();
      }
    } else if (relativePosition === "behind") {
      if (position === "front") {
        // 往前拖  1、2、3、4、5  比如 把 5 拖到 1 的后边
        // 就是将 1 后边的 2 换成 5 然后原来的 2 往后挪换成了 3 结果就是 1、5、2、3、4
        const recordDragCell = moveCell.copy();
        for (let i = res.dragIndex; i > res.dropIndex + 1; i--) {
          const currentCell = this.children[i];
          const preCell = this.children[i - 1];
          currentCell.replaceContentWith(preCell);
          currentCell.typesetting();
        }
        const dropCellNext = this.children[res.dropIndex + 1];
        dropCellNext.replaceContentWith(recordDragCell);
        dropCellNext.typesetting();
      } else if (position === "behind") {
        // 往后拖 1、2、3、4、5  比如 把 1 拖到 5 的后边
        // 2 会往前挪，替换掉原来的 1，3 也会往前挪，替换掉原来的 2 最后 5 往前挪替换掉原来的 4
        // 原来的 1 就会替换掉原来的 5
        // 需要先存储一下 拖拽单元格的内容 否则他替换完了 就找不到原来的内容了 drop 的单元格 就没有内容替换了
        const tempCell = this.children[res.dragIndex].copy();
        for (let i = res.dragIndex; i < res.dropIndex; i++) {
          const currentCell = this.children[i];
          const nextCell = this.children[i + 1];
          currentCell.replaceContentWith(nextCell);
          currentCell.typesetting();
        }
        this.children[res.dropIndex].replaceContentWith(tempCell);
        this.children[res.dropIndex].typesetting();
      }
    }
  }

  /**
   * 根据某一行内容高度，设置正确的row_size
   * @param rowIndex
   * @param contentHeight
   */
  updateRowSize(rowIndex: number, contentHeight: number) {
    if (this.min_row_size[rowIndex] >= contentHeight) {
      if (this.row_size[rowIndex] < this.min_row_size[rowIndex]) {
        this.row_size[rowIndex] = this.min_row_size[rowIndex];
      }
    } else {
      if (this.row_size[rowIndex] < contentHeight) {
        this.row_size[rowIndex] = contentHeight;
      }
    }
    // 当调整表格边线高度为内容高度时，对应行的min_row_size设为配置值
    this.min_row_size[rowIndex] === contentHeight &&
      (this.min_row_size[rowIndex] = Config.min_row_size);
    this.overflow();
  }

  overflow() {
    // this.parent.updateRowBounding(this.cell_index);
  }

  /**
   * 光标移动到可调整cell的边界处的处理
   * @param cell
   * @param direction
   */
  onResizableCellBounding(cell: Cell, direction: Direction, editor: Editor) {
    if (this.tableFiexedStyle) return;
    // 分组锁定的情况下 不能拖动表格线 ↓↓↓↓↓
    const group_id = this.group_id;
    if (group_id) {
      const group = editor.selection.getGroupByGroupId(group_id);
      const isLock = group?.lock;
      if (isLock) {
        return;
      }
    }
    if (
      cell.editor.internal.draw_shape === ShapeMode.Line ||
      cell.editor.internal.draw_shape === ShapeMode.Circle ||
      cell.editor.internal.draw_shape === ShapeMode.Cross ||
      cell.editor.internal.draw_shape === ShapeMode.Rect
    )
      return;
    // 分组锁定的情况下 不能拖动表格线 ↑↑↑↑↑

    editor.internal.resize_cell === cell ||
      (editor.internal.resize_cell = cell);

    editor.internal.resize_cell_position === direction ||
      (editor.internal.resize_cell_position = direction);

    const cursor_style =
      direction === Direction.up || direction === Direction.down
        ? "n-resize"
        : "e-resize";
    Renderer.getCanvasDom()!.style.cursor = editor.internal.cursor_state.type =
      cursor_style;
  }

  // 新插入列的宽度是不变的 但是递归计算时 因为已经分配出去了一些宽度 所以剩余分配宽度 应该是重新计算的
  changeTableColSize(col_size: number, min_col_size: number, col_num: number) {
    let rest_col_size = col_size;
    // 计算宽度占比
    let total = 0;
    this.col_size.forEach((current) => {
      if (current > min_col_size) {
        total += current;
      }
    });
    for (let i = 0, len = this.col_size.length; i < len; i++) {
      const current_col_size = this.col_size[i];
      if (current_col_size > min_col_size) {
        // 宽度大于20的才需要修改
        const should_sub_width = (current_col_size / total) * col_size; // 当前单元格应该减小的宽度
        if (current_col_size - should_sub_width < min_col_size) {
          // 如果当前宽度减去 应该减少的宽度后小于20了 就直接赋值20
          rest_col_size -= this.col_size[i] - min_col_size;
          this.col_size[i] = min_col_size;
        } else {
          this.col_size[i] -= should_sub_width;
          rest_col_size -= should_sub_width;
        }
      }
    }
    // 应该在此处递归调用 因为要走完一个循环 都减少一遍 这样比较匀称
    if (rest_col_size > 0 && rest_col_size / col_num > min_col_size) {
      this.changeTableColSize(rest_col_size, min_col_size, col_num);
    }
  }

  /**
   * 表格末尾插入指定行数
   * @param row_num 插入的行数
   */
  addRows({ row_num = 1 }: { row_num?: number } = { row_num: 1 }) {
    row_num = row_num * 1;
    // 找到行号最小的单元格 在该单元格下方插入1行
    const copy_cells = this.children.map((cell) => cell.copy(this));
    const last_row_cell = copy_cells.sort(
      (a, b) => a.position[0] - b.position[0]
    )[copy_cells.length - 1];

    for (let i = 0, len = copy_cells.length; i < len; i++) {
      const cell = copy_cells[i];
      const end_row_index = cell.end_row_index; // 该单元格 末尾行的行号

      for (let j = 0; j < row_num; j++) {
        if (end_row_index === last_row_cell.end_row_index) {
          // 末尾行号 和 初始的末尾行号一致 就要new出来新的 单元格
          const new_cell = new Cell(
            this.editor,
            [last_row_cell.end_row_index + 1 + j, copy_cells[i].position[1]], // position 因为是下方插入行，所以新单元格行号+1 列号不变 原单元格行号和列号都不变
            cell.colspan,
            1,
            this
          );
          this.children.push(new_cell);
          this.cellMap.set(new_cell.id, new_cell);
          new_cell.insertEmptyParagraph();
        }
      }
    }
    this.min_row_size.splice(
      last_row_cell.end_row_index + 1,
      0,
      ...Array(row_num).fill(Config.min_row_size)
    );
    this.row_size.splice(
      last_row_cell.end_row_index + 1,
      0,
      ...Array(row_num).fill(Config.min_row_size)
    );
    this.children.forEach((cell) => cell.typesetting());
  }

  /**
   * 表格最右侧插入指定的列数
   * @param col_num 插入的列数
   * @param editor editor对象
   */
  addCols({ col_num = 1, editor }: { col_num?: number; editor: Editor }) {
    col_num = col_num * 1;
    const copy_cells = this.children.map((cell) => cell.copy(this));
    const last_row_cell = copy_cells.sort(
      (a, b) => a.position[1] - b.position[1]
    )[copy_cells.length - 1]; // 列数最大的单元格 一定是在该单元格右边插入几列

    const current_col_num = this.col_size.length; // 该表格 现在有多少列

    const tbl_width =
      editor.page_size.width -
      editor.config.page_padding_left -
      editor.config.page_padding_right;
    const new_col_size = tbl_width / (current_col_num + col_num); // 加上新单元格后 每个单元格的平均宽度(不是每个单元格的实际宽度) 也就是新插入列的宽度
    // 新插入的列 宽度都不到配置的最小宽度了 就不允许插入了
    if (new_col_size < Config.min_col_size) {
      return false;
    }

    for (let i = 0; i < copy_cells.length; i++) {
      const cell = copy_cells[i];
      const end_col_index = cell.end_col_index; // 该单元格 末尾列的 列号

      for (let k = 0; k < col_num; k++) {
        if (end_col_index === last_row_cell.end_col_index) {
          // 起始列号 和 初始的 起始列号 一致的单元格数量 rowspan 的总和 为要new出来的单元格数量
          for (let j = 0; j < cell.rowspan; j++) {
            const new_cell = new Cell(
              this.editor,
              [
                copy_cells[i].position[0] + j,
                last_row_cell.end_col_index + 1 + k,
              ],
              1,
              1,
              this
            );
            this.children.push(new_cell);
            this.cellMap.set(new_cell.id, new_cell);
            new_cell.insertEmptyParagraph();
          }
        }
      }
    }

    this.changeTableColSize(
      new_col_size * col_num,
      Config.min_col_size,
      col_num
    );
    this.col_size.splice(
      last_row_cell.end_col_index + 1,
      0,
      ...Array(col_num).fill(new_col_size)
    );
  }

  /**
   * 添加行和列
   * @param cursor_path 光标路径 如果是选区的话 就是起点路径(selection.start)
   * @param direction 插入的方向 上下左右
   * @param editor Editor
   * @returns 没有返回值
   */
  addRowOrCol(cursor_path: number[], direction: Direction, editor: Editor) {
    const source = editor.internal.sourceOfAddRowOrCol ?? SourceOfAddRowOrCol.INVOKE;
    const para_path = editor.modelPath2ParaPath(cursor_path); // 转为段落的路径 // ①：第几个表格 ②：第几个cell ③：第几个段落 ④：第几个character

    const origin_cells = this.children; // 光标所在表格内的所有单元格
    const copied_cells = origin_cells.map((cell) => {
      const copiedCell = cell.copy(this);
      for (const field of copiedCell.fields) {
        field.reviseAttr({ id: uuid("field") })
      }
      return copiedCell
    });

    const copied_cell_with_caret = copied_cells[cursor_path[1]]; // 光标所在的单元格
    origin_cells[cursor_path[1]].is_with_caret = true; // 记住插入行和列之前 光标所在的单元格

    const init_start_row_index = copied_cell_with_caret.start_row_index; // 初始 起始行
    const init_end_row_index = copied_cell_with_caret.end_row_index; // 初始 末尾行

    const init_start_col_index = copied_cell_with_caret.start_col_index; // 初始 起始列
    const init_end_col_index = copied_cell_with_caret.end_col_index; // 初始 末尾列

    const new_cells = []; // 保存new出来的新单元格的数组

    // 处理表格线的显示隐藏 ↓  思路：也是根据插入方向，一次插入是有的坐标+1， 有的坐标-1(不是这么简单，插入行和列，本质上就单个单元格来看，是多画了一条横线和两条竖线，但是有可能是合并单元格，这些线，有的也不应该画出来,就是这三条线一条都不画的情况也是有的)
    // 不该画的线，就不应该画出来，透明度，插入行和列 不管方向 都依赖于上方和左侧的线的透明度
    // 首先要先获取光标所在单元格的 起始行 末尾行 起始列和末尾列 就是上方的init_xxx
    const undrawRow = this.notAllowDrawLine.row;
    const undrawCol = this.notAllowDrawLine.col;
    const changeOpacityRow = this.notAllowDrawLine.changeOpacityRow;
    const changeOpacityCol = this.notAllowDrawLine.changeOpacityCol;
    // 处理表格线的显示隐藏 ↑

    // 在上方插入一行
    if (direction === Direction.up) {
      // 处理表格线的显示隐藏 ↓↓
      // 处理原来就有的线的编号 横线上边的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↓
      undrawRow.forEach((arr) => {
        if (arr[0] >= init_start_row_index) {
          // 当前横线往下的横坐标都要加1
          arr[0] += 1;
        }
      });
      undrawCol.forEach((arr) => {
        // 同样横线往下的列的 横坐标 也都要加1
        if (arr[1] >= init_start_row_index) {
          arr[1] += 1;
        }
      });
      const new_add_opacity_row_line: number[][] = [];
      changeOpacityRow.forEach((arr) => {
        if (arr[0] === init_start_row_index) {
          new_add_opacity_row_line.push([arr[0], arr[1]]);
        }
        if (arr[0] >= init_start_row_index) {
          arr[0] += 1;
        }
      });
      changeOpacityRow.push(...new_add_opacity_row_line);

      const new_add_opacity_col_line: number[][] = [];
      changeOpacityCol.forEach((arr) => {
        if (arr[1] === init_start_row_index) {
          new_add_opacity_col_line.push([arr[0], arr[1]]);
        }
        if (arr[1] >= init_start_row_index) {
          arr[1] += 1;
        }
      });
      changeOpacityCol.push(...new_add_opacity_col_line);
      // 处理原来就有的线的编号 横线上边的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↑
      // 处理表格线的显示隐藏 ↑↑
      for (let i = 0, len = copied_cells.length; i < len; i++) {
        const origin_cell = origin_cells[i]; // 修改的就是原来的cell
        const start_row_index = origin_cell.start_row_index; // 该单元格 起始行的行号
        const end_row_index = origin_cell.end_row_index; // 该单元格 末尾行的行号

        // 所有竖线的透明度 都依赖于上方竖线的透明度 ↓
        for (let j = 0; j < this.col_size.length; j++) {
          if (
            changeOpacityCol.find(
              (item) =>
                item[0] === copied_cells[i].position[1] + j &&
                item[1] === init_start_row_index - 1
            )
          ) {
            changeOpacityCol.push([
              copied_cells[i].position[1] + j,
              init_start_row_index,
            ]);
          }
        }
        // 所有竖线的透明度 都依赖于上方竖线的透明度 ↑

        if (start_row_index === init_start_row_index) {
          // 找到所有起始行一样的单元格 这里new出新的单元格 并对新单元格 做各种配置修改
          // 新单元格的位置 行和列跟该单元格一致
          let new_cell: Cell;
          const config_carry_data = editor.config.insert_row_carry_data;
          if (
            config_carry_data === InsertRowCarryDataType.all ||
            config_carry_data === InsertRowCarryDataType.clearField
          ) {
            new_cell = copied_cells[i];
            new_cell.id = uuid("cell");
            new_cell.position = [
              copied_cells[i].position[0],
              copied_cells[i].position[1],
            ];
            new_cell.colspan = origin_cell.colspan;
            new_cell.rowspan = 1;
            config_carry_data === InsertRowCarryDataType.clearField &&
              editor.clearFieldsOnly(new_cell.fields);
          } else {
            new_cell = new Cell(
              this.editor,
              [copied_cells[i].position[0], copied_cells[i].position[1]], // position
              origin_cell.colspan, // colspan
              1, // rowspan
              this // parent
            );
            new_cell.insertEmptyParagraph();
          }

          // 新new出来的单元格里边的所有线 都是不画的
          const notAllowDrawLine = new_cell.getInsideLineNumber();
          const notAllowColLine = notAllowDrawLine.res_cols;
          undrawCol.push(...notAllowColLine);
          origin_cell.position[0]++; // 该单元格 行号+1 列号不变
          new_cells.push(new_cell);
          editor.event.emit("addRowOrCol", { direction, cell: new_cell, source });
        } else if (
          end_row_index >= init_start_row_index &&
          start_row_index < init_start_row_index
        ) {
          // 末尾行号大于等于起始行，并且起始行小于初始起始行 的这些单元格 要合并 新单元格
          origin_cell.rowspan++;
          // 合并单元格 处理表格线的显示隐藏 ↓ 新出来的横线 默认就应该是要画的(但是遇到合并单元格就不能画了),透明度就不管了 竖线(遇到合并单元格不该画的也不能画了，透明度应该依赖于上方的最近的竖线)
          for (let j = 0; j < origin_cell.colspan; j++) {
            // colspan的数量就是新添加的横线的数量
            undrawRow.push([
              init_start_row_index,
              copied_cells[i].position[1] + j,
            ]); // 不画的横线
            if (j > 0) {
              undrawCol.push([
                copied_cells[i].position[1] + j,
                init_start_row_index,
              ]); // 不画的竖线
            }
          }
          // 合并单元格 处理表格线的显示隐藏 ↑
        } else if (start_row_index > init_start_row_index) {
          origin_cell.position[0]++;
        }
      }
      this.min_row_size.splice(init_start_row_index, 0, Config.min_row_size);
      this.row_size.splice(init_start_row_index, 0, Config.min_row_size); // 这里min_row_size随便写 会被typesetting方法重置
    } else if (direction === Direction.down) {
      // 处理表格线的显示隐藏 ↓↓
      // 处理原来就有的线的编号 横线上边的和等于横线的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↓
      undrawRow.forEach((arr) => {
        if (arr[0] > init_end_row_index) {
          // 当前横线往下的横坐标都要加1
          arr[0] += 1;
        }
      });
      undrawCol.forEach((arr) => {
        // 同样横线往下的列的 横坐标 也都要加1
        if (arr[1] > init_end_row_index) {
          arr[1] += 1;
        }
      });

      const new_add_opacity_row_line: number[][] = [];
      changeOpacityRow.forEach((arr) => {
        if (arr[0] === init_end_row_index + 1) {
          // 在下方插入 光标显示隐藏依赖于 光标位置处 那一行单元格的下边线
          new_add_opacity_row_line.push([init_end_row_index + 1, arr[1]]);
        }
        if (arr[0] > init_end_row_index) {
          arr[0] += 1;
        }
      });
      changeOpacityRow.push(...new_add_opacity_row_line);
      changeOpacityCol.forEach((arr) => {
        if (arr[1] > init_end_row_index) {
          arr[1] += 1;
        }
      });
      // 处理原来就有的线的编号 横线上边的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↑
      // 处理表格线的显示隐藏 ↑↑
      for (let i = 0, len = copied_cells.length; i < len; i++) {
        const origin_cell = origin_cells[i];
        const start_row_index = origin_cell.start_row_index; // 该单元格 起始行的行号
        const end_row_index = origin_cell.end_row_index; // 该单元格 末尾行的行号

        // 所有竖线的透明度 都依赖于上方竖线的透明度 ↓
        for (let j = 0; j < this.col_size.length; j++) {
          if (
            changeOpacityCol.find(
              (item) =>
                item[0] === copied_cells[i].position[1] + j &&
                item[1] === init_end_row_index
            )
          ) {
            changeOpacityCol.push([
              copied_cells[i].position[1] + j,
              init_end_row_index + 1,
            ]);
          }
        }
        // 所有竖线的透明度 都依赖于上方竖线的透明度 ↑

        if (end_row_index === init_end_row_index) {
          // 末尾行号 和 初始的末尾行号一致 就要new出来新的 单元格
          let new_cell: Cell;
          const carryDataType = editor.config.insert_row_carry_data;
          if (
            carryDataType === InsertRowCarryDataType.all ||
            carryDataType === InsertRowCarryDataType.clearField
          ) {
            new_cell = copied_cells[i];
            new_cell.id = uuid("cell"); // 重新赋值 id 否则表格分页的时候 插入一行会报错
            new_cell.position = [
              init_end_row_index + 1,
              copied_cells[i].position[1],
            ];
            new_cell.colspan = origin_cell.colspan;
            new_cell.rowspan = 1;
            carryDataType === InsertRowCarryDataType.clearField &&
              editor.clearFieldsOnly(new_cell.fields);
          } else {
            new_cell = new Cell(
              this.editor,
              [init_end_row_index + 1, copied_cells[i].position[1]], // position 因为是下方插入行，所以新单元格行号+1 列号不变 原单元格行号和列号都不变
              origin_cell.colspan,
              1,
              this
            );
            new_cell.insertEmptyParagraph();
          }
          // 新new出来的单元格里边的所有线都是不画的
          const notAllowDrawLine = new_cell.getInsideLineNumber();
          const notAllowColLine = notAllowDrawLine.res_cols;
          undrawCol.push(...notAllowColLine);
          new_cells.push(new_cell);
          editor.event.emit("addRowOrCol", { direction, cell: new_cell, source });
        } else if (
          start_row_index <= init_start_row_index &&
          end_row_index > init_end_row_index
        ) {
          // 这些单元格应该合并新单元格
          origin_cell.rowspan++;
          // 合并单元格 处理表格线的显示隐藏 ↓ 新出来的横线 默认就应该是要画的(但是遇到合并单元格就不能画了),透明度就不管了 竖线(遇到合并单元格不该画的也不能画了，透明度应该依赖于上方的最近的竖线)
          for (let j = 0; j < origin_cell.colspan; j++) {
            // colspan的数量就是新添加的横线的数量
            undrawRow.push([
              init_end_row_index + 1,
              copied_cells[i].position[1] + j,
            ]); // 不画的横线
            if (j > 0) {
              undrawCol.push([
                copied_cells[i].position[1] + j,
                init_end_row_index + 1,
              ]); // 不画的竖线
            }
          }
          // 合并单元格 处理表格线的显示隐藏 ↑
        } else if (start_row_index > init_end_row_index) {
          // 如果起始行 大于 初始末尾行 这些单元格 行号应该+1 注意：不是剩余的所有单元格行号都变化 还是要加判断的
          origin_cell.position[0]++;
        }
      }

      this.min_row_size.splice(init_end_row_index + 1, 0, Config.min_row_size);
      this.row_size.splice(init_end_row_index + 1, 0, Config.min_row_size);
    } else {
      const max_cols = this.col_size.length; // 该表格 现在有多少列

      const tbl_width =
        editor.page_size.width -
        editor.config.page_padding_left -
        editor.config.page_padding_right;

      const new_col_size = tbl_width / (max_cols + 1); // 加上新单元格后 每个单元格的平均宽度(不是每个单元格的实际宽度) 也就是新插入列的宽度

      // 新插入的列 宽度都不到配置的最小宽度了 就不允许插入了
      if (new_col_size < Config.min_col_size) {
        return false;
      }
      if (direction === Direction.left) {
        // 处理表格线的显示隐藏 ↓↓
        // 处理原来就有的线的编号 竖线(当前单元格起始列的那条竖线)左边的 就不管了  右边的(列坐标大于等于起始列) 不管横线还是竖线 列的坐标 都要+1 ↓
        undrawRow.forEach((arr) => {
          if (arr[1] >= init_start_col_index) {
            arr[1] += 1;
          }
        });
        undrawCol.forEach((arr) => {
          if (arr[0] >= init_start_col_index) {
            arr[0] += 1;
          }
        });

        const new_add_opacity_row_line: number[][] = [];
        changeOpacityRow.forEach((arr) => {
          if (arr[1] === init_start_col_index) {
            new_add_opacity_row_line.push([...arr]);
          }
          if (arr[1] >= init_start_col_index) {
            arr[1] += 1;
          }
        });
        changeOpacityRow.push(...new_add_opacity_row_line);
        const new_add_opacity_col_line: number[][] = [];
        changeOpacityCol.forEach((arr) => {
          if (arr[0] === init_start_col_index) {
            new_add_opacity_col_line.push([arr[0], arr[1]]);
          }

          if (arr[0] >= init_start_col_index) {
            arr[0] += 1;
          }
        });
        changeOpacityCol.push(...new_add_opacity_col_line);
        // 处理原来就有的线的编号 横线上边的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↑
        // 处理表格线的显示隐藏 ↑↑
        for (let i = 0, len = copied_cells.length; i < len; i++) {
          const cell = origin_cells[i];
          const start_col_index = cell.start_col_index; // 该单元格 起始列的 列号
          const end_col_index = cell.end_col_index; // 该单元格 末尾列的 列号

          // 所有横线的透明度 都依赖于左侧横线的透明度 ↓
          for (let j = 0; j < this.row_size.length; j++) {
            if (
              changeOpacityRow.find(
                (item) =>
                  item[0] === copied_cells[i].position[1] + j &&
                  item[1] === init_start_col_index - 1
              )
            ) {
              changeOpacityRow.push([
                copied_cells[i].position[1] + j,
                init_start_col_index,
              ]);
            }
          }
          // 所有横线的透明度 都依赖于左侧横线的透明度 ↑

          // 末尾列号比当前单元格起始列号小的 单元格列号 不需要改变
          // 起始列号和初始列号一致的单元格列号 和 起始列号比当前单元格 末尾列号大的单元格 列号要+1
          if (start_col_index === init_start_col_index) {
            // 起始列号 和 初始的 起始列号 一致的单元格数量 rowspan的总和 为要new出来的单元格数量
            for (let j = 0, length = copied_cells[i].rowspan; j < length; j++) {
              // 列号跟该单元格一致 然后该单元格的列号+1 行号应该每new出来一个 行号就+1
              const new_cell = new Cell(
                this.editor,
                [copied_cells[i].position[0] + j, init_start_col_index],
                1,
                1,
                this
              );
              new_cell.insertEmptyParagraph();
              new_cells.push(new_cell);
              editor.event.emit("addRowOrCol", { direction, cell: new_cell, source });
            }
            cell.position[1]++;
          } else if (
            start_col_index < init_start_col_index &&
            end_col_index >= init_start_col_index
          ) {
            // 这些单元格应该合并新单元格
            cell.colspan++;
            // 合并单元格 处理表格线的显示隐藏 ↓ 新出来的横线 默认就应该是要画的(但是遇到合并单元格就不能画了),透明度就不管了 竖线(遇到合并单元格不该画的也不能画了，透明度应该依赖于上方的最近的竖线)
            for (let j = 0; j < cell.rowspan; j++) {
              // rowspan的数量就是新添加的竖线的数量
              undrawCol.push([
                init_start_col_index,
                copied_cells[i].position[0] + j,
              ]); // 不画的横线
              if (j > 0) {
                undrawRow.push([
                  copied_cells[i].position[0] + j,
                  init_start_col_index,
                ]); // 不画的横线
              }
            }
            // 合并单元格 处理表格线的显示隐藏 ↑
          } else if (start_col_index > init_start_col_index) {
            // 如果起始列 大于 初始末尾列 这些单元格 行号应该+1
            cell.position[1]++;
          }
        }

        // 插入列的话，宽度应该需要计算了 只需要修改col_size即可
        this.changeTableColSize(new_col_size, Config.min_col_size, 1);
        this.col_size.splice(init_start_col_index, 0, new_col_size);
      } else if (direction === Direction.right) {
        // 处理表格线的显示隐藏 ↓↓
        // 处理原来就有的线的编号 竖线(当前单元格起始列的那条竖线)左边的 就不管了  右边的(列坐标大于起始列) 不管横线还是竖线 列的坐标 都要+1 ↓
        undrawRow.forEach((arr) => {
          if (arr[1] > init_end_col_index) {
            arr[1] += 1;
          }
        });
        undrawCol.forEach((arr) => {
          if (arr[0] > init_end_col_index) {
            arr[0] += 1;
          }
        });

        changeOpacityRow.forEach((arr) => {
          if (arr[1] > init_end_col_index) {
            arr[1] += 1;
          }
        });

        const new_add_opacity_col_line: number[][] = [];
        changeOpacityCol.forEach((arr) => {
          if (arr[0] === init_end_col_index + 1) {
            new_add_opacity_col_line.push([arr[0], arr[1]]);
          }
          if (arr[0] > init_end_col_index) {
            arr[0] += 1;
          }
        });
        changeOpacityCol.push(...new_add_opacity_col_line);
        // 处理原来就有的线的编号 横线上边的 就不管了  下边的 不管横线还是竖线 横坐标 都要+1 ↑
        // 处理表格线的显示隐藏 ↑↑
        for (let i = 0; i < copied_cells.length; i++) {
          const cell = origin_cells[i];
          const start_col_index = cell.start_col_index; // 该单元格 起始列的 列号
          const end_col_index = cell.end_col_index; // 该单元格 末尾列的 列号

          // 所有横线的透明度 都依赖于左侧横线的透明度 ↓
          for (let j = 0; j < this.row_size.length; j++) {
            if (
              changeOpacityRow.find(
                (item) =>
                  item[0] === copied_cells[i].position[0] + j &&
                  item[1] === init_end_col_index
              )
            ) {
              changeOpacityRow.push([
                copied_cells[i].position[0] + j,
                init_end_col_index + 1,
              ]);
            }
          }
          // 所有横线的透明度 都依赖于左侧横线的透明度 ↑

          // 末尾列号比当前单元格起始列号小的 单元格列号 不需要改变
          // 末尾列号和初始末尾列号一致的单元格列号 和 起始列号比当前单元格 末尾列号大的单元格 列号要+1
          if (end_col_index === init_end_col_index) {
            // 起始列号 和 初始的 起始列号 一致的单元格数量 rowspan 的总和 为要new出来的单元格数量
            for (let j = 0; j < cell.rowspan; j++) {
              const new_cell = new Cell(
                this.editor,
                [copied_cells[i].position[0] + j, init_end_col_index + 1],
                1,
                1,
                this
              );
              new_cell.insertEmptyParagraph();
              new_cells.push(new_cell);
              editor.event.emit("addRowOrCol", { direction, cell: new_cell, source });
            }
          } else if (
            start_col_index <= init_end_col_index &&
            end_col_index > init_end_col_index
          ) {
            // 这些单元格应该合并新单元格
            cell.colspan++;
            // 合并单元格 处理表格线的显示隐藏 ↓ 新出来的横线 默认就应该是要画的(但是遇到合并单元格就不能画了),透明度就不管了 竖线(遇到合并单元格不该画的也不能画了，透明度应该依赖于上方的最近的竖线)
            for (let j = 0; j < cell.rowspan; j++) {
              // rowspan的数量就是新添加的竖线的数量
              undrawCol.push([
                init_end_col_index + 1,
                copied_cells[i].position[0] + j,
              ]); // 不画的竖线
              if (j > 0) {
                undrawRow.push([
                  copied_cells[i].position[0] + j,
                  init_end_col_index + 1,
                ]); // 不画的横线
              }
            }
            // 合并单元格 处理表格线的显示隐藏 ↑
          } else if (start_col_index > init_end_col_index) {
            // 如果起始行 大于 初始末尾行 这些单元格 行号应该+1
            cell.position[1]++;
          }
        }

        this.changeTableColSize(new_col_size, Config.min_col_size, 1);
        // 插入列的话，宽度应该需要计算了 只需要修改col_size即可
        this.col_size.splice(init_end_col_index + 1, 0, new_col_size);
      }
    }

    this.children = [...origin_cells, ...new_cells];
    this.sortingCells();
    // 更新光标位置 ↓↓↓↓↓↓↓↓↓
    // 因为光标在段落里边的位置不能变，所以要转为para_path再转回来，直接修改cursor_path不对，因为会typesetting 第几个row和第几个character可能会变
    let index = 0; // 记录的第几个cell
    this.children.forEach((cell, i) => {
      cell.typesetting();
      if (cell.is_with_caret) {
        index = i;
        cell.is_with_caret = null;
      }
    });
    para_path[1] = index;
    const model_path = editor.paraPath2ModelPath(para_path);
    editor.selection.setCursorPosition([...model_path]);
    editor.internal.sourceOfAddRowOrCol = undefined;
    // 更新光标位置 ↑↑↑↑↑↑↑↑↑↑
  }

  // 删除整行  同时也要把表格里边记录的透明度和不画的线都删除掉，否则再插入的时候好像就不大对了
  deleteRowByCursor(model_path: number[], editor: Editor) {
    // 新逻辑 一切操作的都应该是 modelData 传入的也是 model_path
    const children = this.children;
    const currentCell = children[model_path[1]];
    let initStartRowIndex = currentCell.start_row_index;
    let initEndRowIndex = currentCell.end_row_index;
    let topCell = currentCell;
    let bottomCell = currentCell;
    const { selection } = editor;
    if (!selection.isCollapsed) {
      const selectedCells = selection.selected_cells; // 分页也没关系 这里边都是 model 里边的单元格
      for (const { cell } of selectedCells) {
        const startRowIndex = cell.start_row_index;
        const endRowIndex = cell.end_row_index;
        if (startRowIndex < initStartRowIndex) {
          initStartRowIndex = startRowIndex;
          topCell = cell;
        }
        if (endRowIndex > initEndRowIndex) {
          initEndRowIndex = endRowIndex;
          bottomCell = cell;
        }
      }
    }

    if (children.find(cell => cell.start_row_index >= initStartRowIndex && cell.end_row_index <= initEndRowIndex && cell.lock)) return false;

    this.notAllowDrawLine.row = [];
    this.notAllowDrawLine.col = [];
    const optRowMap = new Map();
    const optColMap = new Map();
    for (let i = 0; i < this.notAllowDrawLine.changeOpacityRow.length; i++) {
      const l = this.notAllowDrawLine.changeOpacityRow[i].toString();
      if (!optRowMap.has(l)) {
        optRowMap.set(l, l);
      }
    }
    for (let i = 0; i < this.notAllowDrawLine.changeOpacityCol.length; i++) {
      const l = this.notAllowDrawLine.changeOpacityCol[i].toString();
      if (!optColMap.has(l)) {
        optColMap.set(l, l);
      }
    }

    const newOptRowMap = new Map();
    const newOptColMap = new Map();

    let cursorTopCell;
    let cursorBottomCell;
    const deleteRowTotalNum = initEndRowIndex - initStartRowIndex + 1;
    for (let i = 0; i < children.length;) {
      const c = children[i];
      const sRowIndex = c.start_row_index;
      const eRowIndex = c.end_row_index;
      if (sRowIndex >= initStartRowIndex && eRowIndex <= initEndRowIndex) {
        children.splice(i, 1);
        continue;
      }
      if (c.end_row_index === topCell.start_row_index - 1 && c.start_col_index <= topCell.start_col_index && c.end_col_index >= topCell.start_col_index) {
        cursorTopCell = c;
      }
      if (c.start_row_index === bottomCell.end_row_index + 1 && c.start_col_index <= bottomCell.start_col_index && c.end_col_index >= bottomCell.start_col_index) {
        cursorBottomCell = c;
      }
      const { top, right, bottom, left } = c.getNumbersOfFourLine();
      if (sRowIndex < initStartRowIndex && eRowIndex > initEndRowIndex) {
        // 完整跨过被删除行的单元格
        for (let j = 0; j < top.length; j++) {
          // 在上边的线 只要存在就要保留
          const l = top[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            bottom[j][0] -= deleteRowTotalNum;
            l = bottom[j].toString();
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][1] >= initStartRowIndex && left[j][1] <= initEndRowIndex) continue
          let l = left[j].toString();
          if (optColMap.has(l)) {
            if (left[j][1] > initEndRowIndex) {
              left[j][1] -= deleteRowTotalNum;
            }
            l = left[j].toString();
            newOptColMap.set(l, l);
          }
        }

        for (let j = 0; j < right.length; j++) {
          if (right[j][1] >= initStartRowIndex && right[j][1] <= initEndRowIndex) continue;
          let l = right[j].toString();
          if (optColMap.has(l)) {
            if (right[j][1] > initEndRowIndex) {
              right[j][1] -= deleteRowTotalNum;
            }
            l = right[j].toString();
            newOptColMap.set(l, l);
          }
        }

        c.rowspan -= deleteRowTotalNum;
      } else if (sRowIndex < initStartRowIndex && eRowIndex >= initStartRowIndex) {
        // 只跨过起始行的单元格
        const deleteRowNum = eRowIndex - initStartRowIndex + 1;

        for (let j = 0; j < top.length; j++) {
          const l = top[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            bottom[j][0] -= deleteRowNum;
            l = bottom[j].toString();
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][1] >= initStartRowIndex && left[j][1] <= initEndRowIndex) continue;
          let l = left[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          if (right[j][1] >= initStartRowIndex && right[j][1] <= initEndRowIndex) continue;
          let l = right[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }

        c.rowspan -= deleteRowNum;
      } else if (sRowIndex <= initEndRowIndex && eRowIndex > initEndRowIndex) {
        c.clear();
        // 只跨过结束行的单元格
        const deleteRowNum = initEndRowIndex - sRowIndex + 1;

        for (let j = 0; j < top.length; j++) {
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            top[j][0] -= (deleteRowTotalNum - deleteRowNum);
            l = top[j].toString();
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            bottom[j][0] -= deleteRowNum;
            l = bottom[j].toString();
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][1] >= initStartRowIndex && left[j][1] <= initEndRowIndex) continue;
          let l = left[j].toString();
          if (optColMap.has(l)) {
            left[j][1] -= deleteRowNum;
            l = left[j].toString();
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          if (right[j][1] >= initStartRowIndex && right[j][1] <= initEndRowIndex) continue;
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][1] -= deleteRowNum;
            l = right[j].toString();
            newOptColMap.set(l, l);
          }
        }

        c.rowspan -= deleteRowNum;
        c.position[0] = c.position[0] - (deleteRowTotalNum - deleteRowNum); // 这个比删除列那个计算好理解
      } else if (sRowIndex > initStartRowIndex) {
        console.log(c.getStr());
        // 在被删除行下方的单元格
        for (let j = 0; j < top.length; j++) {
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            top[j][0] -= deleteRowTotalNum;
            l = top[j].toString();
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            bottom[j][0] -= deleteRowTotalNum;
            l = bottom[j].toString();
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          let l = left[j].toString();
          if (optColMap.has(l)) {
            left[j][1] -= deleteRowTotalNum;
            l  = left[j].toString();
            newOptColMap.set(l,l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][1] -= deleteRowTotalNum;
            l  = right[j].toString();
            newOptColMap.set(l,l);
          }
        }

        c.position[0] -= deleteRowTotalNum;
      } else {
        // 剩下的就是在删除行上方的单元格 只有处理表格透明度的线的时候才需要走这里
        for (let j = 0; j < top.length; j++) {
          const l = top[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          const l = bottom[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < left.length; j++) {
          const l = left[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          const l = right[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
      }

      if (c.rowspan !== 1 || c.colspan !== 1) {
        const { rows, cols } = c.getInnerUndrawnLines();
        this.notAllowDrawLine.row.push(...rows);
        this.notAllowDrawLine.col.push(...cols);
      }

      i++;
    }

    if (!children.length) {
      this.remove();
      if (!editor.current_cell.children[model_path[0]]) {
        selection.setCursorPosition([model_path[0] - 1 < 0 ? 0 : model_path[0] - 1, 0]);
      } else {
        selection.setCursorPosition([model_path[0], 0]);
      }
      return true;
    }
    this.row_size.splice(initStartRowIndex, deleteRowTotalNum);
    this.notAllowDrawLine.changeOpacityRow = [...newOptRowMap.values()].map(it => {
      const arr = it.split(",");
      return [Number(arr[0]), Number(arr[1])];
    })
    this.notAllowDrawLine.changeOpacityCol = [...newOptColMap.values()].map(it => {
      const arr = it.split(",");
      return [Number(arr[0]), Number(arr[1])];
    })
    this.sortingCells();
    if (cursorBottomCell) {
      const index = children.findIndex(c => c === cursorBottomCell);
      if (index > -1) {
        selection.setCursorPosition([this.cell_index, index, 0, 0]);
      }
    } else if (cursorTopCell) {
      const index = children.findIndex(c => c === cursorTopCell);
      if (index > -1) {
        selection.setCursorPosition([this.cell_index, index, 0, 0]);
      }
    } else {
      selection.setCursorByRootCell("start");
    }
    editor.refreshDocument(true);
    return true;
  }

  // 删除整列
  deleteColByCursor(cursor_path: number[], editor: Editor): boolean {
    const cells = this.children as Cell[];

    const currentCell = cells[cursor_path[1]] as Cell;

    let initStartColIndex = currentCell.start_col_index; // 初始 起始列
    let initEndColIndex = currentCell.end_col_index; // 初始 末尾列

    // 记录边界单元格 为了删除之后将光标设置到距离该单元格距离最近的单元格 我要在循环单元格的时候从留下来的单元格里边找到距离 right 最近的单元格和距离 left 最近的单元格 然后最后将光标放到找到的单元格里边
    let leftCell = currentCell;
    let rightCell = currentCell;

    if (!editor.selection.isCollapsed) {
      const selectedCells = editor.selection.selected_cells;
      for (const { cell } of selectedCells) {
        const startColIndex = cell.start_col_index;
        const endColIndex = cell.end_col_index;
        if (startColIndex < initStartColIndex) {
          initStartColIndex = startColIndex;
          leftCell = cell;
        }
        if (endColIndex > initEndColIndex) {
          initEndColIndex = endColIndex;
          rightCell = cell;
        }
      }
    }



    // 要删除的列中有锁定的单元格 就不能删除
    if (
      cells.find(
        (cell) =>
          cell.start_col_index <= initStartColIndex &&
          cell.end_col_index >= initEndColIndex &&
          cell.lock
      )
    ) {
      return false;
    }

    // 1. 调整 col_size 将删除的 col_size 平均分配给其他 col_size
    const deleteColSize = this.col_size.splice(initStartColIndex, (initEndColIndex - initStartColIndex + 1));
    const deleteColSizeTotalWidth = deleteColSize.reduce((t, c) => t + c, 0);
    const every = deleteColSizeTotalWidth / this.col_size.length;
    for (let i = 0; i < this.col_size.length; i++) {
      this.col_size[i] += every;
    }
    this.notAllowDrawLine.row = [];
    this.notAllowDrawLine.col = [];

    //2. 将透明度的线存在 map 里 循环单元格的时候 进行处理 ↓
    const optRowMap = new Map();
    const optColMap = new Map();
    for (let i = 0; i < this.notAllowDrawLine.changeOpacityRow.length; i++) {
      const l = this.notAllowDrawLine.changeOpacityRow[i].toString();
      if (!optRowMap.has(l)) {
        optRowMap.set(l, l);
      }
    }
    for (let i = 0; i < this.notAllowDrawLine.changeOpacityCol.length; i++) {
      const l = this.notAllowDrawLine.changeOpacityCol[i].toString();
      if (!optColMap.has(l)) {
        optColMap.set(l, l);
      }
    }
    // 将透明度的线存在 map 里 循环单元格的时候 进行处理 ↑

    // 我要在循环单元格的时候 同时把透明度的线 也给处理好
    // 我应该循环原每个单元格的边线 看看透明度数组里边有没有 如果有的话 就及时修改线的位置 然后再放到新的数组里边去 最后换成这个就 ok 了
    const newOptRowMap = new Map();
    const newOptColMap = new Map();

    // 列号相对于 right +1 行号包含 right 的起始行的单元格 就是要将光标放入的单元格
    let rightTargetCell;
    // 列号相对于 left -1 行号包含 right 的起始行的单元格 就是要将光标放入的单元格
    let leftTargetCell;
    // 3. 将单元格删除掉 并且修改 colspan 并且修改透明度的线
    for (let i = 0; i < cells.length;) {
      const c = cells[i];
      const { top, right, bottom, left } = c.getNumbersOfFourLine(); // 要在最开始就获取 才能跟 记录的对应上
      const sColIndex = c.start_col_index;
      const eColIndex = c.end_col_index;
      // 1.1 删除掉符合条件的单元格 这些单元格是不用考虑线的问题的
      if (sColIndex >= initStartColIndex && eColIndex <= initEndColIndex) {
        c.clear()
        cells.splice(i, 1);
        continue;
      }

      if (c.start_col_index === rightCell.end_col_index + 1 && c.start_row_index <= rightCell.start_row_index && c.end_row_index >= rightCell.start_row_index) {
        rightTargetCell = c;
      }

      if (c.end_col_index === leftCell.start_col_index - 1 && c.start_row_index <= leftCell.start_row_index && c.end_row_index >= leftCell.start_row_index) {
        leftTargetCell = c;
      }

      // 关于透明度的线 我只关心留下来的单元格 不管是横线还是竖线
      // 1.2. 修改 colspan
      if (sColIndex < initStartColIndex && eColIndex > initEndColIndex) { // 完全跨过被删除列的单元格
        const deleteColNum = (initEndColIndex - initStartColIndex + 1);

        // TODO 我有必要每个单元格都循环四条边吗？ 是不是只循环上边就可以了 最后一行再循环下边？不知道有没有其他问题 先都循环再说
        // 处理横线这部分是通用的 不管你是跨过了开始列还是结束列 还是都跨过了 都可以这样循环这些横线 不对 跨过的列数不一样 那么位置的修改也不一样 列号的修改就也是不一样了 所以是不通用的 不能放在外边
        for (let j = 0; j < top.length; j++) {
          if (top[j][1] >= initStartColIndex && top[j][1] <= initEndColIndex) continue
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            if (top[j][1] > initEndColIndex) {
              top[j][1] -= deleteColNum;
              l = top[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          if (bottom[j][1] >= initStartColIndex && bottom[j][1] <= initEndColIndex) continue
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            if (bottom[j][1] > initEndColIndex) {
              bottom[j][1] -= deleteColNum;
              l = bottom[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][0] > initStartColIndex && left[j][0] < initEndColIndex) continue
          // 左边线存在就放进去
          const l = left[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }

        for (let j = 0; j < right.length; j++) {
          // 因为是跨过了 所以右边的线要列号要减
          if (right[j][0] > initStartColIndex && right[j][0] < initEndColIndex) continue
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][0] -= deleteColNum;
            l = right[j].toString()
            newOptColMap.set(l, l);
          }
        }

        c.colspan = c.colspan - deleteColNum;
      } else if (sColIndex < initStartColIndex && eColIndex >= initStartColIndex) {
        // 只跨过起始列的单元格
        const deleteColNum = (eColIndex - initStartColIndex + 1);

        for (let j = 0; j < top.length; j++) {
          if (top[j][1] >= initStartColIndex && top[j][1] <= initEndColIndex) continue
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            if (top[j][1] > initEndColIndex) { // 只跨过起始列 这个判断就不会走了
              top[j][1] -= deleteColNum;
              l = top[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          if (bottom[j][1] >= initStartColIndex && bottom[j][1] <= initEndColIndex) continue
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            if (bottom[j][1] > initEndColIndex) {
              bottom[j][1] -= deleteColNum;
              l = bottom[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][0] > initStartColIndex && left[j][0] < initEndColIndex) continue
          // 左边线存在就放进去
          const l = left[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          // 因为是跨过了 所以右边的线要列号要减
          if (right[j][0] > initStartColIndex && right[j][0] < initEndColIndex) continue
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][0] -= deleteColNum;
            l = right[j].toString()
            newOptColMap.set(l, l);
          }
        }


        c.colspan = c.colspan - deleteColNum;
      } else if (sColIndex <= initEndColIndex && eColIndex > initEndColIndex) {
        // 只跨过结束列的单元格
        c.clear(); // 起始列被删了 内容也要清空
        const deleteColNum = (initEndColIndex - sColIndex + 1);


        for (let j = 0; j < top.length; j++) {
          if (top[j][1] >= initStartColIndex && top[j][1] <= initEndColIndex) continue
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            if (top[j][1] > initEndColIndex) {
              top[j][1] -= deleteColNum;
              l = top[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          if (bottom[j][1] >= initStartColIndex && bottom[j][1] <= initEndColIndex) continue
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            if (bottom[j][1] > initEndColIndex) {
              bottom[j][1] -= deleteColNum;
              l = bottom[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }

        // 这里左边的线有点特殊 如果左边的线跟 initStartColIndex 一样 也是要保留的
        for (let j = 0; j < left.length; j++) {
          // if (left[j][0] > initStartColIndex && left[j][0] < initEndColIndex) continue // 不能用这个判断了 因为只跨过结束列非常有可能左边的线变成最左侧的(表格的)边线 就不对了
          let l = left[j].toString();
          if (optColMap.has(l)) {
            // 假设总共删除 3 列 但是这个单元格只跨过了结束列 只需要删除一列 那么左边线就应该左移 3 - 1 = 2 列
            left[j][0] -= ((initEndColIndex - initStartColIndex + 1) - deleteColNum)
            l = left[j].toString()
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          // 因为是跨过了 所以右边的线要列号要减
          if (right[j][0] > initStartColIndex && right[j][0] < initEndColIndex) continue
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][0] -= deleteColNum;
            l = right[j].toString()
            newOptColMap.set(l, l);
          }
        }

        c.colspan = c.colspan - deleteColNum;
        c.position[1] = c.position[1] + deleteColNum - deleteColSize.length
      } else if (c.position[1] > initStartColIndex) {
        const deleteColNum = initEndColIndex - initStartColIndex + 1;
        for (let j = 0; j < top.length; j++) {
          if (top[j][1] >= initStartColIndex && top[j][1] <= initEndColIndex) continue
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            if (top[j][1] > initEndColIndex) {
              top[j][1] -= deleteColNum;
              l = top[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          if (bottom[j][1] >= initStartColIndex && bottom[j][1] <= initEndColIndex) continue
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            if (bottom[j][1] > initEndColIndex) {
              bottom[j][1] -= deleteColNum;
              l = bottom[j].toString();
            }
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][0] > initStartColIndex && left[j][0] < initEndColIndex) continue
          // 左边线存在就放进去
          let l = left[j].toString();
          if (optColMap.has(l)) {
            left[j][0] -= deleteColNum;
            l = left[j].toString()
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          // 因为是跨过了 所以右边的线要列号要减
          if (right[j][0] > initStartColIndex && right[j][0] < initEndColIndex) continue
          let l = right[j].toString();
          if (optColMap.has(l)) {
            right[j][0] -= deleteColNum;
            l = right[j].toString()
            newOptColMap.set(l, l);
          }
        }

        c.position[1] -= deleteColSize.length;
      } else {
        // 还有左边的 不用修改 直接放入
        for (let j = 0; j < top.length; j++) {
          if (top[j][1] >= initStartColIndex && top[j][1] <= initEndColIndex) continue
          let l = top[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }
        for (let j = 0; j < bottom.length; j++) {
          if (bottom[j][1] >= initStartColIndex && bottom[j][1] <= initEndColIndex) continue
          let l = bottom[j].toString();
          if (optRowMap.has(l)) {
            newOptRowMap.set(l, l);
          }
        }

        for (let j = 0; j < left.length; j++) {
          if (left[j][0] > initStartColIndex && left[j][0] < initEndColIndex) continue
          // 左边线存在就放进去
          let l = left[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
        for (let j = 0; j < right.length; j++) {
          // 因为是跨过了 所以右边的线要列号要减
          if (right[j][0] > initStartColIndex && right[j][0] < initEndColIndex) continue
          let l = right[j].toString();
          if (optColMap.has(l)) {
            newOptColMap.set(l, l);
          }
        }
      }

      if (c.colspan !== 1 || c.rowspan !== 1)  {
        const { rows, cols } = c.getInnerUndrawnLines();
        this.notAllowDrawLine.row.push(...rows);
        this.notAllowDrawLine.col.push(...cols);
      }
      i++;
    }

    if (!cells.length) {
      this.remove();
      if (!editor.current_cell.children[cursor_path[0]]) {
        // 如果后边没有东西了
        editor.selection.setCursorPosition([cursor_path[0] - 1 < 0 ? 0 : cursor_path[0] - 1, 0]);
      } else {
        editor.selection.setCursorPosition([cursor_path[0], 0]);
      }
      return true;
    }

    this.notAllowDrawLine.changeOpacityRow = [...newOptRowMap.values()].map(it => {
      const arr = it.split(",");
      return [Number(arr[0]), Number(arr[1])];
    })
    this.notAllowDrawLine.changeOpacityCol = [...newOptColMap.values()].map(it => {
      const arr = it.split(",");
      return [Number(arr[0]), Number(arr[1])];
    })

    this.sortingCells();
    if (rightTargetCell) {
      const index = this.children.findIndex(c => c === rightTargetCell);
      index > - 1 && this.editor.selection.setCursorPosition([this.cell_index, index, 0, 0]);
    } else if (leftTargetCell) {
      const index = this.children.findIndex(c => c === leftTargetCell);
      index > - 1 && this.editor.selection.setCursorPosition([this.cell_index, index, 0, 0]);
    } else {
      this.editor.selection.setCursorByRootCell("start");
    }

    this.editor.refreshDocument(true); // 先加上 true 否则原来居中对齐的 就有影响了
    return true;


    // const undrawRows = this.notAllowDrawLine.row;
    // const undrawCols = this.notAllowDrawLine.col;
    // const changeOpacityRow = this.notAllowDrawLine.changeOpacityRow;
    // const changeOpacityCol = this.notAllowDrawLine.changeOpacityCol;
    // // 处理表格线的显示隐藏 目前删除整列 是只有规则的一列 才允许删除 ↓
    // // 就单个单元格来说 就是删除了 两条横线和一条竖线(写成左边的竖线吧) (不画的里边要删除，透明度的里边也要删除)，然后再修改不画的和透明度里边的线的编号
    // // 第一步：先把线删除
    // for (let i = 0; i < undrawRows.length;) {
    //   if (undrawRows[i][1] === initStartColIndex) {
    //     undrawRows.splice(i, 1);
    //   } else {
    //     i++;
    //   }
    // }
    // for (let i = 0; i < undrawCols.length;) {
    //   if (undrawCols[i][0] === initStartColIndex) {
    //     undrawCols.splice(i, 1);
    //   } else {
    //     i++;
    //   }
    // }
    // for (let i = 0; i < changeOpacityRow.length;) {
    //   if (
    //     changeOpacityRow[i][1] >= initStartColIndex &&
    //     changeOpacityRow[i][1] <= initEndColIndex
    //   ) {
    //     changeOpacityRow.splice(i, 1);
    //   } else {
    //     i++;
    //   }
    // }
    // for (let i = 0; i < changeOpacityCol.length;) {
    //   if (
    //     changeOpacityCol[i][0] >= initStartColIndex &&
    //     changeOpacityCol[i][0] <= initEndColIndex
    //   ) {
    //     changeOpacityCol.splice(i, 1);
    //   } else {
    //     i++;
    //   }
    // }
    // // 第二步： 修改线的编号
    // for (let i = 0; i < undrawCols.length; i++) {
    //   if (undrawCols[i][0] > initStartColIndex) {
    //     undrawCols[i][0]--;
    //     if (undrawCols[i][0] === 0) {
    //       undrawCols.splice(i, 1);
    //       i--;
    //     }
    //   }
    // }
    // for (let i = 0; i < undrawRows.length; i++) {
    //   if (undrawRows[i][1] > initStartColIndex) {
    //     undrawRows[i][1]--;
    //   }
    // }
    // for (let i = 0; i < changeOpacityRow.length; i++) {
    //   if (changeOpacityRow[i][1] > initEndColIndex) {
    //     changeOpacityRow[i][1] -= currentCell.colspan;
    //   }
    // }
    // for (let i = 0; i < changeOpacityCol.length; i++) {
    //   if (changeOpacityCol[i][0] > initEndColIndex) {
    //     changeOpacityCol[i][0] -= currentCell.colspan;
    //   }
    // }
    // // 处理表格线的显示隐藏 ↑

    // if (currentCell.left === 0 && currentCell.right === this.width) {
    //   const current_group = editor.selection.getFocusGroup();
    //   this.remove({ current_group });
    //   if (!editor.current_cell.children[cursor_path[0]]) {
    //     // 如果后边没有东西了
    //     editor.selection.setCursorPosition([cursor_path[0] - 1, 0]);
    //   } else {
    //     editor.selection.setCursorPosition([cursor_path[0], 0]);
    //   }
    //   return true;
    // }

    // // 找到起始列和初始起始列一致 并且 末尾列和初始末尾列一致的单元格删除掉
    // for (let i = 0; i < cells.length;) {
    //   const cell = cells[i];
    //   const start_col_index = cell.start_col_index;
    //   const end_col_index = cell.end_col_index;
    //   const col_span = cell.colspan;
    //   if (start_col_index === initStartColIndex && col_span === 1) {
    //     cells.splice(i, 1);
    //   } else {
    //     if (
    //       start_col_index <= initStartColIndex &&
    //       end_col_index >= initEndColIndex
    //     ) {
    //       cell.colspan--;
    //       if (start_col_index === initStartColIndex) {
    //         cell.clear();
    //       }
    //     } else if (start_col_index > initStartColIndex) {
    //       cell.position[1]--;
    //     }
    //     i++;
    //   }
    // }
    // for (let i = 0; i < undrawCols.length; i++) {
    //   const colIndex = undrawCols[i][0]
    //   const rowIndex = undrawCols[i][1]
    //   cells.forEach((cell) => {
    //     if (cell.start_col_index === colIndex && cell.start_row_index === rowIndex) {
    //       undrawCols.splice(i, 1)
    //     }
    //   });
    // }

    // if (this.children.length === 0) {
    //   const current_group = editor.selection.getFocusGroup();
    //   this.remove({ current_group });
    //   editor.selection.setCursorPosition([cursor_path[0], 0]);
    // } else {
    //   this.sortingCells();
    //   let index = 0;
    //   for (; index < this.children.length; index++) {
    //     const cell = this.children[index];
    //     if (
    //       cell.start_col_index === initStartColIndex &&
    //       initStartColIndex !== this.col_size.length - 1
    //     ) {
    //       cursor_path.splice(1, 3, index, 0, 0);
    //       editor.selection.setCursorPosition(cursor_path);
    //       break;
    //     }
    //   }
    //   if (index === this.children.length) {
    //     if (!editor.current_cell.children[cursor_path[0] + 1]) {
    //       cursor_path.splice(1, 3, 0, 0, 0);
    //       editor.selection.setCursorPosition(cursor_path);
    //     } else {
    //       editor.selection.setCursorPosition([cursor_path[0] + 1, 0]);
    //     }
    //   }
    // }
    // // ① 把删除那一列的col_size删除掉
    // const col_size_arr = this.col_size;
    // col_size_arr.splice(initStartColIndex, 1);
    // // ② 再重新计算col_size
    // const total_col_size = col_size_arr.reduce(
    //   (total, current) => total + current,
    //   0
    // );
    // const tbl_width =
    //   editor.page_size.width -
    //   editor.config.page_padding_left -
    //   editor.config.page_padding_right;
    // for (let i = 0, len = col_size_arr.length; i < len; i++) {
    //   col_size_arr[i] = (col_size_arr[i] / total_col_size) * tbl_width;
    // }

    // return true;
  }

  /**
   * 补全单元格
   */
  completeTheCell() {
    const rows_num = this.row_size.length;
    const cols_num = this.col_size.length;
    const cells = this.children;
    for (let i = 0; i < rows_num; i++) {
      for (let j = 0; j < cols_num; j++) {
        // [i, j] 就是所有的位置
        // 如果在cells中找到了 该坐标 那就不需要new出来新的单元格 补上了 如果没有 再补上
        const item = cells.find(
          (cell) =>
            i >= cell.start_row_index &&
            i <= cell.end_row_index &&
            j >= cell.start_col_index &&
            j <= cell.end_col_index
        );
        if (!item) {
          // 如果没有找到 才需要new新的单元格 并且位置就是 [i, j] colspan为1 rowspan为1
          const cell = new Cell(this.editor, [i, j], 1, 1, this);
          cell.insertEmptyParagraph();
          cells.push(cell);
        }
      }
    }
  }

  /**
   * 处理表格线的透明度，谁修改谁调用
   * 思路是：循环选中单元格的所有线，去notAllowDrawLine的四个属性中去找 如果找到 线的横坐标修改 然后添加到新表格对应的四个属性中去
   * @param selected_cells 选中单元格
   * @param relyOnTalbe 依赖的表格  relyOnTalbe 就是复制的单元格的父级表格 还是copy过的，notAllowDrawLine属性 也被copy过了
   * @param min_row_index 起始行 所有线的横坐标要减去这个值 就是新的值
   * @param min_col_index 起始列 所有线的纵坐标要减去这个值 就是新的值
   */
  handleLine(
    selected_cells: { cell: Cell }[],
    relyOnTalbe: Table,
    min_row_index: number,
    min_col_index: number
  ) {
    const lines_row = []; // 保存复制单元格所有的横线 会有大量重复的线
    const lines_col = []; // 保存复制单元格所有的竖线 会有大量重复的线
    for (const { cell } of selected_cells) {
      const outer_line = JSON.parse(
        JSON.stringify(cell.getNumbersOfFourLine())
      );
      lines_row.push(...outer_line.top, ...outer_line.bottom);
      lines_col.push(...outer_line.right, ...outer_line.left);
      const inside_line = JSON.parse(
        JSON.stringify(cell.getInsideLineNumber())
      );
      lines_row.push(...inside_line.res_rows);
      lines_col.push(...inside_line.res_cols);
    }
    const cols = relyOnTalbe.notAllowDrawLine.col;
    const rows = relyOnTalbe.notAllowDrawLine.row;
    const changeOpacityRow = relyOnTalbe.notAllowDrawLine.changeOpacityRow;
    const changeOpacityCol = relyOnTalbe.notAllowDrawLine.changeOpacityCol;
    const res_cols = [];
    const res_rows = [];
    const res_changeOpacityRow = [];
    const res_changeOpacityCol = [];
    // 循环横线
    for (const [row, col] of lines_row) {
      if (rows.find((item) => item[0] === row && item[1] === col)) {
        res_rows.push([row - min_row_index, col - min_col_index]);
      }
      if (changeOpacityRow.find((item) => item[0] === row && item[1] === col)) {
        res_changeOpacityRow.push([row - min_row_index, col - min_col_index]);
      }
    }
    // 循环竖线
    for (const [col, row] of lines_col) {
      if (cols.find((item) => item[0] === col && item[1] === row)) {
        res_cols.push([col - min_col_index, row - min_row_index]);
      }
      if (changeOpacityCol.find((item) => item[0] === col && item[1] === row)) {
        res_changeOpacityCol.push([col - min_col_index, row - min_row_index]);
      }
    }

    this.notAllowDrawLine.row = [...res_rows];
    this.notAllowDrawLine.col = [...res_cols];
    this.notAllowDrawLine.changeOpacityRow = [...res_changeOpacityRow];
    this.notAllowDrawLine.changeOpacityCol = [...res_changeOpacityCol];
  }

  copy(parent: Cell) {
    const table = new Table(
      this.editor,
      this.id,
      null,
      this.col_size,
      this.row_size,
      this.min_row_size,
      parent,
      this.left,
      this.right,
      this.top,
      this.newPage,
      this.skipMode
    );
    const cells = this.children;
    for (let i = 0; i < cells.length; i++) {
      const child = cells[i];

      const new_child = child.copy(table);
      table.children.push(new_child);
      table.cellMap.set(new_child.id, new_child);
    }
    const notAllowDrawLine = this.notAllowDrawLine;
    const col = notAllowDrawLine.col.map((arr) => [...arr]);
    const row = notAllowDrawLine.row.map((arr) => [...arr]);
    const changeOpacityCol = notAllowDrawLine.changeOpacityCol.map((arr) => [
      ...arr,
    ]);
    const changeOpacityRow = notAllowDrawLine.changeOpacityRow.map((arr) => [
      ...arr,
    ]);
    table.notAllowDrawLine = {
      col,
      row,
      changeOpacityCol,
      changeOpacityRow,
    };
    if (this.imageList) {
      table.imageList = deepClone(this.imageList);
    }
    table.name = this.name;
    table.fullPage = this.fullPage || false;
    return table;
  }

  getOrigin(): Table {
    let origin = this.origin || this;

    while (origin.origin) {
      origin = origin.origin;
    }

    return origin;
  }

  // 获取该表格内的所有字符串
  // TODO 优化单元格位置判断
  getStr(noSymbol: boolean = false): string {
    let res = "";
    let cellPositionMark;
    const cells = this.children;
    for (const cell of cells) {
      if (cellPositionMark === undefined) {
        cellPositionMark = cell.position[0];
      }
      const paragraphs = cell.paragraph as Paragraph[];
      for (const paragraph of paragraphs) {
        if (isParagraph(paragraph)) {
          if (cell.position[0] === cellPositionMark) {
            if (paragraph === paragraphs[paragraphs.length - 1]) {
              const str = paragraph.getStr(noSymbol, undefined, {
                excludeUnselected: true
              });
              res += str.replace(/\n/, "\t");
            } else {
              res += paragraph.getStr(noSymbol, undefined, {
                excludeUnselected: true
              });
            }
          } else {
            res = res.substring(0, res.length - 1) + "\n";
            if (paragraph === paragraphs[paragraphs.length - 1]) {
              const str = paragraph.getStr(noSymbol, undefined, {
                excludeUnselected: true
              });
              res += str.replace(/\n/, "\t");
            } else {
              res += paragraph.getStr(noSymbol, undefined, {
                excludeUnselected: true
              });
            }
            cellPositionMark = cell.position[0];
          }
        }
      }
    }
    return res;
  }

  getRawData() {
    const saveData = [this.copy(this.editor.current_cell)];
    const newRootCell = initCell(this.editor, "trans");
    const newHeaderCell = initCell(this.editor, "header_trans");
    const newFooterCell = initCell(this.editor, "footer_trans");
    newRootCell.paragraph = saveData;
    // return rawDataTrans.modelDataToRawData(
    //   newHeaderCell,
    //   newRootCell,
    //   newFooterCell
    // );
    return this.editor.event.emit(
      "modelData2RawData",
      newHeaderCell,
      newRootCell,
      newFooterCell
    );
  }

  replaceWith(...rawDataArr: any[]) {
    const originViewMode = this.editor.view_mode;
    // 不用 setViewMode() 因为里边会调用 refreshDocument(true) 改变了 页眉页脚的编辑状态
    // 更重要的是避免消耗性能因为里边就只根据了该属性值做判断 不需要刷新页面
    this.editor.view_mode = ViewMode.NORMAL;

    if (this.parent.hf_part) {
      this.editor.enterEditHeaderAndFooterMode(this.parent.hf_part);
    }

    const resInfo = this.editor.deleteContentByPath(
      [this.para_index, 0, 0, 0],
      [this.para_index + 1, 0]
    );
    if (Array.isArray(resInfo)) {
      // 将para_path再转为model_path进行光标位置设置
      this.editor.selection.setCursorPosition(
        this.editor.paraPath2ModelPath(resInfo)
      );
      for (const rawData of rawDataArr) {
        EditorHelper.insertTemplateData(this.editor, rawData);
      }
    }

    if (this.parent.hf_part) {
      this.editor.quitEditHeaderAndFooterMode();
    }

    // 这儿不能只改属性了 quitEditHeaderAndFooterMode 方法里边有重新刷新渲染的逻辑 只改属性赋值 页面显示文本域边框等可能会有问题
    this.editor.setViewMode(originViewMode);
  }

  /**
   *  记录每个分割线 在表格内 相对于原始表格的位置
   */
  recordSplitLine(split_line: number) {
    if (this.page_break) {
      // 此表格已经被分割过
      const origin = this.getOrigin();
      const split_line_arr = origin.split_line_arr;
      const previous_split_line = split_line_arr[split_line_arr.length - 1];
      split_line_arr.push(previous_split_line + split_line);
    } else {
      this.split_line_arr = [split_line];
    }
  }

  // 将 cell 内的所有线放到不绘制的数组中(因为合并单元格内部的线是肯定不会绘制的)
  addLinesThatAreNotDrawn(cell: Cell) {
    if (cell.rowspan > 1 || cell.colspan > 1) {
      for (let i = 0; i < cell.rowspan - 1; i++) {
        for (let j = 0; j < cell.colspan; j++) {
          this.notAllowDrawLine.row.push([
            cell.position[0] + 1 + i,
            cell.position[1] + j,
          ]);
        }
      }
      for (let i = 0; i < cell.colspan - 1; i++) {
        for (let j = 0; j < cell.rowspan; j++) {
          this.notAllowDrawLine.col.push([
            cell.position[1] + 1 + i,
            cell.position[0] + j,
          ]);
        }
      }
    }
  }

  // 但是也有此时的例子一样的情况 永远都循环不到 top > splitLine 的单元格 需要在循环外再做处理(循环外 cellsTraversedBySplitLine 还有的话就说明到了最后一页了)
  // 我应该在每次创建 nextTable 的时候就处理 split_line_arr split_parts 等记录分割的属性
  // 返回的 cell 在被分割后的表格内 顺序必须是正确的,点击单元格的时候进行路径转换等等 都要用到顺序,
  // 可以记录放进 cellsTraversedBySplitLine 里边单元格应该在preTable或者nextTable中的下标,随便找个东西占位,然后等待分割的时候再替换掉,不知道是否可行
  // FIXME 最后检查一遍只在该创建 preTable 的时候创建 preTable 只在该创建 nextTable 的时候创建 nextTable 一旦创建了 nextTable 就要往 split_parts 里边放
  // 里边用了不少的 shift unshift 后期可以考虑用链表来优化
  // const tempPreTableChildren = []; // 应该放入到 preTable 里边的单元格 避免重置分割线的时候有些单元格会被拽到下一页 所以临时存一下 出现特殊情况后再做处理(一般可能会倒着循环该数组,数量为表格的 col_size 的 length)
  // TODO 应该不会有 temPreTableChildren 这种情况,因为如果被拽下去也得是 cellsTraversedBySplitLine 里边的单元格整体下移 该在上一页中的单元格是不会被拽下去的
  testSplit(initSplitLine: number, parent: Cell): Table[] {
    if (this.origin && this.origin.split_parts.length > 0) return [this];
    const fixedRowNum = this.fixed_table_header_num;
    const fixedRowSize = [...this.row_size.slice(0, fixedRowNum)];
    const fixedMinRowSize = [...this.min_row_size.slice(0, fixedRowNum)];
    const cancelFixed =
      fixedRowSize.reduce((t, c) => t + c, 0) >= initSplitLine; // TODO 这个判断好像不绝对
    const headerCells = []; // 表头单元格
    if (!cancelFixed) {
      for (const cell of this.children) {
        if (cell.position[0] < fixedRowNum) {
          cell.isHeaderCell = true;
          headerCells.push(cell);
        } else {
          break;
        }
      }
    }
    const splitTables: Table[] = [];
    this.resetSplitAttr();
    // this.sortingCells();
    let pageNumber = this.page_number; // 经过一次赋值之后就要 +1
    let splitLine = initSplitLine;
    const editor = this.editor;
    const page = editor.pages[0];
    const fixedSplitLine = page.contentHeight; // 固定分割线 后续分割时内容高度就是理论上的分割线的位置
    const unCells = [...this.children]; // 未分配的单元格
    const unRowSize = [...this.row_size]; // 未分配的 row_size
    const unMinRowSize = [...this.min_row_size]; // 未分配的 min_row_size
    const unOpacityHorizontal = arrDeduplication(
      this.notAllowDrawLine.changeOpacityRow
    ); // 数组去重 并且进行了克隆
    sort(unOpacityHorizontal);
    const unOpacityVertical = arrDeduplication(
      this.notAllowDrawLine.changeOpacityCol
    );
    sort(unOpacityVertical);
    let splitedRowSizeNum = 0; // 已经被拆分过的(已经放到上方表格中去了) row_size 的数量

    let preTable = new Table(
      editor,
      this.id,
      this.group_id,
      this.col_size,
      [],
      [],
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      this.top,
      false,
      SkipMode.ROW
    ); // 被拆分的就不能分页了
    preTable.page_number = pageNumber++; // 一旦创建新表格就赋值 page_number
    preTable.page_index = this.page_index; // 一旦创建新表格就赋值 page_index
    preTable.origin = this; // 一旦创建新表格就要赋值 origin 其他地方不赋值 会在数据转换的时候用到 origin
    splitTables.push(preTable); // 一旦创建新表格就要放到 splitTables 里边去(是否应该考虑跟 split_parts 一样只有放 nextTable 的时候才放入 preTable)
    this.split_parts.push(preTable);

    const unshiftCells: Cell[] = [];

    const cellsTraversedBySplitLine = []; // 被分割线穿过的单元格 如果这里边没有单元格就只能是硬分割 如果这里有单元格就可能是硬分割也可能是软分割

    // const nextTableLinesNumber = []; // 下一页表格的编号

    // 固定表头 可以在循环 unCells 之前就将表格放入进去,然后每次分割的时候再放入进去 逻辑就跟即将写好的逻辑保持一致就可以了
    // 循环分配每个单元格,直到将表格内的所有单元格都分配完
    let index = 0; // 用 index 下标代替,就记录要取的 unCells 里边的下标,甚至都不用 unCells 就用 this.children 就可以,然后要往里边 unshift 的时候及时修改 index 下标 还要用另外一个变量来及时记录是否进循环
    while (index < unCells.length || unshiftCells.length) {
      // shift 方法特别耗性能 不要用这个 即便用 slice 代替也还是会消耗一部分性能
      let cell;
      if (unshiftCells.length > 0) {
        cell = unshiftCells.shift()!;
      } else {
        cell = unCells[index]!;
        index++;
      }
      // unCells = unCells.slice(1);

      // 这里进行 copy 比直接调用 cell.copy 少调用很多东西简化了性能
      const copiedCell = new Cell(
        cell.editor,
        [...cell.position],
        cell.colspan,
        cell.rowspan,
        preTable,
        cell.id
      );
      Cell.attrJudgeUndefinedAssign(copiedCell, cell);
      copiedCell.children = cell.children;
      copiedCell.paragraph = cell.paragraph;
      copiedCell.origin = cell.getOrigin();
      copiedCell.page_break = cell.page_break;
      copiedCell.position[0] !== 0 &&
        (copiedCell.position[0] -= splitedRowSizeNum); // 因为等于 0 的是在单元格拆分的时候就直接修改过位置的是正确的 所以不需要减
      if (
        headerCells.length > 0 &&
        preTable.page_number > this.page_number &&
        !cell.isHeaderCell
      ) {
        copiedCell.position[0] += fixedRowNum;
      }
      const top = copiedCell.getTop(unRowSize);
      const bottom = top + copiedCell.getHeight(unRowSize);

      // 其实即便 bottom <= splitLine 也未必一定要放在上一页的表格中 因为后边如果有个内容很大的单元格 可能会将该单元格拽到下一页中去 其实是一定会放到上一页中去的因为表格拆分处理的是 row_size bottom <= splitLine 就说明此时的 row_size 会被拆分到上一页中去 即便是有软分割的单元格 里边有很大的内容 也只是将合并单元格的下一个 row_size 放到下一页中去
      // 比如一个两行两列的表格 将最右侧一列的单元格合并 将合并后单元格内的文字样式改到最大 整个表格都会被拖到下一页中去 但是第一个单元格的 bottom <= splitLine 这个情况会将下一页的第一个 row_size 撑很大 但是这些单元格仍然会放进上一页中去 也不存在没有合并的单元格在同一个 row_size 前边的 bottom <= splitLine 后边的穿过分割线这个情况 因为整行的 row_size 都会很大 都会穿过分割线
      if (bottom <= splitLine) {
        if (cell.shouldPushSplitParts) {
          if (
            headerCells.length > 0 &&
            preTable.page_number > this.page_number &&
            !cell.isHeaderCell
          ) {
            cell.position[0] += fixedRowNum;
          }
          preTable.cellMap.set(cell.id, cell);
          preTable.children.push(cell); // 不能放 copiedCell 是因为路径转换的时候会用到 viewData 中的 table.chldren 找这个表格,用的是引用地址判断的,如果用了 copiedCell 在 getCellIndex 里边就会找不到
          cell.getOrigin().split_parts.push(cell);
          cell.resetSplitRowIndexs();
          preTable.addLinesThatAreNotDrawn(cell);
        } else {
          preTable.children.push(copiedCell);
          preTable.cellMap.set(copiedCell.id, copiedCell);
          preTable.addLinesThatAreNotDrawn(copiedCell);
        }
      }

      // 如果没进这个判断就会是硬分割 进了这个判断也有可能是硬分割
      if (top <= splitLine && bottom > splitLine) {
        cellsTraversedBySplitLine.push(copiedCell);
      }

      // 如果已经拆分完了 但是没进这个判断 也是有可能的 因为拆分完了没有 top > splitLine 的单元格 在表格最后一行拆分的时候就这样
      // TODO 也不一定 有可能是分配到下一页中有很多行 有可能是第一行的 top 并不大于 splitLine 到了第二行 top 才会大于 splitLine 这种情况也没问题
      // 因为 这三个判断 包含了所有情况 循环的每个单元格必然会放到一个地方去 这种情况下,第一行的单元格应该会放到 cellsTraversdBySplitLine 里边去
      // 所以要在这个判断里边将要放到下一页中去的单元格 重新放入到 unCells 里边去 分配好 row_size 创建好表格 等等 所以循环结束了 cellsTraversedBySplitLine 里边还有值的肯定就会都在下一页中了
      // TODO 还要测试就两行的表格进行硬分割和软分割的情况,整体应该被拽到下一页中去的情况,看看效果对不对
      if (top > splitLine) {
        // unCells.unshift(cell); // 因为处理的都是 copiedCell 所以遇到这种就直接再放回到 unCells 里边去 也没有问题
        unshiftCells.unshift(cell);
        // 此时不一定就应该有两个表格了 会有整个表格被拽下来的情况 比如三行的表格 第一行有特别大的字
        const nextTable = new Table(
          editor,
          this.id,
          this.group_id,
          this.col_size,
          [],
          [],
          parent,
          parent.left + parent.padding_left,
          parent.right - parent.padding_right,
          page.header.header_outer_bottom,
          false,
          SkipMode.ROW
        ); // 第二个表格一定在这一页的开头 所以 top 值等于这个
        // 以下是一旦创建 nextTable 就要赋值的属性
        this.split_line_arr.push(
          (this.split_line_arr[this.split_line_arr.length - 1] || 0) + splitLine
        ); // 每次增加一个表格的时候 this.split_line_arr 就要追加一个线的高度
        nextTable.page_number = pageNumber++;
        nextTable.page_index = 0;
        nextTable.origin = this;

        // 此时将该 cell 重新放回 unCells 之后就只处理 preTable 就可以了 只在最后的时候才处理最后一页的 nextTable
        // bottom <= splitLine 未必会放到上一页中去 那么这个单元格就一定要放到下一页中去吗?
        // 是一定的 因为如果字小那么分割线就在这儿了 如果字大就会把其他单元格拽到下一页中去 也不存在分割线下移的情况 所以这个单元格一定是要放到下一页中去的
        // 那么到这儿的时候上方表格就一定装满了吗 后边的单元格就绝对不会放到上一页中去了?
        // 是的因为 row_size 是整个表格的属性, 是整个表格上的一条横线, 又因为单元格经过了排序,这个单元格都要放到下一页中去了,往后就没有应该放到上一页中去的了
        let splitRowSizeIndex = 0; // 应该被拆分的 row_size 的下标
        let cellSplitLine = 0; // 拆分单元格使用 默认为 0 跟 splitLine 用法一样
        let hasHardSplit = false; // 有硬分割 默认 false
        for (const cell of cellsTraversedBySplitLine) {
          const cellTop = cell.getTop(unRowSize);
          const distanceToCellTopBorder = splitLine - cellTop;

          if (
            cell.ifHardSplit(distanceToCellTopBorder) ||
            cellTop === splitLine
          ) {
            splitRowSizeIndex = Math.max(cell.position[0], splitRowSizeIndex);
            cellSplitLine = cellTop;
            hasHardSplit = true;
          }
        }

        if (hasHardSplit) {
          if (
            this.row_size.length === unRowSize.length &&
            splitRowSizeIndex === 0
          ) {
            // 情况之一: 三行的表格 第一行内容很高 整体都会被拽下去就会走这儿了
            this.split_parts.pop();
            return [this];
          }
          let nextTableFirstRowSize = 0; // 表格拆分 下一页表格 第一个 row_size 的应该的高度
          nextTable.page_break_type = BreakType.hard;
          const preTableNeedHandleLineCells = [];
          const shouldBeReassignedCells: Cell[] = []; // 为了保证顺序 所以用这个数组过渡 不能直接循环里边用 unCells.unshift(c2) 添加
          for (const cell of cellsTraversedBySplitLine) {
            const top = cell.getTop(unRowSize);
            const height = cell.getHeight(unRowSize);
            const bottom = top + height;

            if (top < cellSplitLine && bottom > cellSplitLine) {
              const [c1, c2] = cell.split(
                splitLine - top,
                splitRowSizeIndex,
                preTable,
                nextTable,
                BreakType.hard
              );
              const splitedCell = cell.getOrigin().split_parts.pop()!;
              splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

              preTable.children.push(c1);
              preTable.cellMap.set(c1.id, c1);
              preTableNeedHandleLineCells.push(c1);

              const c2RealContentHeight = c2.getRealContentHeight();
              if (c2.rowspan === 1) {
                nextTableFirstRowSize = Math.max(
                  c2RealContentHeight,
                  nextTableFirstRowSize
                );
              }

              shouldBeReassignedCells.push(c2);

              cell.resetSplitRowIndexs();
              c2.position[0] = 0;
            } else {
              const newCell = new Cell(
                cell.editor,
                [0, cell.position[1]],
                cell.colspan,
                cell.rowspan,
                nextTable,
                cell.id
              );
              Cell.attrJudgeUndefinedAssign(newCell, cell);
              newCell.children = cell.children;
              newCell.paragraph = cell.paragraph;
              newCell.origin = cell.getOrigin();
              shouldBeReassignedCells.push(newCell);
            }
          }
          preTableNeedHandleLineCells.forEach((c) => {
            preTable.addLinesThatAreNotDrawn(c);
          });
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex);
          unRowSize.splice(0, splitRowSizeIndex);
          unRowSize[0] = Math.max(unRowSize[0], nextTableFirstRowSize);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex);
          unMinRowSize.splice(0, splitRowSizeIndex);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            // unCells.unshift(...headerCells, ...shouldBeReassignedCells);
            unshiftCells.unshift(...headerCells, ...shouldBeReassignedCells);
          } else {
            // unCells.unshift(...shouldBeReassignedCells);
            unshiftCells.unshift(...shouldBeReassignedCells);
          }

          splitedRowSizeNum += splitRowSizeIndex;

          if (
            headerCells.length > 0 &&
            preTable.page_number > this.page_number
          ) {
            splitedRowSizeNum -= fixedRowNum;
          }

          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum < splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1); // 因为删除了 所以 i 在这里不能加 1 否则就会跳过一个编号
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i++;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum < splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[1], 0]);
              i++;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
        } else {
          nextTable.page_break_type = BreakType.soft;
          const preTableNeedHandleLineCells = [];
          let nextTableFirstRowSize = 0; // 表格拆分 下一页表格 第一个 row_size 的应该的高度

          // 因为是软分割 要重新计算 被拆分的 row_size 先计算到 splitLine 之前的 row_size 的总高度 不能放到最下方  cell.split 要用 splitRowSizeIndex
          let totalRowSizeHeight = 0;
          for (let i = 0; i < unRowSize.length; i++) {
            if (totalRowSizeHeight + unRowSize[i] > splitLine) {
              splitRowSizeIndex = i;
              break;
            }
            totalRowSizeHeight += unRowSize[i];
          }

          // 应该被重新分配的单元格集合
          const shouldBeReassignedCells: Cell[] = []; // 为了保证顺序 所以用这个数组过渡 不能直接循环里边用 unCells.unshift(c2) 添加
          for (const cell of cellsTraversedBySplitLine) {
            const top = cell.getTop(unRowSize);
            const height = cell.getHeight(unRowSize);
            const bottom = top + height;
            if (top < splitLine && bottom > splitLine) {
              const [c1, c2] = cell.split(
                splitLine,
                splitRowSizeIndex,
                preTable,
                nextTable,
                BreakType.soft,
                unRowSize
              );

              // 因为虽然是被拆分成了两个单元格 但是下一个单元格就又往 unCells 里边放了 下一轮又可能是硬分割直接放在上一页表格 也有可能继续进行软分割
              // 所以下一次还是软分割的时候就要把原来放到 split_parts 里边的最后一个删除掉,因为又被拆分了 但是要做个标记 要是下次被硬分割直接放到上一页中去的时候
              // split_parts 里边就少一个了 所以要做标记循环的时候别忘了放进 split_parts 里边去
              const splitedCell = cell.getOrigin().split_parts.pop()!;
              splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

              preTable.children.push(c1);
              preTable.cellMap.set(c1.id, c1);
              preTableNeedHandleLineCells.push(c1);
              c2.position[0] = 0;
              shouldBeReassignedCells.push(c2); // 为了保证再进训话时候的顺序是对的，提供一个变量先收集一下 直接用 unCell.unshift(c2) 会导致顺序不对 因为会 unCells.unshift(...shouldBeReassignedCells)

              const c2RealContentHeight = c2.getRealContentHeight();
              if (c2.rowspan === 1) {
                nextTableFirstRowSize = Math.max(
                  c2RealContentHeight,
                  nextTableFirstRowSize
                );
              }

              cell.resetSplitRowIndexs();
            } else {
              // 这些是直接放到下一页表格中去的
              const newCell = new Cell(
                copiedCell.editor,
                [0, cell.position[1]],
                cell.colspan,
                cell.rowspan,
                nextTable,
                cell.id
              );
              Cell.attrJudgeUndefinedAssign(newCell, cell);
              newCell.children = cell.children;
              newCell.paragraph = cell.paragraph;
              newCell.origin = cell.getOrigin();
              if (newCell.rowspan > 1 || newCell.colspan > 1) {
                for (let i = 0; i < newCell.rowspan - 1; i++) {
                  for (let j = 0; j < newCell.colspan; j++) {
                    nextTable.notAllowDrawLine.row.push([
                      newCell.position[0] + 1 + i,
                      newCell.position[1] + j,
                    ]);
                  }
                }
              }
              shouldBeReassignedCells.push(newCell);
            }
          }
          // unCells.unshift(...shouldBeReassignedCells);
          unshiftCells.unshift(...shouldBeReassignedCells);

          splitedRowSizeNum += splitRowSizeIndex; // TODO 还得仔细研究一下这里 如果有固定一行表头的话 += 是不对的
          if (
            headerCells.length > 0 &&
            preTable.page_number > this.page_number
          ) {
            splitedRowSizeNum -= fixedRowNum;
          }
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex + 1);
          unRowSize.splice(0, splitRowSizeIndex + 1);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex + 1);
          unMinRowSize.splice(0, splitRowSizeIndex + 1);

          // 上边只是分配 row_size 接下来是要分割 row_size
          // 分割 row_size
          // 1. 计算 preTable 的 row_size 高度和(包括了应该被拆分还没被拆分的 row_size)
          const preTableSumRowSizeHasToBeSplit = preTable.row_size.reduce(
            (total, current) => total + current,
            0
          );
          // 将要被拆分的 row_size 的高度
          const rowSizeToBeSplit =
            preTable.row_size[preTable.row_size.length - 1];
          // 将要被拆分的 row_size 分配给 preTalbe 之后的剩余高度
          const restHeightOfToBeSplit =
            preTableSumRowSizeHasToBeSplit - splitLine;
          // 将要被拆分的 row_size 的高度减去应该分配给 preTable 的 row_size 之后的剩余高度，就是 preTable 最后一个 row_size 的高度
          preTable.min_row_size[preTable.min_row_size.length - 1] =
            preTable.row_size[preTable.row_size.length - 1] =
            rowSizeToBeSplit - restHeightOfToBeSplit;

          // 应该分配给 nextTable 的 row_size 的高度，就是被拆分的 row_size 的高度，减去分配给 preTable 的 row_size 的高度
          let rowSizeAssignedToNextTable =
            rowSizeToBeSplit - preTable.row_size[preTable.row_size.length - 1];
          rowSizeAssignedToNextTable =
            rowSizeAssignedToNextTable > nextTableFirstRowSize
              ? rowSizeAssignedToNextTable
              : nextTableFirstRowSize;
          unRowSize.unshift(rowSizeAssignedToNextTable);
          const minRowSizeAssignedToNextTable =
            rowSizeAssignedToNextTable < Config.min_row_size
              ? Config.min_row_size
              : rowSizeAssignedToNextTable;
          unMinRowSize.unshift(minRowSizeAssignedToNextTable);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }
          preTable.page_break_type = BreakType.soft;
          preTableNeedHandleLineCells.forEach((c) => {
            preTable.addLinesThatAreNotDrawn(c);
          });

          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum <= splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex + 1) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              unOpacityHorizontal.unshift([1, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i += 2;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum <= splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[0], 0]);
              unOpacityVertical.unshift([coordinate[0], 1]);
              i += 2;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
        }
        cellsTraversedBySplitLine.length = 0;
        splitLine = fixedSplitLine;
        preTable.page_break = true;

        this.split_parts.push(nextTable);
        splitTables.push(nextTable);

        preTable = nextTable;
      } else if (
        index >= unCells.length &&
        cellsTraversedBySplitLine.length &&
        unshiftCells.length === 0
      ) {
        // 比如一个单元格无限软分割会走这里 或者 正好有硬分割 但是没有 top > splitLine 的单元格的时候就会走这个
        // 增加 unshiftCells.length === 0 因为如果有多列的表格,在最后一列进行无限软分割的时候 unshiftCells 只拿出来一个单元格的时候就会进这个判断结果就不对了 应该是要把 unshiftCells 都走完了确定应该进行拆分的时候才进这里进行拆分
        let splitRowSizeIndex = 0; // 应该被拆分的 row_size 的下标
        // XXX 重新计算了分割线 那么根据之前的 splitLine 分配到 tempPreTableChildren 里边的单元格是不是又要重新分配了?
        let cellSplitLine = 0; // 拆分单元格使用 默认为 0 跟 splitLine 用法一样

        let hasHardSplit = false; // 有硬分割 默认 false
        for (const cell of cellsTraversedBySplitLine) {
          const cellTop = cell.getTop(unRowSize);
          const distanceToCellTopBorder = splitLine - cellTop;

          if (
            cell.ifHardSplit(distanceToCellTopBorder) ||
            cellTop === splitLine
          ) {
            if (cell.position[0] === 0) {
              this.split_parts.pop();
              return [this];
            } // 如果是硬分割了 但是位置为 0 就是说整个表格都要下移到下一页去 就没必要继续往下走了
            splitRowSizeIndex = Math.max(cell.position[0], splitRowSizeIndex);
            cellSplitLine = cellTop;
            hasHardSplit = true;
          }
        }

        if (hasHardSplit) {
          const nextTable = new Table(
            editor,
            this.id,
            this.group_id,
            this.col_size,
            [],
            [],
            parent,
            parent.left + parent.padding_left,
            parent.right - parent.padding_right,
            page.header.header_outer_bottom,
            false,
            SkipMode.ROW
          ); // 第二个表格一定在这一页的开头 所以 top 值等于这个
          // 以下是一旦创建 nextTable 就要赋值的属性
          this.split_line_arr.push(
            (this.split_line_arr[this.split_line_arr.length - 1] || 0) +
            splitLine
          ); // 每次增加一个表格的时候 this.split_line_arr 就要追加一个线的高度

          let nextTableFirstRowSize = 0; // 表格拆分 下一页表格 第一个 row_size 的应该的高度
          // this.split_parts.push(preTable, nextTable); // 只有在需要将 nextTable 放入 split_parts 的时候才将 preTable 放进去,避免表格整体放入下一页的情况 而且是不存在
          // splitTables.push(preTable, nextTable);
          nextTable.page_number = pageNumber++;
          nextTable.page_index = 0;
          nextTable.origin = this;
          nextTable.page_break_type = BreakType.hard;
          // 因为到这儿了 就只有 cellsTraversedBySplitLine 里边有单元格了 所以根据这里边任意一个单元格的位置判断是否整个下移,
          // 不能用 cellsTraversedBySplitLine 里边的单元格来进行判断 因为不准确 得用判断硬分割的单元格的位置来判断是否 return [this]
          // if (cellsTraversedBySplitLine[0].position[0] === 0) return [this];
          const preTableNeedHandleLineCells = [];
          const shouldBeReassignedCells: Cell[] = [];
          // 分配被分割线穿过的单元格
          for (const cell of cellsTraversedBySplitLine) {
            const top = cell.getTop(unRowSize);
            const height = cell.getHeight(unRowSize);
            const bottom = top + height;

            // 需要拆分的单元格
            if (top < cellSplitLine && bottom > cellSplitLine) {
              const [c1, c2] = cell.split(
                splitLine - top,
                splitRowSizeIndex, // 被拆分的 row_size 的下标
                preTable,
                nextTable,
                BreakType.hard
              );
              const splitedCell = cell.getOrigin().split_parts.pop()!;
              splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

              preTable.children.push(c1);
              preTable.cellMap.set(c1.id, c1);
              preTableNeedHandleLineCells.push(c1);
              shouldBeReassignedCells.push(c2);

              const c2RealContentHeight = c2.getRealContentHeight();
              if (c2.rowspan === 1) {
                nextTableFirstRowSize = Math.max(
                  c2RealContentHeight,
                  nextTableFirstRowSize
                );
              }

              cell.resetSplitRowIndexs(); // 此时要重新更新 之前更新的不一定对了

              // preTable.addLinesThatAreNotDrawn(c1);
            } else {
              // 被分割线穿过的单元格 不可能被分配到上一页中去 只有可能分配到下一页中 因为只有可能将单元格拽到下一页中去,不可能增加页面高度,将放不下的单元格硬放到上一页中,撑大页面
              const newCell = new Cell(
                cell.editor,
                [0, cell.position[1]],
                cell.colspan,
                cell.rowspan,
                nextTable,
                cell.id
              );
              Cell.attrJudgeUndefinedAssign(newCell, cell);
              newCell.children = cell.children;
              newCell.paragraph = cell.paragraph;
              newCell.origin = cell.getOrigin();
              shouldBeReassignedCells.push(newCell);
            }
          }
          cellsTraversedBySplitLine.length = 0;
          splitLine = fixedSplitLine;
          // preTable.children.push(...tempPreTableChildren); // TODO 因为软分割的时候已经放进去了 所以不能直接赋值 preTable.children = [...tempPreTableChildren] 这样不行 暂时这样
          preTableNeedHandleLineCells.forEach((c) => {
            preTable.addLinesThatAreNotDrawn(c);
          });
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex);
          unRowSize.splice(0, splitRowSizeIndex);
          unRowSize[0] = Math.max(unRowSize[0], nextTableFirstRowSize);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex);
          unMinRowSize.splice(0, splitRowSizeIndex);
          splitedRowSizeNum += splitRowSizeIndex;
          if (
            headerCells.length > 0 &&
            preTable.page_number > this.page_number
          ) {
            splitedRowSizeNum -= fixedRowNum;
          }

          this.split_parts.push(nextTable);
          splitTables.push(nextTable);

          // unCells.unshift(...shouldBeReassignedCells);
          unshiftCells.unshift(...shouldBeReassignedCells);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }

          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum < splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i++;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum < splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[1], 0]);
              i++;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
          preTable = nextTable;
        } else {
          // 因为如果有 return [this] 的情况就已经 return 掉了 到这儿 肯定是要创建 nextTable 的,一旦创建 nextTable 就要往 split_parts 和 splitTalbes 里边放 并且修改好 nextTable 的各个属性值
          const nextTable = new Table(
            editor,
            this.id,
            this.group_id,
            this.col_size,
            [],
            [],
            parent,
            parent.left + parent.padding_left,
            parent.right - parent.padding_right,
            page.header.header_outer_bottom,
            false,
            SkipMode.ROW
          ); // 第二个表格一定在这一页的开头 所以 top 值等于这个
          this.split_line_arr.push(
            (this.split_line_arr[this.split_line_arr.length - 1] || 0) +
            splitLine
          ); // 每次增加一个表格的时候 this.split_line_arr 就要追加一个线的高度

          // this.split_parts.push(preTable, nextTable); // 只有在需要将 nextTable 放入 split_parts 的时候才将 preTable 放进去,避免表格整体放入下一页的情况 而且是不存在
          // splitTables.push(preTable, nextTable);
          nextTable.page_number = pageNumber++;
          nextTable.page_index = 0;
          nextTable.origin = this;
          nextTable.page_break_type = BreakType.soft;

          const preTableNeedHandleLineCells = [];
          let nextTableFirstRowSize = 0; // 表格拆分 下一页表格 第一个 row_size 的应该的高度

          // 因为是软分割 要重新计算 被拆分的 row_size 先计算到 splitLine 之前的 row_size 的总高度 不能放到最下方  cell.split 要用 splitRowSizeIndex
          let totalRowSizeHeight = 0;
          for (let i = 0; i < unRowSize.length; i++) {
            if (totalRowSizeHeight + unRowSize[i] > splitLine) {
              splitRowSizeIndex = i;
              break;
            }
            totalRowSizeHeight += unRowSize[i];
          }
          // 应该被重新分配的单元格集合
          const shouldBeReassignedCells: Cell[] = []; // 为了保证顺序 所以用这个数组过渡 不能直接循环里边用 unCells.unshift(c2) 添加
          const rowspanArr: number[] = [];
          let nextTableRowSize = 0;
          for (const cell of cellsTraversedBySplitLine) {
            const top = cell.getTop(unRowSize);
            const height = cell.getHeight(unRowSize);
            const bottom = top + height;
            if (top < splitLine && bottom > splitLine) {
              const [c1, c2] = cell.split(
                splitLine,
                splitRowSizeIndex,
                preTable,
                nextTable,
                BreakType.soft,
                unRowSize
              );

              // 因为虽然是被拆分成了两个单元格 但是下一个单元格就又往 unCells 里边放了 下一轮又可能是硬分割直接放在上一页表格 也有可能继续进行软分割
              // 所以下一次还是软分割的时候就要把原来放到 split_parts 里边的最后一个删除掉,因为又被拆分了 但是要做个标记 要是下次被硬分割直接放到上一页中去的时候
              // split_parts 里边就少一个了 所以要做标记循环的时候别忘了放进 split_parts 里边去
              const splitedCell = cell.getOrigin().split_parts.pop()!;
              splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

              preTable.children.push(c1);
              preTable.cellMap.set(c1.id, c1);
              preTableNeedHandleLineCells.push(c1);
              c2.position[0] = 0;
              shouldBeReassignedCells.push(c2); // 为了保证再进训话时候的顺序是对的，提供一个变量先收集一下 直接用 unCell.unshift(c2) 会导致顺序不对 因为会 unCells.unshift(...shouldBeReassignedCells)
              // cell.getOrigin().split_parts.push(c1, c2); // 不用在这儿 push cell.split 的时候就已经放进去了

              const c2RealContentHeight = c2.getRealContentHeight();
              if (c2.rowspan === 1) {
                nextTableFirstRowSize = Math.max(
                  c2RealContentHeight,
                  nextTableFirstRowSize
                );
              }
              if (rowspanArr.length === 0) {
                rowspanArr.push(c2.rowspan);
              } else if (c2.rowspan === rowspanArr[0]) {
                rowspanArr.push(c2.rowspan);
              }
              nextTableRowSize = Math.max(c2RealContentHeight);
              cell.resetSplitRowIndexs();
            } else {
              // 这些是直接放到下一页表格中去的
              const newCell = new Cell(
                copiedCell.editor,
                [0, cell.position[1]],
                cell.colspan,
                cell.rowspan,
                nextTable,
                cell.id
              );
              Cell.attrJudgeUndefinedAssign(newCell, cell);
              newCell.children = cell.children;
              newCell.paragraph = cell.paragraph;
              newCell.origin = cell.getOrigin();
              if (newCell.rowspan > 1 || newCell.colspan > 1) {
                for (let i = 0; i < newCell.rowspan - 1; i++) {
                  for (let j = 0; j < newCell.colspan; j++) {
                    nextTable.notAllowDrawLine.row.push([
                      newCell.position[0] + 1 + i,
                      newCell.position[1] + j,
                    ]);
                  }
                }
              }
              shouldBeReassignedCells.push(newCell);
            }
          }

          // unCells.unshift(...shouldBeReassignedCells);
          unshiftCells.unshift(...shouldBeReassignedCells);

          splitedRowSizeNum += splitRowSizeIndex;
          if (
            headerCells.length > 0 &&
            preTable.page_number > this.page_number
          ) {
            splitedRowSizeNum -= fixedRowNum;
          }
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex + 1);
          unRowSize.splice(0, splitRowSizeIndex + 1);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex + 1);
          unMinRowSize.splice(0, splitRowSizeIndex + 1);

          // 上边只是分配 row_size 接下来是要分割 row_size
          // 分割 row_size
          // 1. 计算 preTable 的 row_size 高度和(包括了应该被拆分还没被拆分的 row_size)
          const preTableSumRowSizeHasToBeSplit = preTable.row_size.reduce(
            (total, current) => total + current,
            0
          );
          // 将要被拆分的 row_size 的高度
          const rowSizeToBeSplit =
            preTable.row_size[preTable.row_size.length - 1];
          // 将要被拆分的 row_size 分配给 preTalbe 之后的剩余高度
          const restHeightOfToBeSplit =
            preTableSumRowSizeHasToBeSplit - splitLine;
          // 将要被拆分的 row_size 的高度减去应该分配给 preTable 的 row_size 之后的剩余高度，就是 preTable 最后一个 row_size 的高度
          preTable.min_row_size[preTable.min_row_size.length - 1] =
            preTable.row_size[preTable.row_size.length - 1] =
            rowSizeToBeSplit - restHeightOfToBeSplit;

          // 应该分配给 nextTable 的 row_size 的高度，就是被拆分的 row_size 的高度，减去分配给 preTable 的 row_size 的高度
          let rowSizeAssignedToNextTable =
            rowSizeToBeSplit - preTable.row_size[preTable.row_size.length - 1];

          const minRowSizeAssignedToNextTable =
            rowSizeAssignedToNextTable < Config.min_row_size
              ? Config.min_row_size
              : rowSizeAssignedToNextTable;
          unMinRowSize.unshift(minRowSizeAssignedToNextTable);
          rowSizeAssignedToNextTable =
            rowSizeAssignedToNextTable > nextTableFirstRowSize
              ? rowSizeAssignedToNextTable
              : nextTableFirstRowSize;
          unRowSize.unshift(
            Math.max(rowSizeAssignedToNextTable, minRowSizeAssignedToNextTable)
          );
          if (
            rowspanArr.length === cellsTraversedBySplitLine.length &&
            rowspanArr[0] !== 1
          ) {
            // 如果出现软分割 有可能分配的 rowsize 不对,所以多退少补
            const rowspan = rowspanArr[0];
            const rowsizeArr = unRowSize.slice(0, rowspan);
            const rowsizeTotal = rowsizeArr.reduce((t, c) => t + c, 0);
            if (rowsizeTotal !== nextTableRowSize) {
              const difference = rowsizeTotal - nextTableRowSize;
              rowsizeArr[rowspan - 1] -= difference;
              unRowSize.splice(0, rowsizeArr.length, ...rowsizeArr);
            }
          }
          preTable.page_break_type = BreakType.soft;
          preTableNeedHandleLineCells.forEach((c) => {
            preTable.addLinesThatAreNotDrawn(c);
          });

          splitLine = fixedSplitLine;
          cellsTraversedBySplitLine.length = 0;
          this.split_parts.push(nextTable);
          splitTables.push(nextTable);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }

          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum <= splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex + 1) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              unOpacityHorizontal.unshift([1, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i += 2;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum <= splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[0], 1]);
              unOpacityVertical.unshift([coordinate[0], 0]);
              i += 2;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
          preTable = nextTable;
        }
      }
    }

    // FIXME 如果有比如说一个单元格无限软分割的情况也不会到这儿来 在循环 unCells 的时候,就会继续放入到 unCells 里边去 确保走完循环之后就是最后一页的情况
    // 因为此处的逻辑 完全按照最后一页的情况来处理 但是最后一页未必会进这个判断 比如 四行四列的有一列全合并表格将最后一行分配到下一页时会进来,有两行分配到下一页时就不用进来了
    // 因为在循环 unCells 的时候就会遇到 top > splitLine 的情况 那个时候处理完了就要清空 cellsTraverseBySplitLine
    // 到这儿了 cellsTraversedBySplitLine 还有的话就说明到了最后一页了 所以这里就只处理最后一页的情况(并不是说最后一页一定会在这里处理)
    if (!cellsTraversedBySplitLine.length) {
      // 最后循环结束了还是要将 preTable 放进去的
      preTable.row_size = [...unRowSize];
      preTable.min_row_size = [...unMinRowSize];
      preTable.notAllowDrawLine.changeOpacityRow = unOpacityHorizontal;
      preTable.notAllowDrawLine.changeOpacityCol = unOpacityVertical;
    }

    // TODO 先暂时用这个,没问题了再解决顺序问题,可能就几个有软分割的单元格才会有顺序问题,到时候可以把那几个专门拿出来进行排序,不用所有表格都进行排序
    splitTables.forEach((table) => {
      table.sortingCells();
    });
    return splitTables;
  }

  // TODO 大概逻辑是：拆分所有东西都永远只处理分配给上一页表格的逻辑
  // FIXME 软分割的时候分配 row_size 应该还是有问题的，下一页会多出来大片空白
  newSplitFn(split_line: number, parent: Cell): Table[] {
    this.resetSplitAttr();
    this.sortingCells(); // 必须保证循环的单元格是顺序的

    let splitLine = split_line; // 理论上表格应该拆分的位置距离当前页表格顶部的距离 第一次为传进来的值 之后拆分每次都可以根据配置计算出来了 就是当前页能盛放内容的最大高度(nextSplitLine)
    const nextSplitLine = this.editor.pages[0].contentHeight; // 下一页的表格必然是在开头的 理论分割线的位置就是该页的内容高度(就是该分割线距离该表格顶部的距离)

    const splitTables: Table[] = []; // 被拆分好的所有表格(最终返回出去的表格列表)
    const unCells = [...this.children]; // 未分配的单元格
    const unRowSize = [...this.row_size]; // 未分配的 row_size
    const unMinRowSize = [...this.min_row_size]; // 未分配的 min_row_size
    const unHorizontalLines = JSON.parse(
      JSON.stringify(this.notAllowDrawLine.changeOpacityRow)
    ); // 未分配的透明度的横线 所有被修改了透明度的横线 数据是无序的
    const unVerticalLines = JSON.parse(
      JSON.stringify(this.notAllowDrawLine.changeOpacityCol)
    ); // 未分配的透明度的竖线 所有被修改了透明度的竖线 数据是无序的

    let preTable = new Table(
      this.editor,
      this.id,
      this.group_id,
      this.col_size,
      [],
      [],
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      this.top,
      false,
      SkipMode.ROW
    );
    let nextTable = new Table(
      this.editor,
      this.id,
      this.group_id,
      this.col_size,
      [],
      [],
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      this.editor.pages[0].header.header_outer_bottom,
      false,
      SkipMode.ROW
    ); // 第二个表格一定在这一页的开头 所以 top 值等于这个

    const cellsTraversedBySplitLine: Cell[] = []; // 被分割线穿过的单元格 根据这些单元格判断分页应该是软分割还是赢分割
    let subRow = 0; // 修改单元格的 position 使用 (因为是一次性的拆分，所以假设被拆分成非常多页的时候，最后一页的单元格的位置就会很大，只减掉拆分位置处的 rowSizeIndexOfSplit 就不够了也不对了，所以要减掉这个值)

    while (unCells.length > 0) {
      let rowSizeIndexOfSplit: number = 0; // 被拆分的 row_size 的下标
      const cell = unCells.shift()!;
      // 要先 copy 出来一个 cell 否则修改位置等就会影响到 modelData 中的表格和单元格
      const newCell = new Cell(
        cell.editor,
        [...cell.position],
        cell.colspan,
        cell.rowspan,
        preTable,
        cell.id
      );
      Cell.attrJudgeUndefinedAssign(newCell, cell);
      newCell.children = cell.children;
      newCell.paragraph = cell.paragraph;
      newCell.origin = cell.getOrigin();
      const copiedCell = newCell; // 比调用 cell.copy 简化了
      copiedCell.page_break = cell.page_break;
      copiedCell.position[0] !== 0 && (copiedCell.position[0] -= subRow); // 因为等于 0 的是在单元格拆分的时候就直接修改过位置的是正确的 所以不需要减

      // 此时单元格还没分配 还不知道应该在哪一页 就是说不知道未来父表格是哪个
      // 此时计算的 cell.top 除了第一页的是对的 剩下的都是不对的 因为都用了最原始的表格的 row_size
      // TODO 用 unassignedRowSize 也是不对的 因为软分割的时候就不对了
      const top = copiedCell.getTop(unRowSize);
      const bottom = top + copiedCell.getHeight(unRowSize); // 跟 top 同样的道理 cell.height 里边计算使用的 row_size 是不对的

      // 下边这种判断上方表格是否装满的方法有点问题
      // 比如最左侧一列合并和最右侧一列合并 只能循环一行 就认为上方表格已经装满了 中间会忽略掉一些单元格(未分配给上方表格) 多循环一列也不行 中间的单元格还有可能有硬分割 多循环多少都不合适
      // 就是说这种判断方式是软分割还是硬分割都判断的不准
      // 有一个想法 就是根据面积来判断 但是又因为小数精度的问题 还有面积对比的时候还没有真正的进行分割 如果都是软分割 面积没问题 但是万一有个硬分割 整体上方表格总的面积就不准了
      // 我要是用这两个 双重判断呢? 或者说就按照现有的判断 生成完了表格 然后循环生成完的表格里边的单元格根据位置 col_size 和 row_size 判断是否已经装满了(这样也不行)
      // 我应该分两种情况:一种是有硬分割的情况 一种是没有硬分割的情况 就先循环所有单元格 直到所有的线都跟分割线比较过了 最后判断是否有硬分割 最后创建下一个表格就可以了
      // 或者说我应该循环所有单元格跟理论分割线的位置比较 一直循环到单元格的 top 值比理论分割线的位置大的时候 才说明上一页的表格装满了 这个应该是最正确的
      // 还要考虑一个单元格无线分割的情况,多个单元格合并成一个单元格再进行无线分割的情况

      // 只需要处理 1 和 2 这两种单元格
      // 1. 必然放在 preTable 中，不要 continue 掉，因为下方要判断到这儿上方表格是否装满了
      if (bottom <= splitLine) {
        if (cell.shouldPushSplitParts) {
          // 得用 cell.should... 判断，因为 copy 的时候没有该属性的赋值(没有必要)
          preTable.children.push(cell);
          preTable.cellMap.set(cell.id, cell);
          copiedCell.getOrigin().split_parts.push(cell); // 应该 push cell 不能 push copiedCell
          cell.resetSplitRowIndexs(); // 此时要重新更新 之前更新的不一定对了
        } else {
          preTable.children.push(copiedCell);
          preTable.cellMap.set(copiedCell.id, copiedCell);
        }
        preTable.addLinesThatAreNotDrawn(copiedCell);
      }

      // 2. 被分割线横穿过的单元格，只会被分配到下一页表格或者被拆分
      if (top <= splitLine && bottom > splitLine) {
        // 必须将毫无疑问会放到下方表格的单元格也放进去(top <= splitLine)，否则就没法判断上放表格是否已经装满了
        cellsTraversedBySplitLine.push(copiedCell);
      }

      // 3. 判断上方表格是否已装满 进行单元格的拆分 各种属性值的计算
      if (
        this.right - this.editor.config.page_padding_left - copiedCell.right <=
        2
      ) {
        // 写 2 是为了 比 1 保险 1 是测出来的正好相等的值
        if (bottom === splitLine || (top <= splitLine && bottom >= splitLine)) {
          // 上方表格已装满
          // TODO 此时是否要只处理 preTable 将应该放到下一页表格中去单元格 再重新放入到 unCells 中去 ？？？这样就是真的永远只处理 preTable
          let cellSplitLine = 0; // 要用这个判断 cellsTraversedByTheSplitLine 里边的单元格是否拆分和怎么分配
          this.split_line_arr.push(
            (this.split_line_arr[this.split_line_arr.length - 1] || 0) +
            splitLine
          );
          // 分开软分割的单元格和硬分割的单元格 并计算硬分割情况下的拆分行
          let hasHardSplit = false;
          for (const cell of cellsTraversedBySplitLine) {
            const cellTop = cell.getTop(unRowSize); // 应该要重新获取 不能直接用上边的 top 和 bottom 因为单元格不一样了
            const splitLineOfCell = splitLine - cellTop; // 分割线距离当前单元格顶部边界的距离
            if (cell.ifHardSplit(splitLineOfCell) || cellTop === splitLine) {
              rowSizeIndexOfSplit = Math.max(
                cell.position[0],
                rowSizeIndexOfSplit
              );
              cellSplitLine = cellTop;
              hasHardSplit = true;
              // 虽然一旦出现硬分割，表格就按照硬分割进行拆分了，但是 rowSizeIndexOfSplit 就会计算错
              // 比如一个四行两列的表格 将左侧单元格上下两两合并 在右侧第三行的单元格内往下拆分就会出问题 所以不能 break
            }
          }
          // 计算软分割情况下的拆分行号 并且拆分 row_size 和 min_row_size
          if (hasHardSplit) {
            if (cell.position[0] === 0) return [this]; // 此时整个表格都会移动到下一页中去，所以就不用继续往下走了
            // 分配被分割线穿过的单元格
            const shouldBeReassignedCells: Cell[] = [];
            for (const traversedCell of cellsTraversedBySplitLine) {
              const traversedTop = traversedCell.getTop(unRowSize);
              const height = traversedCell.getHeight(unRowSize);
              const traversedBottom = traversedTop + height;
              if (
                traversedTop < cellSplitLine &&
                traversedBottom > cellSplitLine
              ) {
                const [c1, c2] = traversedCell.split(
                  splitLine - traversedTop, // 应该重新计算 不能直接用 cellSplitLine 因为 cellSplitLine 并不是单元格内的分割线，就是说不是相对于该单元格顶部的距离，而是硬分割时距离表格顶部的距离
                  rowSizeIndexOfSplit,
                  preTable,
                  nextTable,
                  BreakType.hard
                );

                const splitedCell = traversedCell
                  .getOrigin()
                  .split_parts.pop()!; // 要删除一个
                splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

                preTable.addLinesThatAreNotDrawn(c1);
                preTable.children.push(c1);
                preTable.cellMap.set(c1.id, c1);
                c2.position[0] = 0;
                shouldBeReassignedCells.push(c2);
              } else {
                const newCell = new Cell(
                  copiedCell.editor,
                  [0, traversedCell.position[1]],
                  traversedCell.colspan,
                  traversedCell.rowspan,
                  nextTable,
                  traversedCell.id
                );
                Cell.attrJudgeUndefinedAssign(newCell, traversedCell);
                newCell.children = traversedCell.children;
                newCell.paragraph = traversedCell.paragraph;
                newCell.origin = traversedCell.getOrigin();
                shouldBeReassignedCells.push(newCell);
              }
            }
            unCells.unshift(...shouldBeReassignedCells);
            // 注意顺序，分配 rowSize 和 minRowSize 要在分配穿过的单元格之后 因为之前要用 rowSize 计算
            preTable.row_size = unRowSize.slice(0, rowSizeIndexOfSplit);
            unRowSize.splice(0, rowSizeIndexOfSplit);
            preTable.min_row_size = unMinRowSize.slice(0, rowSizeIndexOfSplit);
            unMinRowSize.splice(0, rowSizeIndexOfSplit);

            // 处理表格线 ↓
            // 硬分割 下方的表格会多出一条横线  所以changeOpacityRow 横坐标要多出一组改变横坐标的  changeOpacityCol 也要多出一组 改变的是横坐标
            // 分配横线
            for (let i = 0; i < unHorizontalLines.length;) {
              const numbers = unHorizontalLines[i]; // 横线的编号
              const numbersX = numbers[0] - subRow; // 横线编号中的横坐标值
              if (numbersX <= preTable.row_size.length) {
                // 横坐标值小于等于 preTable 拥有的 row_size 的个数 就该分配到上方表格
                unHorizontalLines.splice(i, 1);
                preTable.notAllowDrawLine.changeOpacityRow.push([
                  numbersX,
                  numbers[1],
                ]);
                if (numbersX === preTable.row_size.length) {
                  // 因为是硬分割 该线是上方表格最底部的线，也应该是下方表格最顶部的线 所以下个表格也应该有
                  nextTable.notAllowDrawLine.changeOpacityRow.push([
                    0,
                    numbers[1],
                  ]);
                }
              } else {
                i++;
              }
            }

            // 分配竖线
            for (let i = 0; i < unVerticalLines.length;) {
              const numbers = unVerticalLines[i]; // 竖线的编号
              const numbersX = numbers[1] - subRow; // 竖线编号中的横坐标值
              if (numbersX < preTable.row_size.length) {
                // 该判断条件跟分配横线有区别 这儿不能等于
                // 横坐标值小于等于 preTable 拥有的 row_size 的个数 就该分配到上方表格
                unVerticalLines.splice(i, 1);
                preTable.notAllowDrawLine.changeOpacityCol.push([
                  numbers[0],
                  numbersX,
                ]);
              } else {
                i++;
              }
            }
            // 处理表格线 ↑

            // 前一个表格一获取了正确的 row_size nextTable 就要做相对应的改变
            preTable.page_break_type = BreakType.hard;
            subRow += rowSizeIndexOfSplit;
          } else {
            // 1. 计算被拆分的 row_size 的下标
            let rowSize2Top = 0;
            for (let i = 0; i < unRowSize.length; i++) {
              if (rowSize2Top + unRowSize[i] > splitLine) {
                rowSizeIndexOfSplit = i;
                break;
              }
              rowSize2Top += unRowSize[i];
            }

            // 2. 分配被分割线穿过的 cell 注意代码顺序
            let nextTableFirstRowSize = 0;
            const shouldBeReassignedCells: Cell[] = [];
            for (const traversedCell of cellsTraversedBySplitLine) {
              // 此时计算的横穿分割线的单元格的 top 和 bottom 值都是拆分前的值，根据该值进行拆分判断
              const traversedTop = traversedCell.getTop(unRowSize); // 此时的 unRowSize 是还没分配的
              const height = traversedCell.getHeight(unRowSize);
              const traversedBottom = traversedTop + height;
              // TODO 处理无限软分割的单元格 软分割的时候也是按照永远只装上一页表格的逻辑进行处理 应该分配到下一页表格中的单元格就先收集起来
              if (traversedTop < splitLine && traversedBottom > splitLine) {
                // 因为单元格的拆分用到了父级表格 所以在将 preTable 填满了之后，要将 preTable 赋值为 nextTable 就可以
                const [c1, c2] = traversedCell.split(
                  splitLine,
                  rowSizeIndexOfSplit,
                  preTable,
                  nextTable,
                  BreakType.soft,
                  unRowSize
                );
                // 因为经过 traversedCell.split 就将 c1 和 c2 放进了 traversedCell.getOrigin().split_parts 里边了 所以对现在的逻辑不适用了
                const splitedCell = traversedCell
                  .getOrigin()
                  .split_parts.pop()!; // 要删除一个
                splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

                preTable.children.push(c1);
                preTable.cellMap.set(c1.id, c1);
                preTable.addLinesThatAreNotDrawn(c1);
                c2.position[0] = 0;
                shouldBeReassignedCells.push(c2); // 为了保证再进训话时候的顺序是对的，提供一个变量先收集一下 直接用 unCell.unshift(c2) 会导致顺序不对
                // 此时就要计算好下一页表格的 row_size 修改好 unRowSize 否则的话，下个循环所用的 unRowSize 计算就不对了
                // FIXME 直接这样分配是不对的 合并单元格的情况就不对 只有一个 rowspan 的单元格这么算是对的 如果 rowspan > 1 就不一定对了
                // 所以如果用原来的 row_size(用应该分配给下方表格的第一个 row_size 的高度和剩下的 rowspan - 1 个 row_size 的高度计算和) 和 rowspan 计算的结果大于 c2RealContentHeight 就不用改变了 否则就需要改变
                // 此时 看看 unRowSize 应该就想起来怎么算了
                const c2RealContentHeight = c2.getRealContentHeight();
                if (c2.rowspan === 1) {
                  if (c2RealContentHeight > nextTableFirstRowSize) {
                    nextTableFirstRowSize = c2RealContentHeight;
                  }
                }
                // TODO 如果 c2.rowspan > 1 的时候 那么理论上应该分配给下方表格的 row_size 是多少就是多少 ??

                // 被拆分的单元格上也得有 split_parts 和  split_row_indexes
                // 其中 split_parts 已在 split 里边分配好了
                traversedCell.resetSplitRowIndexs();
              } else {
                const newCell = new Cell(
                  copiedCell.editor,
                  [0, traversedCell.position[1]],
                  traversedCell.colspan,
                  traversedCell.rowspan,
                  nextTable,
                  traversedCell.id
                );
                Cell.attrJudgeUndefinedAssign(newCell, traversedCell);
                newCell.children = traversedCell.children;
                newCell.paragraph = traversedCell.paragraph;
                newCell.origin = traversedCell.getOrigin();
                if (newCell.rowspan > 1 || newCell.colspan > 1) {
                  for (let i = 0; i < newCell.rowspan - 1; i++) {
                    for (let j = 0; j < newCell.colspan; j++) {
                      nextTable.notAllowDrawLine.row.push([
                        newCell.position[0] + 1 + i,
                        newCell.position[1] + j,
                      ]);
                    }
                  }
                }
                shouldBeReassignedCells.push(newCell);
              }
            }
            unCells.unshift(...shouldBeReassignedCells);

            // 如果是硬分割：
            // 就将找到的硬分割的单元格上方的所有 row_size 给上一页的表格 下方的所有 row_size 给下方的表格
            // 表现形式就是 直接截取 unassignedRowSize.slice(0, rowIndexOfSplitCell)
            // 如果是软分割：
            // 就要找到具体是哪个 row_size 软分割了 要将该 row_size 进行分割
            // TODO 搞清楚 软分割的时候 啥时候增加 row_size 因为可能有放不下字还是啥问题?
            // 必须先分配分配
            preTable.row_size = unRowSize.slice(0, rowSizeIndexOfSplit + 1);
            unRowSize.splice(0, rowSizeIndexOfSplit + 1); // 删除掉被分配给 preTable 的 row_size
            preTable.min_row_size = unMinRowSize.slice(
              0,
              rowSizeIndexOfSplit + 1
            );
            unMinRowSize.splice(0, rowSizeIndexOfSplit + 1); // 删除掉分配给 preTable 的 min_row_size

            // 分割 row_size
            // 1. 计算 preTable 的 row_size 高度和(包括了应该被拆分还没被拆分的 row_size)
            const preTableSumRowSizeHasToBeSplit = preTable.row_size.reduce(
              (total, current) => total + current,
              0
            );
            // 将要被拆分的 row_size 的高度
            const rowSizeToBeSplit =
              preTable.row_size[preTable.row_size.length - 1];
            // 将要被拆分的 row_size 分配给 preTalbe 之后的剩余高度
            const restHeightOfToBeSplit =
              preTableSumRowSizeHasToBeSplit - splitLine;
            // 将要被拆分的 row_size 的高度减去应该分配给 preTable 的 row_size 之后的剩余高度，就是 preTable 最后一个 row_size 的高度
            preTable.min_row_size[preTable.min_row_size.length - 1] =
              preTable.row_size[preTable.row_size.length - 1] =
              rowSizeToBeSplit - restHeightOfToBeSplit;

            // 应该分配给 nextTable 的 row_size 的高度，就是被拆分的 row_size 的高度，减去分配给 preTable 的 row_size 的高度
            let rowSizeAssignedToNextTable =
              rowSizeToBeSplit -
              preTable.row_size[preTable.row_size.length - 1];
            rowSizeAssignedToNextTable =
              rowSizeAssignedToNextTable > nextTableFirstRowSize
                ? rowSizeAssignedToNextTable
                : nextTableFirstRowSize;
            unRowSize.unshift(rowSizeAssignedToNextTable);
            const minRowSizeAssignedToNextTable =
              rowSizeAssignedToNextTable < Config.min_row_size
                ? Config.min_row_size
                : rowSizeAssignedToNextTable;
            unMinRowSize.unshift(minRowSizeAssignedToNextTable);
            preTable.page_break_type = BreakType.soft;
            // 软分割 处理表格线 👇
            // 软分割：是 preTable 多画了一条横线(行坐标为preTable.row_size.length) nextTable 多画了一条横线(行坐标为0)和两条竖线(行坐标为0)
            // 分配横线
            for (let i = 0; i < unHorizontalLines.length;) {
              const numbers = unHorizontalLines[i]; // 横线编号
              const numberX = numbers[0] - subRow; // 横线编号中的横坐标值
              if (numberX <= preTable.row_size.length) {
                unHorizontalLines.splice(i, 1);
                preTable.notAllowDrawLine.changeOpacityRow.push([
                  numberX,
                  numbers[1],
                ]);
                if (numberX === preTable.row_size.length) {
                  // 因为软分割 会多出来两条横线 所以下方表格应该追加两条横线
                  nextTable.notAllowDrawLine.changeOpacityRow.push([
                    0,
                    numbers[1],
                  ]);
                  nextTable.notAllowDrawLine.changeOpacityRow.push([
                    numbers[0] - Math.max(rowSizeIndexOfSplit, subRow),
                    numbers[1],
                  ]);
                }
              } else {
                i++;
              }
            }

            // 分配竖线
            for (let i = 0; i < unVerticalLines.length;) {
              const numbers = unVerticalLines[i]; // 竖线编号
              const numberX = numbers[1] - subRow; // 竖线编号中的横坐标值
              if (numberX < preTable.row_size.length) {
                unVerticalLines.splice(i, 1);
                preTable.notAllowDrawLine.changeOpacityCol.push([
                  numbers[0],
                  numberX,
                ]);
                if (numberX === preTable.row_size.length - 1) {
                  // 该竖线也应该在下个表格中
                  nextTable.notAllowDrawLine.changeOpacityCol.push([
                    numbers[0],
                    0,
                  ]);
                }
              } else {
                i++;
              }
            }
            // 软分割 处理表格线 👆
            subRow += rowSizeIndexOfSplit; // 是给下一页的单元格使用的 所以要放在最后
          }
          cellsTraversedBySplitLine.length = 0;
          splitLine = nextSplitLine;
          preTable.origin = this;
          preTable.page_break = true;
          splitTables.push(preTable);
          this.split_parts.push(preTable);
          preTable = nextTable;
          nextTable = new Table(
            this.editor,
            this.id,
            this.group_id,
            this.col_size,
            [...unRowSize],
            [...unMinRowSize],
            parent,
            parent.left + parent.padding_left,
            parent.right - parent.padding_right,
            this.editor.pages[0].header.header_outer_bottom, // 第二个表格 一定在这一页的开头 所以top值等于这个
            false,
            SkipMode.ROW
          );
        }
      }
    }

    // 最后循环结束了还是要将 preTable 放进去的
    preTable.row_size = [...unRowSize];
    preTable.min_row_size = [...unMinRowSize];
    preTable.origin = this;
    this.split_parts.push(preTable);
    preTable.page_break_type = BreakType.hard;
    splitTables.push(preTable);
    // 因为最后一个 perTable 不会走那个上方表格已经装满的判断 所以最后的表格是没有处理线的 要处理一下 全部都给 preTable 就可以 不用判断了
    for (const numbers of unHorizontalLines) {
      preTable.notAllowDrawLine.changeOpacityRow.push([
        numbers[0] - subRow,
        numbers[1],
      ]);
    }
    for (const numbers of unVerticalLines) {
      preTable.notAllowDrawLine.changeOpacityCol.push([
        numbers[0],
        numbers[1] - subRow,
      ]);
    }

    return splitTables;
  }

  /**
   * 调用该方法的表格 不一定是最原始的表格 有可能是拆分过好几次以后的最后那部分的表格
   * @param split_line 理论上应该拆分的位置 就是理论分割线在当前页距离当前页表格顶部边界的距离
   * @param parent Cell 因为页眉页脚的表格不会拆分 所以表格的 parent 就是 root_cell
   * @returns 被拆分过的两个表格
   * 硬分割：分割线正好在表格单元格的边界上
   * 软分割：分割线会穿过单元格内部
   * 1、先 new 出来两个表格
   * 2、记录分割线的位置
   * 3、根据单元格的 top 值跟分割线的位置相比较 找到硬分割的单元格 记录下分割的单元格的行和真实的拆分位置(cell.top)
   */
  split(split_line: number, parent: Cell): Table[] {
    // if (EditorLocalTest.transUse) {
    //   return this.testSplit(split_line, parent);
    //   // return this.newSplitFn(split_line, parent);
    // }
    // this.fn(split_line);
    const table1 = new Table(
      this.editor,
      this.id,
      this.group_id,
      this.col_size,
      this.row_size,
      this.min_row_size,
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      this.top,
      false,
      SkipMode.ROW
    );
    const table2 = new Table(
      this.editor,
      this.id,
      this.group_id,
      this.col_size,
      this.row_size,
      this.min_row_size,
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      this.editor.config.page_padding_top, // 第二个表格 一定在这一页的开头 所以top值等于这个
      false,
      SkipMode.ROW
    );

    // 原始表格时所有的分割属性重新初始化
    if (!this.origin) {
      this.resetSplitAttr();
    }
    this.recordSplitLine(split_line);
    table1.origin = table2.origin = this.getOrigin();

    let split_cell_row_index: number = 0;
    let page_break_line: number; // 功能跟 split_line 一样 只不过在硬分割的时候值要改变一下为 cell.top 软分割的时候 不用更改 就是 split_line

    // 遍历一遍，查找是否存在硬分割
    for (let i = 0; i < this.children.length; i++) {
      const cell = this.children[i];

      let hard_split_flag = false;

      // 不用考虑 cell.bottom === split_line 的情况，此情况肯定table至少有两行，且存在某个cell top = split_line 必存在一个cell bottom = split_line
      if (cell.top === split_line) {
        // 该单元格的top值 应该就是上边单元格的bottom值 所以只找top值相等的就可以了
        hard_split_flag = true;
      }

      if (cell.top < split_line && cell.bottom > split_line) {
        const cell_split_line = split_line - cell.top; // 分割线距离当前单元格顶部边界的距离

        hard_split_flag = cell.ifHardSplit(cell_split_line); // 分割线如果正好穿过单元格内第一行内容的话 不能将字给拆成上下两部分 所以要将那种情况看做硬分割 所以这里要做一个判断 仅仅靠那个 if 是不够的
      }
      // 该单元格是硬分割
      if (hard_split_flag) {
        // cell 在第一行，且分割线移到 cell 头部，整个表格下移
        if (cell.position[0] === 0) {
          return [this];
        }
        // 分割线在cell边界处
        table1.page_break_type = table2.page_break_type = BreakType.hard;

        page_break_line = cell.top;

        split_cell_row_index = cell.position[0];

        break;
      }
    }

    // TODO ？？先查找了一遍硬分割 就是说 优先 硬分割 为什么？ 就没有 先找到了 硬分割 然后软分割 分不了的情况吗？
    if (table1.page_break_type === BreakType.hard) {
      // 硬分割情况
      this.assembleSplitTable(
        // 装配拆分后的表格 就是拆分row_size, min_row_size, 还有单元格(单元格位置也是经过修改的)
        table1,
        table2,
        page_break_line!,
        split_cell_row_index,
        BreakType.hard
      );
      // 处理表格线的透明度 - 硬分割 ↓
      // 硬分割 下方的表格会多出一条横线  所以changeOpacityRow 横坐标要多出一组改变横坐标的  changeOpacityCol 也要多出一组 改变的是横坐标
      const origin_talbe =
        this.getOrigin().split_parts[this.getOrigin().split_parts.length - 1] ||
        this.getOrigin();
      // 根表格 所有修改透明度的 横线和竖线 编号
      const all_opacity_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      );
      const all_opacity_cols = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityCol)
      );
      // 1. 计算table1总共 有多少行
      const table1_row_size = table1.row_size.length;
      const table1_opacity_rows = []; // 计算出上个表格改透明度的下标
      for (let i = 0; i < all_opacity_rows.length;) {
        const horizontal_line_number = all_opacity_rows[i][0]; // 横坐标
        if (horizontal_line_number <= table1_row_size) {
          // 横坐标 小于 等于 row_size 就是属于table1的
          const numbers = all_opacity_rows.splice(i, 1); // 删除掉 给table1
          table1_opacity_rows.push([...numbers[0]]);
        } else {
          // 否则就是属于 table2的
          all_opacity_rows[i][0] -= table1_row_size;
          i++;
        }
      }
      // 经过上个循环 table2会少一条横线 所以要 循环一遍table1的横线编号 如果最后一行有 那么table2第一行就得有 加上
      for (let i = 0; i < table1_opacity_rows.length; i++) {
        if (table1_opacity_rows[i][0] === table1_row_size) {
          all_opacity_rows.push([0, table1_opacity_rows[i][1]]);
        }
      }
      table1.notAllowDrawLine.changeOpacityRow = table1_opacity_rows;
      table2.notAllowDrawLine.changeOpacityRow = all_opacity_rows;

      const table1_opacity_cols = []; // 计算出上个表格的透明度数组
      for (let i = 0; i < all_opacity_cols.length;) {
        const vertical_line_number = all_opacity_cols[i][1];
        if (vertical_line_number <= table1_row_size - 1) {
          const numbers = all_opacity_cols.splice(i, 1);
          table1_opacity_cols.push([...numbers[0]]);
        } else {
          all_opacity_cols[i][1] -= table1_row_size;
          i++;
        }
      }
      table1.notAllowDrawLine.changeOpacityCol = table1_opacity_cols;
      table2.notAllowDrawLine.changeOpacityCol = all_opacity_cols;
      // 处理表格线的透明度 ↑
    } else {
      // TODO ？？没有一个单元格能够硬分割 才会进这里进行软分割
      // 进入此行，说明是软分割,
      let split_row_top = 0; // 其实就是row_size相加值 就是单元格上方每行的高度和 是个过渡变量 没什么实际用途
      let split_row_index = 0; // 被分割线分割的虚拟单元格的索引

      for (let i = 0; i < this.row_size.length; i++) {
        if (split_row_top + this.row_size[i] > split_line) {
          // TODO ?? split_line 是距离当前页的表格顶部边界的距离 this 也不是原始表格 拆分多页的表格 this 也只是当前要被拆分的表格
          split_row_index = i;
          break;
        }
        split_row_top += this.row_size[i];
      }
      this.assembleSplitTable(
        table1,
        table2,
        split_line,
        split_row_index, // 软分割的时候 说明 这个 row_size 应该被拆分
        BreakType.soft
      );
      // 软分割 - 处理表格线的透明度 代码必须写在这个else的分支 最后 table1才有东西 ↓
      // 就一个单元格来说 - 借鉴硬分割：是table2多画一条横线(行坐标为0)  那么软分割：是table1多画了一条横线(行坐标为table1.row_size.length) table2多画了一条横线(行坐标为0)和两条竖线(行坐标为0)
      // table1最后一个条横线和table2第一条横线就不管了,因为他们肯定是要画出来的, 然后就是根据table1的两条竖线画talbe2的两条竖线(table2多画的两条竖线依赖于table1行坐标为table1.row_size.length - 1的竖线)  只管竖线
      const origin_talbe =
        this.getOrigin().split_parts[this.getOrigin().split_parts.length - 1] ||
        this.getOrigin(); // 这个获取的是：新拆分之前的上一页的表格
      // 根表格 所有修改透明度的 横线和竖线 编号
      const all_opacity_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      );
      const backup_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      ); // 备用 因为all_opacity_rows会被修改
      const all_opacity_cols = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityCol)
      );
      // 1. 计算table1总共 有多少行
      const table1_row_size = table1.row_size.length;
      const table1_opacity_rows = []; // 计算出上个表格改透明度的下标
      for (let i = 0; i < all_opacity_rows.length;) {
        const horizontal_line_number = all_opacity_rows[i][0]; // 横坐标
        // 如果原表格的最后一条横线 是透明的 那么第二个表格的最后一条横线也是透明的
        if (horizontal_line_number < table1_row_size) {
          // 横坐标 小于 row_size 就是属于table1的
          const numbers = all_opacity_rows.splice(i, 1); // 删除掉
          table1_opacity_rows.push([...numbers[0]]); // 给table1
        } else {
          // 否则就是属于 table2 的
          all_opacity_rows[i][0] -= table1_row_size - 1;
          i++;
        }
      }
      // 循环备用的那个数组
      for (let i = 0; i < backup_rows.length; i++) {
        const horizontal_line_number = backup_rows[i][0]; // 横坐标
        if (horizontal_line_number === origin_talbe.row_size.length) {
          table1_opacity_rows.push([table1.row_size.length, backup_rows[i][1]]);
        }
        if (horizontal_line_number === 0) {
          all_opacity_rows.push([0, backup_rows[i][1]]);
        }
      }
      table1.notAllowDrawLine.changeOpacityRow = table1_opacity_rows;
      table2.notAllowDrawLine.changeOpacityRow = all_opacity_rows;

      const table1_opacity_cols = []; // 计算出table1的透明度数组
      for (let i = 0; i < all_opacity_cols.length;) {
        const vertical_line_number = all_opacity_cols[i][1]; // 行坐标
        if (vertical_line_number < table1_row_size) {
          // 行坐标小于 table1_row_size的 是属于table1的
          const numbers = all_opacity_cols.splice(i, 1);
          table1_opacity_cols.push([...numbers[0]]);
        } else {
          // 否则就是属于talbe2的
          all_opacity_cols[i][1] -= table1_row_size - 1;
          i++;
        }
      }
      // 经过上方的循环 table2 应该是少两条竖线 和 一条横线的 (横线先不管  竖线得画上)
      for (let i = 0; i < table1_opacity_cols.length; i++) {
        if (table1_opacity_cols[i][1] === table1_row_size - 1) {
          // 就看最后一行的
          all_opacity_cols.push([table1_opacity_cols[i][0], 0]);
        }
      }
      table1.notAllowDrawLine.changeOpacityCol = table1_opacity_cols;
      table2.notAllowDrawLine.changeOpacityCol = all_opacity_cols;
      // 软分割 - 处理表格线的透明度 ↑
    }

    // 处理表格线  ↓
    const table1_cells = table1.children;
    const table2_cells = table2.children;
    for (const cell of table1_cells) {
      const res = cell.getInsideLineNumber();
      table1.notAllowDrawLine.row.push(...res.res_rows);
      table1.notAllowDrawLine.col.push(...res.res_cols);
    }
    for (const cell of table2_cells) {
      const res = cell.getInsideLineNumber();
      table2.notAllowDrawLine.row.push(...res.res_rows);
      table2.notAllowDrawLine.col.push(...res.res_cols);
    }
    // 处理表格线  ↑

    // 目标：固定表头
    // 思考逻辑：处理时机就是在这个方法里边，就是在 modelData 转为 viewData 的时候，将第一页表格中表头的所有单元格都放到下一页中的开头去，然后再修改 row_size 和 min_row_size 等各种属性
    // 表头是不能够被拆分的 甚至说表头 下方必须有一行 表头也不能在第一页的最后 如果出现那些情况就让固定表头的功能失效
    // 固定表头实现 ↓
    const originTable = this.getOrigin();
    const rowNum = originTable.fixed_table_header_num; // 表头总共四行 从1开始数的
    const cancel_fixed = table1.children.every(
      (cell) => cell.position[0] < rowNum
    ); // 如果第一页的单元格 全部都是表头中的 那么就应该 取消固定表的功能
    if (this.fixed_table_header_num && !cancel_fixed) {
      const headerCells = originTable.children.filter(
        (cell) => cell.position[0] < rowNum
      );

      const firstRowHeight = originTable.row_size.slice(0, rowNum);
      // 所有单元格的位置 行数 暂时都加1
      table2.children.forEach((cell) => {
        cell.position[0] += rowNum;
      });
      table2.children.unshift(...headerCells);
      table2.row_size.unshift(...firstRowHeight);
      table2.min_row_size.unshift(...this.min_row_size.slice(0, rowNum));

      const notDrawRow: number[][] = [];
      originTable.notAllowDrawLine.row.forEach((rowLine) => {
        if (rowLine[0] < rowNum) {
          notDrawRow.push(rowLine);
        }
      });
      table2.notAllowDrawLine.row.forEach((position) => {
        position[0] += rowNum;
      });
      table2.notAllowDrawLine.row.unshift(...notDrawRow);

      const opacityRow: number[][] = [];
      originTable.notAllowDrawLine.changeOpacityRow.forEach((rowLine) => {
        if (rowLine[0] < rowNum) {
          opacityRow.push(rowLine);
        }
      });
      table2.notAllowDrawLine.changeOpacityRow.forEach((position) => {
        position[0] += rowNum;
      });
      table2.notAllowDrawLine.changeOpacityRow.unshift(...opacityRow);

      const notDrawCol: number[][] = [];
      originTable.notAllowDrawLine.col.forEach((colLine) => {
        if (colLine[1] < rowNum) {
          notDrawCol.push(colLine);
        }
      });
      table2.notAllowDrawLine.col.forEach((position) => {
        position[1] += rowNum;
      });
      table2.notAllowDrawLine.col.unshift(...notDrawCol);

      const opacityCol: number[][] = [];
      originTable.notAllowDrawLine.changeOpacityCol.forEach((colLine) => {
        if (colLine[1] < rowNum) {
          opacityCol.push(colLine);
        }
      });
      table2.notAllowDrawLine.changeOpacityCol.forEach((position) => {
        position[1] += rowNum;
      });
      table2.notAllowDrawLine.changeOpacityCol.unshift(...opacityCol);
      table1.fixed_table_header_num = table2.fixed_table_header_num =
        originTable.fixed_table_header_num;
    }
    // 固定表头实现 ↑

    return [table1, table2];
  }

  /**
   * 组装拆分后的两个表格
   * 1、拆分 row_size 属性给 拆分后的两个表格
   * 2、拆分单元格给 拆分后的两个表格
   * @param table1
   * @param table2
   * @param split_line 表格真实的分割线 硬分割时为：cell.top值(cell是拆分之前分割线下方的cell) 软分割的时候 就是split_line 理论上分割线 距离当前页表格顶部边界的距离
   * @param split_row_index 硬分割时表示分割线下方行的行号(cell.position[0],cell是分割线下方的cell)，软分割时代表被拆分行的行号(应该被拆分的row_size的下标)
   * @param break_type
   */
  assembleSplitTable(
    table1: Table,
    table2: Table,
    split_line: number,
    split_row_index: number,
    break_type: BreakType
  ) {
    // 因为有过渡变量 table1_split_row_index 和 截取的时候 两个表格 用的变量不一样 所以硬分割的时候 正好分割 row_size 和 min_row_size 在软分割的时候 应该被拆分的 row_size 和 min_row_size 被分给了上下两个表格(两个表格都有了这个值)
    const table1_split_row_index =
      break_type === BreakType.hard ? split_row_index : split_row_index + 1;
    // 分配 row_size 和 min_row_size 给两个表格
    table1.row_size = this.row_size.slice(0, table1_split_row_index);
    table1.min_row_size = this.min_row_size.slice(0, table1_split_row_index);
    table2.row_size = this.row_size.slice(split_row_index);
    table2.min_row_size = this.min_row_size.slice(split_row_index);
    table1.fullPage = table2.fullPage = this.fullPage || false;
    // 软分割时要设置被分割后 table1 末尾行与 table2 首行的 min_row_size，然后在后面调用 refreshTableRowSize 中依照 min_row_size 与内容高度进行重置两个 table 的 row_size
    if (break_type === BreakType.soft) {
      const sumTable1RowSize = this.row_size // 第一个表格 所有的 row_size 的集合
        .slice(0, table1_split_row_index)
        .reduce((num1: number, num2: number) => {
          return num1 + num2;
        });
      const ori_table1_end_row_size = this.row_size[table1.row_size.length - 1]; // ？？ 在原始表格中 要拆分的这一行的 row_size 大小

      table1.min_row_size[table1.row_size.length - 1] = // table1 最后一个 min_row_size 的 min_row_size
        ori_table1_end_row_size - (sumTable1RowSize - split_line);
      // table2.min_row_size[0] = sumTable1RowSize - split_line;
      const calc_row_size =
        this.min_row_size[split_row_index] -
        table1.min_row_size[table1.row_size.length - 1];
      table2.min_row_size[0] =
        calc_row_size < Config.min_row_size
          ? Config.min_row_size
          : calc_row_size;
    }

    const cellTopMap: any = {};
    this.row_size.forEach((rs, index, array) => {
      if (index === 0) {
        cellTopMap[index] = 0;
      } else {
        cellTopMap[index] = array
          .slice(0, index)
          .reduce((total, current) => total + current);
      }
    });

    // 分配 cell 给表格 this 指的是从当前页要拆分的表格(不包括已经拆分好的 上一页的表格数据了 就是要将 this 拆成两个表格)
    for (let i = 0; i < this.children.length; i++) {
      const cell = this.children[i];
      const top = cellTopMap[cell.position[0]];
      const bottom = top + cell.height;
      if (top < split_line && bottom > split_line) {
        const cells = cell.split(
          // 之前先找硬分割 只是计算 split_line ？？ 这才是真正的 拆分表格，本质上就是拆分单元格
          split_line,
          split_row_index,
          table1,
          table2,
          break_type
        );
        table1.children.push(cells[0]);
        table1.cellMap.set(cells[0].id, cells[0]);
        cells[1].position[0] = 0;
        table2.children.push(cells[1]);
        table2.cellMap.set(cells[1].id, cells[1]);
        // cell.setModelCellWhenSplitTbl(); // TODO  这里也不需要进行 重置 cell 的 parent 吧
      } else if (bottom <= split_line) {
        // 应该放到 上一个表格中
        this.pushCellCopy2Table(table1, cell);
      } else if (top >= split_line) {
        // 该单元格 应该属于 下一页的表格2
        // 原来是进行了 copy 因为改变这个 cell modelData 上的 cell 也会改变 他们是同一个东西 最起码位置信息就要改变 但是还不能影响到 modelData
        const new_cell = new Cell(
          cell.editor,
          [cell.position[0] - split_row_index, cell.position[1]],
          cell.colspan,
          cell.rowspan,
          table2,
          cell.id
        );
        Cell.attrJudgeUndefinedAssign(new_cell, cell);
        new_cell.children = cell.children;
        new_cell.paragraph = cell.paragraph;
        // judgeUndefinedAssign(new_cell, cell, ["padding_bottom", "style", "padding_top", "padding_right", "padding_left", "vertical_align", "is_show_slash_up", "is_show_slash_down", "hf_part", "children", "paragraph"]);
        // 因为当前表格如果为已经分割过的表格，此时所有的又重新copy,将会与原始单元格中记录的单元格不一致，所以此处记录id用户路径转换时作对比
        new_cell.origin = cell.getOrigin();

        // 清空split单元格属性，table1不需要清除 否则跨页时会将原数据中内容清掉

        // cell.getOrigin().resetSplitAttr();
        table2.children.push(new_cell);
        table2.cellMap.set(new_cell.id, new_cell);
      }
      cell.resetSplitRowIndexs();
    }
    table1.refreshTableRowSize();
    // 如果不调用该函数，则分割后的表格高度计算不正确，会有死循环的情况
    table2.refreshTableRowSize();

    // table1.row_size[table1.row_size.length - 1] = table1.min_row_size[table1.min_row_size.length - 1];
    // table2.row_size[0] = table2.min_row_size[0];
    // table2.row_size[table2.row_size.length - 1] = table2.min_row_size[table2.min_row_size.length - 1];
    table1.page_break = table2.page_break = true; // 记录下来 说明该表格 是被拆分过的表格
  }

  splitTableByFullRowsWithAnchors({ start_row_index, end_row_index }: { start_row_index: number, end_row_index: number } ) {
    const editor = this.editor;
    const originAdminMode = editor.adminMode;
    editor.adminMode = true;
    let start = 0;
    let rowspan = Math.max(1, start_row_index);
    let cells: Cell[] = [];
    const tables: Table[] = [];
    for (const cell of this.children) {
      const startRowIndex = cell.start_row_index;
      const endRowIndex = cell.end_row_index;
      const startColIndex = cell.start_col_index;
      if (startRowIndex >= start && endRowIndex < start + rowspan) {
        cells.push(cell);
      } else {
        // 一旦不一样了 可能换行了 也可能跨行了需要更多单元格放在一起了
        if (startColIndex === 0) {
          if (cells.length) {
            const newTable = someCells2Table(cells);
            tables.push(newTable);
          }
          cells = [cell];
          // 已经将能拆分的都放到一个表格里边了 然后就应该判断是否超过选区了 如果超过选区了 就是一个表格了
          start = startRowIndex;
          if (endRowIndex > end_row_index) {
            rowspan = this.row_size.length - end_row_index + 1;
            if (rowspan === this.row_size.length) return;
          } else {
            rowspan = cell.rowspan;
          }
        } else {
          cells.push(cell);
          rowspan += cell.rowspan - 1;
        }
      }
    }
    const lastTable = someCells2Table(cells);
    tables.push(lastTable);
    const cellIndex = this.cell_index;
    this.remove();
    this.editor.selection.setCursorPosition([cellIndex, 0]);
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      table.insert();
      if (i < tables.length - 1) {
        Table.insertBlankRow(Direction.down, this.editor);
        const position = cellIndex + i * 2 + 1;
        this.editor.selection.setCursorPosition([position, 0]);
        XField.insert(this.editor, { type: "anchor" });
        this.editor.selection.setCursorPosition([position + 1, 0]);
      }
    }
    this.editor.refreshDocument();
    editor.adminMode = originAdminMode;
  }

  /**
   * 硬分割时 将分配好的 cell copy 一下 放到该放入的 table 中 并返回 新表格
   * @param table
   * @param cell
   */
  pushCellCopy2Table(table: Table, cell: Cell): Cell {
    const new_cell = new Cell(
      cell.editor,
      [...cell.position],
      cell.colspan,
      cell.rowspan,
      table,
      cell.id
    );
    Cell.attrJudgeUndefinedAssign(new_cell, cell);
    new_cell.children = cell.children;
    new_cell.paragraph = cell.paragraph;
    // judgeUndefinedAssign(new_cell, cell, ["padding_bottom", "style", "padding_top", "padding_right", "padding_left", "vertical_align", "is_show_slash_up", "is_show_slash_down", "hf_part", "children", "paragraph"]);
    new_cell.origin = cell.getOrigin();
    table.children.push(new_cell);
    table.cellMap.set(new_cell.id, new_cell);
    return new_cell;
  }

  /**
   * 刷新表格row_size
   * 调用的地方：
   * 1. 在 assembleSplitTable 中调用了，assembleSplitTable 又只在 page.add 中的 table.split 方法中，也就是表格分页的逻辑中调用了
   * 2. 在 fixedHeight 固定单元格高度中调用了
   * 3. 在 updateRowBounding 中调用了，updateRowBounding ，这个方法调用的地方就多了
   */
  refreshTableRowSize() {
    this.row_size.fill(Config.min_row_size);
    const multi_row_cell: Cell[] = [];
    const single_row_cell: Cell[] = [];
    for (let i = 0; i < this.children.length; i++) {
      const cell = this.children[i];
      if (cell.rowspan > 1) {
        multi_row_cell.push(cell);
      } else {
        single_row_cell.push(cell);
      }
    }

    for (let i = 0; i < single_row_cell.length; i++) {
      const cell = single_row_cell[i];
      const cell_content_height = cell.getContentHeight();
      const rowIndex = cell.position[0];
      if (
        (cell_content_height > this.row_size[rowIndex] ||
          cell_content_height === 0) &&
        cell.set_cell_height.type !== "scroll"
      ) {
        const realHeight = cell.getRealContentHeight();
        this.updateRowSize(rowIndex, realHeight);
      }
    }
    // 合并单元按照row_size的索引排序，才能计算出正确的row_size
    multi_row_cell.sort(
      (a, b) => a.position[0] + a.rowspan - (b.position[0] + b.rowspan)
    );
    for (let i = 0; i < multi_row_cell.length; i++) {
      const cell = multi_row_cell[i];
      const cell_content_height = cell.getContentHeight();
      let pre_total_row_height = 0;
      for (
        let j = cell.position[0];
        j < cell.position[0] + cell.rowspan - 1;
        j++
      ) {
        pre_total_row_height += this.row_size[j];
      }
      const cell_last_row_height = cell_content_height - pre_total_row_height;
      const rowIndex = cell.end_row_index;
      const versionsList = cell.editor.document_meta?.versionList;
      const version = versionsList && versionsList[0] && versionsList[0].version;
      if (versionDiff(version, "10.5.14") >= 0) {
        if (
          (cell_last_row_height > this.row_size[rowIndex] ||
            cell_content_height === 0) &&
          cell.set_cell_height.type !== "scroll"
        ) {
          const realHeight = cell.getRealContentHeight();
          this.updateRowSize(rowIndex, realHeight - pre_total_row_height);
        }
      } else {
        if (
          (cell_last_row_height > this.row_size[rowIndex] ||
            cell_content_height === 0)
        ) {
          const realHeight = cell.getRealContentHeight();
          this.updateRowSize(rowIndex, realHeight - pre_total_row_height);
        }
      }

    }
    // 表格copy的时候 因为新表格单元格还不全 所以计算错误 此处做个修正
    for (let i = 0; i < this.row_size.length; i++) {
      if (this.row_size[i] < this.min_row_size[i]) {
        this.row_size[i] = this.min_row_size[i];
      }
    }
    // this.children.sort((a, b) => a.position[0] - b.position[0]);
  }

  // 居中方式不为top的单元格调用typesetting
  callTypesetting() {
    this.children.forEach((cell) => {
      if (cell.vertical_align !== "top") {
        cell.typesetting();
      }
    });
  }

  // 抽离draw方法内的共有代码
  drawLine(
    left: number,
    top: number,
    size: number,
    color: string,
    alpha: number,
    is_horizontal = true
  ) {
    Renderer.save(); // save translate restore 不能少
    left = Math.floor(left) - DEFINITION; // 原来是 + 0.5 但是单元格内边距为 0 的时候,啊这个字,会被线穿过,放大一点就没事,放在文本域里边,边框左移的更多 所以改成减
    // top = parseInt(top + "") ;
    Renderer.translate(left, top);
    size = Math.floor(size) + DEFINITION;
    if (is_horizontal) {
      // draw_line传入坐标， 起点永远是[0, 0] 终点永远是[宽度, 0] 因为始终当做只画单元格的上边线 即便是表格的最后一行 也无所谓 因为translate了top值 也被当做了单元格的上边线
      Renderer.draw_line([0, 0], [size, 0], color, alpha);
    } else {
      // 画竖线 起点永远是[0, 0], 终点永远是[0, row_size]
      Renderer.draw_line([0, 0], [0, size], color, alpha);
    }
    Renderer.restore();
  }

  // 绘制表格线 在draw方法中调用
  drawTableLine(editor: Editor) {
    if (
      !editor.is_edit_hf_mode &&
      this.parent !== editor.root_cell &&
      this.parent.hf_part !== "footer" &&
      !this.parent.id.startsWith("float-model")
    ) {
      // 只有页眉的表格线不绘制 页脚的表格线还是要绘制的
      return;
    }
    // 画横线竖线 不要合并为一个方法，会导致不太好理解
    // 画横线 数量应该是col_size.length + 1 里边循环col_size 是每条横线 的几个线段(这几个线段组成这个横线)
    const notDrawRow = this.notAllowDrawLine.row;
    const changeOpacityRow = this.notAllowDrawLine.changeOpacityRow;
    for (
      let row_size_index = 0;
      row_size_index <= this.row_size.length;
      row_size_index++
    ) {
      let top = this.row_size
        .slice(0, row_size_index)
        .reduce((prev, current) => prev + current, 0);
      top = Math.floor(top) - 0.5;
      this.col_size.forEach((col_size, col_size_index) => {
        // 第一步：找到不让画的线 return掉
        for (const arr of notDrawRow) {
          if (arr[0] === row_size_index && arr[1] === col_size_index) {
            return;
          }
        }

        // 第二步：找到要修改透明度的线，修改透明度
        let left = this.col_size
          .slice(0, col_size_index)
          .reduce((prev, current) => prev + current, 0);
        left = Math.floor(left) - 0.5;
        for (const arr of changeOpacityRow) {
          if (arr[0] === row_size_index && arr[1] === col_size_index) {
            // if (this.parent.hf_part === "footer" && !editor.is_edit_hf_mode) {
            // 如果编辑正文的时候 不显示页脚的表格线 就可以这么处理
            //   return;
            // }
            const top = this.row_size
              .slice(0, row_size_index)
              .reduce((prev, current) => prev + current, 0);
            this.drawLine(
              left,
              top,
              col_size,
              "#000",
              editor.print_mode || editor.view_mode === "view" ? 0 : 0.2
            );
            return;
          }
        }

        this.drawLine(left, top, col_size, "#000", 1);
      });
    }

    // 画竖线 画竖线的条数应该是this.col_size的长度+1条 里边循环row_size 就是要画 该条竖线的几个线段
    const notDrawCol = this.notAllowDrawLine.col;
    const changeOpacityCol = this.notAllowDrawLine.changeOpacityCol;
    for (
      let col_size_index = 0;
      col_size_index <= this.col_size.length;
      col_size_index++
    ) {
      // 要画的这条竖线 距离该表格左边的距离
      let left = this.col_size
        .slice(0, col_size_index)
        .reduce((prev, current) => prev + current, 0);
      left = Math.floor(left) - 0.5;
      this.row_size.forEach((row_size, row_size_index) => {
        // 第一步：找到合并单元格记录的 不让画的线 直接return掉 在forEach里边的return 不会终止forEach的循环
        for (const arr of notDrawCol) {
          if (arr[0] === col_size_index && arr[1] === row_size_index) {
            return;
          }
        }

        // 第二步：找到要透明的线，绘制出透明度来
        // top 是计算出 要画的这条竖线 距离该表格顶部的距离
        let top = this.row_size
          .slice(0, row_size_index)
          .reduce((prev, current) => prev + current, 0);
        top = Math.floor(top) - 0.5;
        for (const arr of changeOpacityCol) {
          if (arr[0] === col_size_index && arr[1] === row_size_index) {
            const left = this.col_size
              .slice(0, col_size_index)
              .reduce((prev, current) => prev + current, 0);
            this.drawLine(
              left,
              top,
              row_size,
              "#000",
              editor.print_mode || editor.view_mode === "view" ? 0 : 0.2,
              false
            );
            return;
          }
        }

        // 第三步：如果顺利走到这儿的话 就正常画线
        this.drawLine(left, top, row_size, "#000", 1, false);
      });
    }
  }

  draw(editor: Editor) {
    Renderer.save();

    Renderer.translate(this.left, this.top);
    if (this.resize_horizontal_line !== 0) {
      Renderer.save();

      Renderer.draw_horizontal(
        this.resize_horizontal_line,
        0,
        this.width,
        "#1a73e8"
      );

      Renderer.restore();
    }
    // 左右调整
    if (this.resize_vertical_line !== 0) {
      Renderer.save();

      // Renderer.draw_vertical的参数是：x, top, bottom, color
      Renderer.draw_vertical(
        this.resize_vertical_line,
        0,
        this.height,
        "#1a73e8"
      );

      Renderer.restore();
    }
    if (this.editableInFormMode && !editor.print_mode) {
      Renderer.save();
      Renderer.draw_table_corner(0, 0, 1);
      Renderer.draw_table_corner(this.width, 0, 2);
      Renderer.draw_table_corner(0, this.height, 3);
      Renderer.draw_table_corner(this.width, this.height, 4);
      Renderer.restore();
    }
    //在左上角画一个全选的符号
    if (
      editor.internal.isCrossTable &&
      this.id === editor.internal.currentTableId
    ) {
      const selectAllBtnPosition = [-6, 0];
      Renderer.drawArrow(selectAllBtnPosition, 1, "right");
      Renderer.drawArrow(selectAllBtnPosition, 1, "left");
      Renderer.drawArrow(selectAllBtnPosition, 1, "bottom");
      Renderer.drawArrow(selectAllBtnPosition, 1, "top");
      Renderer.draw_stroke_rect(-11, -5, 10, 10);
    }
    const originTable = this.getOrigin()
    const cellsEachRow = new Map();
    // 这个还必须得保留 否则里边的文字 显示会错乱 把cell.draw内部画线的部分给注释掉了
    for (let i = 0; i < this.children.length; i++) {
      const cell = this.children[i];
      if (cell) {
        // 因为页眉页脚中的数据用的都是同一个 在 updateHeaderFooterInfo 方法中也没有更新表格的 page_number
        // 会导致 this.page_number 为 0 .top 就会报错
        // 解决此问题修改 pageNumber 赋值和下方的判断增加 (this.parent !== editor.current_cell)
        // TODO 席中枪 只绘制当前页页眉页脚
        const cellBottom =
          cell.bottom +
          this.editor.pages[(this.page_number || 1) - 1].top + // this.editor.pages[(this.page_number || 1) - 1] 中括号内不能写死 0 还有其他页正文中的表格计算
          this.top +
          editor.config.page_margin_bottom;
        const cellTop = cellBottom - cell.height;
        // 该判断并不是所有页眉页脚的单元格都进行绘制 外部绘制 page 的时候就有判断 只绘制视口内的页
        if (
          this.parent.hf_part ||
          isInViewport(this.editor, cellTop, cellBottom) ||
          this.parent.id.startsWith("float-model")
        ) {
          Renderer.save();
          Renderer.translate(cell.left, cell.top);
          cell.draw(editor);
          if (this.editor.config.rowLineTypeExplain.includesTable) {
            // 有的医院表格里边不想绘制线 所以加个配置
            if (this.editor.config.rowLineType === RowLineType.SOLID) {
              // 因为这是分页的 viewData 所以用 originCell
              // 不考虑有合并拆分的情况
              const originCell = cell.getOrigin();

              const versionsList = cell.editor.document_meta?.versionList;
              const version = versionsList && versionsList[0] && versionsList[0].version;

              if (originCell.rowspan === 1 && (versionDiff(version, "10.9.20") < 0 ? originCell.colspan === 1 : true)) {
                if (cellsEachRow.has(cell.position[0])) {
                  cellsEachRow.get(cell.position[0]).push(cell);
                } else {
                  cellsEachRow.set(cell.position[0], [cell]);
                }
              }
            }
          }

          const originCell = cell.getOrigin();
          if (originCell.rowLineType === RowLineType.SOLID) {
            if (cellsEachRow.has(cell.position[0])) {
              cellsEachRow.get(cell.position[0]).push(cell);
            } else {
              cellsEachRow.set(cell.position[0], [cell]);
            }
          }

          if (this.editor.config.rowLineType === RowLineType.NO_MERGE_CELL || originTable.rowLineType === RowLineType.SOLID) {
            // 因为这是分页的 viewData 所以用 originCell
            // 不考虑有合并拆分的情况
            const versionsList = cell.editor.document_meta?.versionList;
            const version = versionsList && versionsList[0] && versionsList[0].version;
            if (originCell.rowspan === 1 && (versionDiff(version, "10.9.20") < 0 ? originCell.colspan === 1 : true)) {
              if (cellsEachRow.has(cell.position[0])) {
                cellsEachRow.get(cell.position[0]).push(cell);
              } else {
                cellsEachRow.set(cell.position[0], [cell]);
              }
            }
          }
          Renderer.restore();
        }
      }
    }
    const opacityRowLines = this.notAllowDrawLine.changeOpacityRow;


    // 处理单元格自己设置的是否展示行线 ↓
    for (const arr of cellsEachRow.entries()) {
      const cellsPerROw = arr[1];
      const index = cellsPerROw.findIndex(
        (c: Cell) => c.children.length > 1
      );
      const originCell = cellsPerROw[index > -1 ? index : 0].getOrigin();
      if (!originCell.rowLineType || (originCell.rowLineType === RowLineType.VOID)) continue;
      const rowHeight = originCell.children[0].height;
      for (const dCell of cellsPerROw) {
        let lineHeight = dCell.top;
        // 在这里绘制每个单元格里边的线
        if (
          !opacityRowLines.find(
            (lines) => lines[0] === dCell.position[0] + 1
          )
        ) {
          while (true) {
            lineHeight += rowHeight;
            if (lineHeight + rowHeight - 4 > dCell.bottom) {
              // lineHeight + rowHeight 是为了避免有挨得很近的线画出来不好看 -2 是因为有误差 去掉这个误差 尝试出来的 2
              break;
            }
            Renderer.draw_line(
              [dCell.left, lineHeight],
              [dCell.right, lineHeight],
              "#000",
              1,
              0.5
            );
          }
        }
      }
    }
    // 处理单元格自己设置的是否展示行线 ↑


    const flag = this.editor.config.rowLineType === RowLineType.NO_MERGE_CELL;
    if (this.editor.config.rowLineTypeExplain.includesTable || flag || (originTable.rowLineType === RowLineType.SOLID)) {
      if (this.editor.config.rowLineType === RowLineType.SOLID || flag || (originTable.rowLineType === RowLineType.SOLID)) {
        for (const arr of cellsEachRow.entries()) {
          const cellsPerRow = arr[1];
          if (cellsPerRow.length !== this.col_size.length && !flag && !(originTable.rowLineType === RowLineType.SOLID)) {
            cellsEachRow.delete(arr[0]);
          }
        }
        for (const arr of cellsEachRow.entries()) {
          const cellsPerROw = arr[1];
          const index = cellsPerROw.findIndex(
            (c: Cell) => c.children.length > 1
          );
          const originCell = cellsPerROw[index > -1 ? index : 0].getOrigin();
          const rowHeight = originCell.children[0].height;
          for (const dCell of cellsPerROw) {
            let lineHeight = dCell.top;
            // 在这里绘制每个单元格里边的线
            if (
              !opacityRowLines.find(
                (lines) => lines[0] === dCell.position[0] + 1
              )
            ) {
              while (true) {
                lineHeight += rowHeight;
                if (lineHeight + rowHeight - 4 > dCell.bottom) {
                  // lineHeight + rowHeight 是为了避免有挨得很近的线画出来不好看 -2 是因为有误差 去掉这个误差 尝试出来的 2
                  break;
                }
                Renderer.draw_line(
                  [dCell.left, lineHeight],
                  [dCell.right, lineHeight],
                  "#000",
                  1,
                  0.5
                );
              }
            }
          }
        }
      }
    }
    if (editor.print_mode) {
      if (editor.group_print && !(this.group && this.group.meta.print)) {
        Renderer.restore();
        return
      }
    }
    this.drawTableLine(editor); // 放在绘制文字(this.children.forEach...)的下边 线会盖住文字 就不会有文字放大以后盖住线的问题了
    Renderer.restore();
  }

  /**
   * 纵向调整table尺寸 如果是分页表格的话 调用该方法的表格就是 split_parts 中的表格 不是 modelData 中的表格
   * @param row_index 第几根横线 是在 viewData(split_parts中的表格) 中的 第几根线 表格第 0 行下边的线为 0 合并后隐藏的线也是计算的
   * 没有固定表头时的 计算逻辑是：
   * split_parts 中的每个表格都有 page_break_type 属性 为 0 说明该表格下方是软分割 为 1 说明是硬分割
   * 我点击的表格线在当前页的 row_index 就是传递进来的参数 如果都是硬分割的话 应该就是前边所有页的 row_size 个数和 加上 传递进来的 row_index 就是正对的 origin_table 中的 row_index
   * 如果遇到软分割的话 就在算 row_size 个数和的时候 减1 就可以了 再加上 当前传递进来的 row_index 就是 正对 origin_table 中的 row_index 了
   * 如果是软分割的话 会有一个单元格 跨了很多页 再拖动最后一页 第一行的时候 row_size 要算上前边所有页 row_size 的和 所以就只是判断 拖动的线(传入的row_index 是否为0 然后重新计算 height就可以了)
   *
   * 有固定表头时的 计算逻辑：
   * 就在计算 每页的 row_size 个数和的时候 减去固定的表头行数就可以了 就能计算出 对应的 origin_table 中的 row_index 了
   * 但是计算 height 的时候 也需要再进行判断 因为即便是一个单元格 软分割跨了多页 row_size 也不会只有一个了
   */
  resizeRowSizeByRowIndex(row_index: number) {
    if (this.resize_horizontal_line === 0) return;
    const current_row_pre_total_height = this.row_size
      .slice(0, row_index)
      .reduce((total, current) => total + current, 0);
    const origin_talbe = this.origin; // 考虑分页拖动

    if (origin_talbe?.split_parts) {
      // 说明该表格是分页后的表格
      for (let i = 0; i < origin_talbe.split_parts.length; i++) {
        // 目的就是找到要修改的 origin_table 中的 row_size 的下标（因为有固定表头和分页多出来的行，所以不是传递的 row_index 了）
        const talbe = origin_talbe.split_parts[i];
        let height = this.resize_horizontal_line;
        if (talbe === this) {
          let change_row_index = row_index;

          if (i > 0) {
            // 如果不是第一页表格
            let before_current_table_total_row_size_num = 0; // split_parts 中 前边所有页的 row_size 的个数和
            for (let j = 0; j < i; j++) {
              // 这个循环 只计算 before_current_table_total_row_size_num 值 i 是拖动线的当前表格 因为有参数 row_index 了 所以计算 之前的所有表格的 row_size 的个数
              let prev_row_size = origin_talbe.split_parts[j].row_size.length;
              if (origin_talbe.fixed_table_header_num) {
                // 固定表头的话 还要减去 固定的表头行数
                prev_row_size -= origin_talbe.fixed_table_header_num;
              }
              before_current_table_total_row_size_num += prev_row_size;
              origin_talbe.split_parts[j].page_break_type === BreakType.soft &&
                (before_current_table_total_row_size_num -= 1); // 如果是软分割的话 row_size 个数应该减1
            }

            // 如果拖动的是 软分割的情况下的 表格的第一行 那么就要重新计算 height的值
            // 有重复代码 先这样写 比较好理解
            // 不考虑固定表头 这个循环 重新计算 Height 值
            if (row_index === 0 && !origin_talbe.fixed_table_header_num) {
              // 因为 固定表头的话 传递的 row_index 不可能是0
              for (let j = i - 1; j >= 0; j--) {
                // 从上一个表格 开始 往上面的表格循环 就是倒着循环
                const prev_table = origin_talbe.split_parts[j]; // 上一个表格是软分割 才需要加 height
                if (prev_table.page_break_type === BreakType.soft) {
                  height += prev_table.row_size[prev_table.row_size.length - 1];
                  // 如果这个表格 row_size 只有一行 并且再上一个表格 还是软分割 就应该继续循环 继续加下去 否则 就没必须要再继续加了
                  // 这都没有考虑 固定表头的情况
                  if (
                    !(
                      prev_table.row_size.length === 1 &&
                      origin_talbe.split_parts[j - 1]?.page_break_type ===
                      BreakType.soft
                    )
                  ) {
                    break;
                  }
                  // 如果有固定表头的话 row_size 为表头的行数 加上1
                }
              }
            }

            // 考虑固定表头的情况
            if (
              origin_talbe.fixed_table_header_num &&
              row_index - origin_talbe.fixed_table_header_num === 0
            ) {
              // 固定表头的情况下 row_index 减去 固定的表头行数 如果为 0 才是真正的 第 0 行
              for (let j = i - 1; j >= 0; j--) {
                const prev_table = origin_talbe.split_parts[j]; // 上一页的表格
                if (prev_table.page_break_type === BreakType.soft) {
                  height += prev_table.row_size[prev_table.row_size.length - 1];
                }
                if (
                  !(
                    prev_table.row_size.length ===
                    origin_talbe.fixed_table_header_num + 1 &&
                    origin_talbe.split_parts[j - 1]?.page_break_type ===
                    BreakType.soft
                  )
                ) {
                  break;
                }
              }
            }
            change_row_index =
              before_current_table_total_row_size_num + row_index;
          }

          origin_talbe.row_size[change_row_index] = origin_talbe.min_row_size[
            change_row_index
          ] = height - current_row_pre_total_height;
          this.resize_horizontal_line = 0;

          origin_talbe.children.forEach((cell) => {
            if (
              cell.start_row_index <= change_row_index &&
              cell.end_row_index >= change_row_index
            ) {
              // 拖动表格线只会影响这些单元格的对齐方式，所以只有他们typesetting就可以了
              cell.typesetting();
            }
          });

          return;
        }
      }
    }

    this.row_size[row_index] = this.min_row_size[row_index] =
      this.resize_horizontal_line - current_row_pre_total_height;

    this.resize_horizontal_line = 0;

    this.children.forEach((cell) => {
      // 必须都要调用 因为拖动横线有合并单元格的话，不知道会影响哪个单元格 跟拖动竖线一样判断 child.end_row_index === row_index 这样不行
      if (
        cell.start_row_index <= row_index &&
        cell.end_row_index >= row_index
      ) {
        cell.typesetting();
      }
    });
  }

  /**
   * 横向调整table尺寸
   * @param col_index 拖动的是第几根竖线 被合并的线也是算数的，不是肉眼看到的第几根 没有合并拆分情况下的表格 第一列的右边的那条线为 0
   * @param left_col 紧挨着拖动线 最左侧的列
   * @param right_col 紧挨着拖动线 最右侧的列
   */
  resizeColSizeByColIndex(
    col_index: number,
    left_col: number,
    right_col: number
  ) {
    if (this.resize_vertical_line === 0) return;
    const cells_width = this.col_size
      .slice(0, left_col)
      .reduce((total, current) => total + current, 0);
    const prev_cells_width = this.col_size
      .slice(0, right_col + 1)
      .reduce((total, current) => total + current, 0);
    let modify_left_col_index = col_index; // 临时变量 因为col_index会减少
    let modify_right_col_index = col_index + 1; // 临时变量
    // 表格分页情况处理
    if (this.origin && this.origin.split_parts.length > 0) {
      //
      this.origin.split_parts.forEach((tbl) => {
        // 左侧单元格
        const cols_num_left = col_index - left_col + 1;
        for (let i = 0; i < cols_num_left; i++) {
          tbl.col_size[modify_left_col_index] =
            (tbl.resize_vertical_line - cells_width) / cols_num_left;
          modify_left_col_index--;
        }
        // 右侧单元格
        const cols_num_right = right_col - col_index;
        for (let i = 0; i < cols_num_right; i++) {
          tbl.col_size[modify_right_col_index] =
            (prev_cells_width - tbl.resize_vertical_line) / cols_num_right;
          modify_right_col_index++;
        }
        tbl.resize_vertical_line = 0;
        tbl.children.forEach((child) => {
          child.typesetting();
        });
      });
    } else {
      // 左侧单元格
      const cols_num_left = col_index - left_col + 1;
      for (let i = 0; i < cols_num_left; i++) {
        this.col_size[modify_left_col_index] =
          (this.resize_vertical_line - cells_width) / cols_num_left;
        modify_left_col_index--;
      }
      // 右侧单元格
      const cols_num_right = right_col - col_index;
      for (let i = 0; i < cols_num_right; i++) {
        this.col_size[modify_right_col_index] =
          (prev_cells_width - this.resize_vertical_line) / cols_num_right;
        modify_right_col_index++;
      }
      this.resize_vertical_line = 0;

      let preCell: Cell | undefined
      this.children.forEach((child, index) => {
        // 因为只有线两侧的单元格会影响到 row_size 所以就只他们 typesetting 但是 row_size 改变之后 排版会错乱 所以下边更正 top 值
        if (
          child.end_col_index === col_index ||
          child.start_col_index ===
          col_index + 1 /** || index === this.children.length - 1 */
        ) {
          // // 这是原来的写法 是错误的 如果最后一行 线两侧的单元格合并了 那么就不用走 child.typesetting 了 就只走那个传参的 就不会修改 row_size 了 ↓
          // if (child.end_row_index === this.row_size.length - 1) {
          //   child.typesetting();
          // } else {
          //   child.typesetting(this.children[index + 1]);
          // }
          // // 这是原来的写法 ↑
          // 那么现在的问题就是怎么判断这个单元格是最后一个单元格 就不能传参了 我应该声明一个变量 永远更新上一个单元格
          // 让上一个单元格调用 typesetting
          if (!preCell) {
            // 如果没有就赋值
            preCell = child;
          } else {
            // 如果已经有了就先 typesetting 然后再赋值
            preCell.typesetting(this.children[index]);
            preCell = child;
          }
        }
      });
      if (preCell) {
        // 最后一个调用 typesetting 不需要传参了
        preCell.typesetting();
      }
      // 因为调用 typesetting 的时候,用的 row_size 不一定是对的,所以会导致位置错乱,这里更正一下 row 的 top 值
      this.children.forEach((cell) => {
        cell.changeRowTop();
      });
    }
  }

  // 根据行号获取每行单元格
  row_cells(rowIndex: number) {
    const cells = this.children;
    return cells.filter((cell) => cell.position[0] === rowIndex);
  }

  /**
   * 从表格所在容器中移除当前表格
   */
  remove({ current_group }: { current_group?: Group | null } = {}) {
    // 分组内删除
    // const current_group = editor?.selection.getFocusGroup(); // 获取分组的信息 要在删除之前
    if (this.group_id) {
      current_group = this.parent.getGroupById(this.group_id);
    }
    const end_para_index = current_group?.end_para_index;
    const cell = this.parent;
    cell.children.splice(this.cell_index, 1);
    cell.paragraph.splice(this.para_index, 1);
    // 只要表格在分组内末尾，就插入空行
    if (current_group && this.para_index === end_para_index) {
      const new_paragraph = new Paragraph(uuid("para"), cell, this.group_id);
      // 新new出来的段落为空的时候，需要添加一个默认的换行符
      const font = cell.editor.fontMap.add(
        cell.editor.config.default_font_style
      );
      const new_character = new Character(font, "\n");
      new_paragraph.characters.push(new_character);
      new_paragraph.createRow();
      cell.paragraph.splice(end_para_index, 0, new_paragraph);
      cell.children.splice(this.cell_index, 0, ...new_paragraph.children);
      cell.updateParaIndex();
      cell.updateRowBounding(this.cell_index); // 如果不调用的话 会导致 页面错乱 估计是 update 的时候传参在 下边了 导致使用了 modelData 中的 top
      current_group.refreshContentParaId();
      return true;
    }
    if (cell.children.length === 0) {
      const new_paragraph = new Paragraph(uuid("para"), cell, this.group_id);
      // 新new出来的段落为空的时候，需要添加一个默认的换行符
      const font = cell.editor.fontMap.add(
        cell.editor.config.default_font_style
      );
      const new_character = new Character(font, "\n");
      new_paragraph.characters.push(new_character);
      new_paragraph.createRow();
      cell.paragraph.push(new_paragraph);
      cell.children.push(...new_paragraph.children);
      cell.updateParaIndex();
      cell.updateRowBounding(this.cell_index);
      current_group && current_group.refreshContentParaId();
      return true;
    }
    cell.updateParaIndex();
    cell.updateRowBounding(this.cell_index);
    current_group && current_group.refreshContentParaId();
    const multipleSelected = this.editor.selection.multipleSelected;
    if (multipleSelected.length > 0) {
      for (let i = 0; i < multipleSelected.length; i++) {
        const { start, end } = multipleSelected[i];
        const startIndex = start[0];
        const endIndex = end[0];
        if (this.para_index >= startIndex && this.para_index <= endIndex) {
          multipleSelected.splice(i, 1);
          i--;
        }
      }
    }
    return true;
  }

  // 新表格调用 替换掉传参的那个表格
  replace(current_table: Table, editor: Editor) {
    editor.current_cell.children.splice(current_table.cell_index, 1, this);
    editor.current_cell.paragraph.splice(current_table.para_index, 1, this);
    editor.current_cell.updateRowBounding(current_table.cell_index);
    editor.current_cell.updateParaIndex();
  }

  /**
   * 把当前表格内的单元格 排序
   */
  sortingCells(type: number = 0) {
    this.children.sort((a, b) => {
      const [ax, ay] = a.position;
      const [bx, by] = b.position;
      if (type === 0) {
        if (ax !== bx) {
          return ax - bx;
        }
        return ay - by;
      } else {
        if (ay !== by) {
          return ay - by;
        }
        return ax - bx;
      }
    });
  }

  judgeLineShow(row_col: string, line_position: any) {
    if (row_col === "row") {
      for (let i = 0; i < this.notAllowDrawLine.changeOpacityRow.length; i++) {
        const element = this.notAllowDrawLine.changeOpacityRow[i];
        if (
          element[0] === line_position[0] &&
          element[1] === line_position[1]
        ) {
          return true;
        }
      }
    } else {
      for (let i = 0; i < this.notAllowDrawLine.changeOpacityCol.length; i++) {
        const element = this.notAllowDrawLine.changeOpacityCol[i];
        if (
          element[0] === line_position[0] &&
          element[1] === line_position[1]
        ) {
          return true;
        }
      }
    }
    return false;
  }

  showSomeLine(line_style: string, lines: number[][]) {
    if (line_style === "row") {
      this.notAllowDrawLine.changeOpacityRow = deleteTwoDimArrElement(
        this.notAllowDrawLine.changeOpacityRow,
        lines
      );
    } else {
      this.notAllowDrawLine.changeOpacityCol = deleteTwoDimArrElement(
        this.notAllowDrawLine.changeOpacityCol,
        lines
      );
    }
  }

  /**
   * 获取渲染的（可看到的）行列去重后数组
   * @param isRow true:行，false:列
   */
  getViewPositionArr(isRow: boolean = true): number[] {
    let num_arr: any = [];
    let pos_index: number = 0;
    if (!isRow) {
      pos_index = 1;
    }
    this.children.forEach((cell: Cell) => {
      num_arr.push(cell.position[pos_index]);
    });
    // 去重
    num_arr = Array.from(new Set(num_arr));
    num_arr.sort((a: number, b: number) => {
      return a - b;
    });
    return num_arr;
  }

  getColsByContent(
    content: string
  ): { startColIndex: number; colspan: number }[] {
    const res: { startColIndex: number; colspan: number }[] = [];
    for (const cell of this.children) {
      if (cell.position[0] === 0 && cell.getStr() === content) {
        res.push({ startColIndex: cell.position[1], colspan: cell.colspan });
      }
      if (cell.position[0] > 0) return res;
    }
    return res;
  }

  /**
   * 根据行下标获取单元格
   * @param rowIndex
   */
  getCellsByRowIndex(rowIndex: number): Cell[] {
    const rows_num_arr = this.getViewPositionArr();
    rowIndex = rows_num_arr[rowIndex];
    return this.children.filter((cell: Cell) => {
      return cell.position[0] === rowIndex;
    });
  }

  /**
   * 根据列下标获取单元格
   * @param col_index
   */
  getCellsByColIndex(col_index: number): Cell[] {
    const cols_num_arr = this.getViewPositionArr(false);
    col_index = cols_num_arr[col_index];
    return this.children.filter((cell: Cell) => {
      return cell.position[1] === col_index;
    });
  }

  /**
   * 根据行列获取单元格
   * @param row_index
   * @param col_index
   */
  getCellByPosition(row_index: number, col_index: number) {
    const rows_num_arr = this.getViewPositionArr();
    row_index = rows_num_arr[row_index];
    const cols_num_arr = this.getViewPositionArr(false);
    col_index = cols_num_arr[col_index];
    return this.children.find((cell: Cell) => {
      return cell.position[0] === row_index && cell.position[1] === col_index;
    });
  }
}

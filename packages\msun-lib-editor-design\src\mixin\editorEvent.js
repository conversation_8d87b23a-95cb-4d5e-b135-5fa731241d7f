/**
 * 编辑器触发函数
 */
const editorEventMixIn = {
  methods: {
    bindInstanceEvent(Instance) {
      const editor = Instance.editor;
      editor.event.on("pointerUp", () => {
        this.setHeaderBtnStatus();
      }); // 编辑器双击事件
      editor.event.on("handleDataSet", () => {
        return this.oriDataSet;
      });
      editor.event.on("ocr", () => {
        const fields = [];

        if (this.oriDataSet) {
          for (const key in this.oriDataSet) {
            if (key.endsWith("desc")) {
              const v = this.oriDataSet[key];
              if (v && typeof v === "object") {
                if (Array.isArray(v)) {
                  for (const item of v) {
                    console.log(item, "数据集处理 这里边是数组 item 没处理");
                  }
                } else {
                  for (const k in v) {
                    fields.push({
                      text: v[k],
                      name: k,
                      placeholder: v[k],
                    });
                  }
                }
              }
            }
          }
        }
        return {
          product: "design",
          fields,
        };
      });
      editor.event.on("contentChanged", () => {
        // 字体缩小到一页卡顿 ↓
        // const fn = () => {
        //   console.count("几遍");
        //   const MAX_LIMIT = 12;
        //   // 页面缩小到一页
        //   let pageLength = editor.pages.length;
        //   const targetFindingField = editor.getFieldsByName("finding")[0];
        //   const targetConclusionField = editor.getFieldsByName("conclusion")[0];
        //   if (!targetFindingField) return; // 不存在检查所见文本域
        //   if (!targetConclusionField) return; // 不存在检查诊断文本域

        //   let count1 = 0;
        //   while (pageLength === 1 && count1 < MAX_LIMIT) {
        //     editor.setCharacterSize(
        //       targetFindingField,
        //       "bigger",
        //       undefined,
        //       16,
        //       true
        //     );
        //     editor.setCharacterSize(
        //       targetConclusionField,
        //       "bigger",
        //       undefined,
        //       16,
        //       true
        //     );
        //     count1++;
        //     // 更新页码
        //     pageLength = editor.pages.length;
        //   }
        //   editor.refreshDocument();

        //   let count2 = 0;
        //   while (pageLength > 1 && count2 < MAX_LIMIT) {
        //     editor.setCharacterSize(
        //       targetFindingField,
        //       "smaller",
        //       undefined,
        //       undefined,
        //       true
        //     );
        //     editor.setCharacterSize(
        //       targetConclusionField,
        //       "smaller",
        //       undefined,
        //       undefined,
        //       true
        //     );
        //     count2++;
        //     // 更新页码
        //     pageLength = editor.pages.length;
        //   }
        //   editor.refreshDocument();

        // };
        // editor.makeHistoryStackAble(fn);
        // 字体缩小到一页卡顿 ↑
        this.wordStatisticsFun();
        if (this.isPrintDesign) {
          clearTimeout(this.saveLocalDataTimeout);
          this.saveLocalDataTimeout = setTimeout(() => {
            this.saveLocalData();
          }, 0.5 * 60 * 1000);
        }
      }); // 编辑器内容改变事件
      editor.event.on("exeCommand", (e) => {
        if (e.command === "caretMove") {
          if (!editor.formula_mode) {
            this.updateSideData();
            this.updateSideDataSource();
          }
        }
        this.setFontStyle();
      });
      editor.event.on("beforeDrop", () => {
        if (!this.dragInfo || !this.dragInfo.data) {
          return "origin";
        }
        const resInfo = {
          customCallBack: () => {
            this.dragDrop();
          },
        };
        return resInfo;
      });
      editor.event.on("beforePrintView", () => {
        if (this.oriDataSet) {
          const newPrintEditor = editor.copyEditor(editor.getRawData());
          newPrintEditor.event.on("message", (arg) => {
            let type, msg;
            if (!arg) return;
            if (typeof arg === "string" || typeof arg === "number") {
              msg = arg;
            } else {
              type = arg.type;
              msg = arg.msg;
            }
            if (!this.$editor[type]) {
              type = "warning";
            }
            this.$editor[type](msg);
          });
          this.fillContentByReceiveJsonData(newPrintEditor);
          return { newPrintEditor };
        }
      });
      editor.event.on("insertComSentence", (item) => {
        if (item && item.type === "field") {
          this.insertFieldByQuickInputSelect(item);
        }
      });
      editor.event.on("quickSelectInputList", (text, resolve) => {
        return this.searchFieldByKeyText(text, resolve);
      });
      //更新右侧边栏
      editor.event.on("updateSide", () => {
        if (!editor.formula_mode) {
          this.updateSideData();
          this.updateSideDataSource();
        }
      });
      editor.event.on("reInitRaw", () => {
        this.updateCustomFieldDataSet();
      });
    },
  },
};
export default editorEventMixIn;

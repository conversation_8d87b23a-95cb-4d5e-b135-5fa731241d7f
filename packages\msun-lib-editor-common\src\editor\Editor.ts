import FontMap from "./FontMap";
import Cell from "./Cell";
import Row from "./Row";
import Table from "./Table";
import Renderer from "./Renderer";
import Page from "./Page";
import Font, { FontStyle } from "./Font";
import Character from "./Character";
import XSelection from "./Selection";
import ContextState from "./ContextState";
import Caret from "./Caret";
import PathUtils, { Path } from "./Path";
import Image from "./Image";
import Print from "./Print";
import { getFocusImage, setFocusImage } from "./ImageEditing";
import { commands } from "./Command";
import History from "./History";
import Paragraph from "./Paragraph";
import AutoFill from "./AutoFill";
import {
  correctCaret,
  monitorPerformance,
  resCommand,
  resetFieldFocus,
  resetFocus,
  resetSelectionFocus,
  undoStackRecord,
} from "./Decorator";
import Widget from "./Widget";
import XField, { display_type } from "./XField";
import Search from "./Search";
import Group from "./Groups";
import EventEmit from "./EventEmit";
import { EnhancedAhoCorasick } from "./find";
import BoxField from "./BoxField";
import Line from "./line";
import Box from "./Box";
import ImageTable from "./ImageTable";
import EditorConfig from "./EditorConfig";
import Shape, { shapeType } from "./shape";
import WaterMark from "./WaterMark";
import Comment from "./Comment";
import EditorHelper from "./EditorHelper";
import InputAdapter from "./InputAdapter";
import MarkInput from "./MarkInput";
import LocalTest from "../../localtest";
import Internal from "./Internal";
import {
  alignType,
  Direction,
  DirectionType,
  HightLightParameter,
  IncreaseType,
  insertType,
  M2WParameter,
  PacsLayoutParameter,
  pageDirection,
  RowLineType,
  widgetType,
} from "./Definition";
import Button from "./Button";

import ImageMap from "./ImageMap";
import { isBoxField, isCell, isUseCurrentLogic, ReplaceContainerType } from "./Helper";
import { COMMENT_LIST_MIN_WIDTH, HUMAN_BODY_DIAGRAM_POINT_COLOR, HUMAN_BODY_DIAGRAM_POINT_RADIUS, HUMAN_BODY_DIAGRAM_POINT_RADIUS_SQUARE, ShapeMode, SkipMode, TypeInImageMeta, VerticalAlign, ViewMode } from "./Constant";
import createImagePlaceholder, { getCanvasRendering, getImage, getLCM, isCharacter, isField, isNotNullAndUndefined, isParagraph, isRow, isTable, keepDecimal, props2Obj, uuid, versionDiff, } from "./Utils";
import Monitor from "./Monitor";
import FloatModel from "./FloatModel";
import { isInMobileEdit, setMobileSelection } from "./Mobile";
import Ext from "./Ext";
import Fraction from "./Fraction";
import Echarts from "./Echarts";

const init_selection = {
  anchor: [0, 0],
  focus: [0, 0],
};
export default class Editor {

  commentTotal: number = 0;

  isOnScrollBar: boolean = false;

  activeGroupKey: string | undefined; // 记录激活的单元格的分组key

  editorId: string | null = null;

  viewScale: number = 1;

  readonly: boolean = false;

  formReadonly: boolean = false;

  is_comment_mode: boolean = false;

  isCusCommentMode: boolean = false;

  useNewVersionCommentList: boolean = true;

  newVersionOpenCommentMap: Map<string, true> = new Map(); // 新版(因为搞不好旧版也需要) 记录打开着的批注 map key 是 id

  view_mode: ViewMode = ViewMode.NORMAL; // 普通模式normal， 表单模式form

  root_cell: Cell; // 正文内容

  header_cell: Cell; // 页眉内容

  floatModels: FloatModel[] = [];

  header_diff_info: any = {}; // 不同的页眉信息

  footer_cell: Cell; // 页脚内容

  current_cell: Cell; // 当前操作的cell

  config: EditorConfig = new EditorConfig();

  fontMap: FontMap = new FontMap();

  imageMap: ImageMap = new ImageMap(this);

  contextState: ContextState = new ContextState(this); // 编辑器当前光标所在的上下文样式

  page_size = this.config.getPageSize();

  raw: any;

  pages: Page[] = [];

  scroll_top: number = 0;

  caret: Caret = new Caret();

  selection: XSelection = new XSelection(init_selection, this);

  event: EventEmit = new EventEmit(this);

  hold_mouse: boolean = false;

  init_canvas: any;

  print_continue: boolean = false;

  area_print: boolean = false; // 是否进行区域打印

  print_mode: boolean = false;

  formula_mode: boolean = false;

  group_print: boolean = false;

  format_brush: boolean = false;

  is_edit_hf_mode: boolean = false; // 是否编辑页眉页脚模式

  editFloatModelMode: boolean = false; // 编辑浮动模型模式

  currentFloatModel: undefined | FloatModel; // 记录当前正在操作的浮动模型,没有的话就是 undefined

  show_header_footer: boolean = true; // 是否打印页眉页脚

  is_forbid_edit_hf: boolean = false; // 是否禁止双击编辑页眉页脚

  is_shape_mode: boolean = false; // 是否图形模式

  shapes: any = []; // 存储绘制的浮动图形

  commentBox: any = [];

  waterMarks: any = []; // 存储水印元素

  markInputStyle: any = null; // markInput框的样式

  waterMark: boolean = false;

  is_modified: boolean = false; // 当前编辑器内容是否被编辑过

  document_meta: any = {}; // 文档的扩展信息属性

  custom_meta: any = {}; // 自定义扩展信息属性

  is_focus: boolean = false;

  show_tips: boolean = true; // 是否可以显示提示信息

  showWartFontEditor: boolean = false; // 是否展示水印字体编辑框

  field_valid: boolean = false; // 文本域校验配置总开关，因大多无需校验功能所以默认关闭

  history: History = new History();

  scrollX: number = 0; // 出现滚动条后的横向滚动距离

  focusElement: {
    field?: XField;
    row?: Row;
    paragraph?: Paragraph;
    cell?: Cell;
    table?: Table;
    group?: Group;
    page?: Page;
  } = {}; // 当前获得焦点的元素（row、paragraph、cell、field、table）


  // TODO xzq
  isPastedFromKeyboard: boolean = false; // 判断粘贴是否是由于键盘事件(ctrl + v)引起，因为手写输入 也是从剪切板拿数据

  internal: Internal = new Internal(this); // 所有内部使用的变量或函数

  ext: any = new Ext(this);

  monitor: Monitor = new Monitor(); // 编辑器性能监视

  adminMode: boolean = false; // 管理员模式

  user: any = null; // 当前登录的用户
  // TODO xzq
  inputDOM: HTMLElement | undefined;
  // TODO xzq
  offsetX: number = 0; // 缩放用的
  offsetY: number = 0; // 缩放用的

  holdRotateIcon: boolean = false;

  get page_left() {
    let commentWidth = 0;
    if (this.is_comment_mode) {
      if (this.useNewVersionCommentList) {
        commentWidth = Math.max(this.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
      } else {
        commentWidth = 300;
      }
    }

    const page_left =
      (parseInt(this.init_canvas.style.width) -
        this.page_size.width -
        commentWidth) /
      2;
    return page_left; // 画板的宽度 减去配置的页面的宽度 再除以2 就是该页面距离canvas最左侧边界的距离
  }

  constructor(config: any = {}) {
    const { root_cell, header_cell, footer_cell } =
      EditorHelper.editorConstructor(this, config);
    // 此时还没有 render 所以在这里调用 appendEmptyPara 不对
    this.root_cell = this.current_cell = root_cell;
    this.header_cell = header_cell;
    this.footer_cell = footer_cell;
  }
  /**
   * 手麻用的数据转换
   * @param parameter
   * @returns
   */
  m2w(parameter: M2WParameter = { editor: this }){
    if (parameter) {
      parameter.editor = this;
    }
    return EditorHelper.m2w(parameter);
  }

  /**
   * 通过原始数据获取waterMarks
   * @param waterMarks
   */
  getWaterMarkModelData(waterMarks: any[] = this.raw.waterMarks) {
    if (!waterMarks) return [];
    return WaterMark.getDataByRawData(this, waterMarks);
  }

  // TODO xzq
  getInputDOM() {
    return document.getElementsByName("editor_input_name")[0];
  }
  // TODO xzq
  // 挂在 input 的 DOM 元素 可以进行编辑 实际上是 textarea
  mountInputDOM() {
    return EditorHelper.mountInputDOM({ editor: this });
  }
  // TODO xzq
  // 移出 input 的 DOM 元素 就不能进行编辑了 实际上是 textarea
  removeInputDOM() {
    return EditorHelper.removeInputDOM({ editor: this });
  }

  /**
   * 替换掉了原来的 rawData2ModelData
   * @param nodeList
   * @param cell
   * @returns
   */
  getCellByRawNodeList(
    nodeList: any,
    cell: Cell = new Cell(this, [0, 0], 1, 1, null, "trans")
  ): Cell | undefined {
    return EditorHelper.getCellByRawNodeList({ editor: this, nodeList, cell });
  }

  setCellsGroupKey(cells: Cell[][], groupKey: string) {
    return EditorHelper.setCellsGroupKey({ cells, groupKey });
  }

  // 根据选区设置单元格分组
  setCellsGroupBySelection(groupKey: string = uuid("groupKey")) {
    return EditorHelper.setCellsGroupBySelection({ editor: this, groupKey });
  }

  // 根据选区 取消单元格成组
  cancelCellsGroupBySelection() {
    return EditorHelper.cancelCellsGroupBySelection({ editor: this });
  }

  /**
   *
   * @returns 是否使用当前逻辑，里边用了本地的 transuse 和 useBeforeVersion 配置
   */
  isUseCurrentLogic() {
    return isUseCurrentLogic(this);
  }

  /**
   * 重新初始化原始数据
   * @param configItems 控制是否使用 rawData 中存储的配置项 ["all"]:全部使用 rawData 中的配置 否则就传要用使用的 rawData 中的配置
   * @isClearHistory 是否清空堆栈 默认不传 为true  即清空
   */
  @monitorPerformance()
  reInitRaw(rawData: any, isClearHistory = true) {
    if (this.isMobileTerminal()) {
      this.blur();
    }
    return this.event.emit("transReInitRaw", { rawData, isClearHistory });
    // EditorHelper.reInitRaw(this, rawData, isClearHistory);
  }

  // 通过配置 决定是否处理压缩数据
  reInitRawByConfig(rawData: any, configItems: string[] = ["all"]) {
    EditorHelper.reInitRawByConfig(this, rawData, true, configItems);
  }

  /**
   * 在 beforePaste 监听事件中使用，在隐藏编辑器中加载数据的时候使用这个，携带字体的 reInitRaw
   * @param rawDataContent rawData 的 content 内容 paragraph 和 table rawData 的集合
   * @param fontStr fontMap 的字符串形式
   */
  reInitRawCarryFont(rawDataContent: any, fontStr: string) {
    return EditorHelper.reInitRawCarryFont(this, rawDataContent, fontStr);
  }

  // 设置是否启用数字排版 已弃用（云病历中还有调用待提需求后作废）
  setIsEnableNumTypesetting() { }

  // 设置是否启用数字排版 已弃用（云病历中还有调用待提需求后作废）
  setIsCommaTypesetting() { }

  /**
   * 获取光标位置处的字符,永远返回两个，哪个没有哪个就是 undefined [character | undefined, character | undefined]
   */
  getCharactersAtCaret(): (Character | Image | Widget | Line | Box | Button)[] {
    return Caret.getCharacters(this);
  }

  getFieldsByPlaceholder(placeholders: string[]): Map<string, XField[]> {
    return EditorHelper.getFieldsByPlaceholder(this, placeholders);
  }



  getFieldsBySelection(): (XField| BoxField)[] {
    return EditorHelper.getFieldsBySelection({ editor: this });
  }

  /** ********* 鼠标操作 ↑ ************** */

  /**
   * 编辑器获得焦点，可主动触发。
   */
  focus() {
    return EditorHelper.focus(this);
  }

  /**
   * 编辑器失去焦点
   */
  blur() {
    return EditorHelper.blur(this);
  }

  /**
   * 鼠标按下事件
   * @param x 点击的横向坐标位置
   * @param y 点击的纵向坐标位置
   * @param hold_shift 是否按住了shift键
   */
  @resCommand()
  pointer_down(x: number, y: number, hold_shift: Boolean = false) {
    return EditorHelper.pointerDown(this, x, y, hold_shift);
  }

  /**
   * 鼠标抬起事件
   * @returns boolean 竖线拖动修改成功返回true 否则返回false
   */
  pointer_up(x: number, y: number, event: PointerEvent) {
    return EditorHelper.pointerUp(this, x, y, event);
  }

  /**
   * 鼠标移动
   * @param x
   * @param y
   */
  pointer_move(x: number, y: number) {
    if (this.editFloatModelMode) {
      x -= this.currentFloatModel?.originPosition[0] || 0;
      y -= this.currentFloatModel?.originPosition[1] || 0;
    }
    return EditorHelper.pointerMove(this, x, y);
  }

  /**
   * 双击页眉页脚时记录堆栈
   * @param x
   * @param y
   * @param resInfo
   */
  // TODO xzq
  @undoStackRecord(commands.uncertainty)
  dblClickEditHeaderFooter(x: number, y: number, resInfo: any) {
    return EditorHelper.dblclickHeaderOrFooter(this, x, y, resInfo);
  }

  /** ********* 鼠标操作 ↑ ************** */

  /**
   * 关闭shapes的编辑状态
   */
  closeShapesEditor() {
    this.shapes.forEach((shape: Shape) => {
      shape.is_editor = false;
    });
  }
  // TODO xzq
  @undoStackRecord(commands.handle_shapes)
  handleShapes() {
    return Shape.handleShapes(this);
  }

  /** ********* shape 相关 ↑ ************** */

  /**
   * 退出编辑页眉页脚模式
   * @param isUpdate 是否需要更新光标，默认更新
   */
  exitEditHfMode(isUpdate: boolean = true) {
    return EditorHelper.exitEditHfMode(this, isUpdate);
  }

  /**
   * 重设canvas宽高
   */
  updateCanvasSize() {
    return EditorHelper.updateCanvasSize(this);
  }

  /**
   *
   * @returns 编辑器自适应大小
   */
  adaptSize() {
    return EditorHelper.adaptSize(this);
  }

  changeCustomPageSize(
    width: number,
    height: number,
    direction: pageDirection = "vertical"
  ) {
    return EditorHelper.changeCustomPageSize(width, height, direction, this);
  }

  // 按照纸张类型改变page的宽高
  changePageSize(type: any, direction: pageDirection) {
    return EditorHelper.changePageSize(this, type, direction);
  }

  // TODO xzq 位置
  setCharacterSize(
    element: XField,
    type: "smaller" | "bigger" | "base",
    baseHeight?: number,
    maxHeight?: number,
    isIncludesBorder?: boolean
  ) {
    return EditorHelper.setCharacterSize({ editor: this, element, type, baseHeight, maxHeight, isIncludesBorder });
  }

  /**
   *  改变页面方向
   * @param direction 页面方向 横向还是纵向
   */
  changePageDirection(direction: pageDirection) {
    return EditorHelper.changePageDirection(this, direction);
  }

  /**
   * 根据当前焦点重置滚动高度，使焦点内容始终在可视范围内
   */
  // @resetSelectionFocus()
  scroll_by_focus(): void {
    // 移动端的时候是不需要修改 scroll_top 的，因为移动端修改的一直都是 translate 的变量，包括滚动翻页， scroll_top 一直都是 0
    // 如果走这个方法之后 scroll_top 不为 0 了,点击位置就不对了
    if (!this.isMobileTerminal()) {
      return EditorHelper.scrollByFocus(this);
    } else {
      this.caretIsInViewport();
    }
  }

  toDocTopResult() {
    EditorHelper.scrollTo(this, "top");
    this.render();
  }

  toDocBottomResult() {
    EditorHelper.scrollTo(this, "bottom");
    this.render();
  }

  // 剪切
  @undoStackRecord(commands.uncertainty)
  cut(is_drag: boolean = false) {
    return EditorHelper.cut(this, is_drag);
  }

  /**
   * 粘贴
   * @param isDrag 是否是拖动行为
   */
  @undoStackRecord(commands.paste)
  paste(
    isDrag: boolean,
    isOnlyText: boolean = false,
    keepOrigin: boolean = false
  ) {
    return EditorHelper.paste(this, isDrag, isOnlyText, keepOrigin);
  }

  /**
   * 设置是否为只读模式
   * @param flag 是否为只读模式
   */
  setReadonly(flag: boolean) {
    this.readonly = flag;
    this.caret.show = !flag;
  }

  /**
   * 设置分组表单模式
   * @param isForm //是否表单模式
   * @param group  //分组对象
   */
  setGroupFormMode(isForm: boolean = true, group?: Group) {
    Group.setFormMode(this, isForm, group);
  }

  /**
   * 设置表单模式只读
   * @param flag
   */
  setFormReadonly(flag: boolean) {
    this.formReadonly = flag;
    this.caret.show = !flag;
  }

  /**
   * 切换行线的配置
   * @param type RowLineType
   */
  toggleRowLineType(type: RowLineType) {
    this.config.rowLineType = type;
    this.refreshDocument();
  }

  // 切换浮动模型 模式
  toggleFloatModelMode(open: boolean = true) {
    return EditorHelper.toggleFloatModelMode({ editor: this, open });
  }

  /**
   * 添加批注
   * 应该将所有的character字符存起来，即便有数据改变也没有关系，因为我们不会增加被批注的字符了，只会减少，所以改动样式什么的，就只改动存的这些字符的样式就可以了
   * 问题：如果保存到了 rawData
   * @param parameters 添加的批注内容
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  addComment(parameters: any): string | undefined {
    return Comment.addComment(this, parameters);
  }

  addCommentByField(parameters: any, field: XField) {
    return Comment.addCommentByField(this, parameters, field)
  }

  getCommentInfo(){
    return Comment.getCommentInfo(this);
  }

  @undoStackRecord(commands.uncertainty)
  replaceComment(commentId: string, value: string, paraId: string) {
    Comment.abolishComment(this, commentId, "replace", { paraId });
    return Comment.replaceContent(this, commentId, value);
  }

  @undoStackRecord(commands.uncertainty)
  deleteComment(commentId: string) {
    Comment.abolishComment(this, commentId, "delete");
    const groupId = commentId.split("$$")[1] + "";
    Comment.deleteComment(this, commentId, groupId);
    return true;
  }


  clearAllComment() {
    return EditorHelper.clearAllComment(this)
  }

  /**
   * 取消批注
   * @param commentID 批注 id
   * @param groupID 分组 id
   * @returns 是否成功
   */
  @undoStackRecord(commands.uncertainty)
  uncomments(commentID: string | undefined, groupID: string | undefined) {
    return Comment.unComments(this, commentID, groupID);
  }

  // 通过ID获得对应的自定义批注
  getCusCommentByCommentID(cusCommentId: string) {
    return Comment.getCommentByID(this, cusCommentId, true);
  }

  // 切换自定义批注模式
  toggleCusCommentMode(isCusCommentMode: boolean) {
    return Comment.toggleMode(this, isCusCommentMode, true);
  }

  // 增加自定义批注
  @undoStackRecord(commands.uncertainty)
  addCusComment(commentContent: any, cusId?: string): string | undefined {
    return Comment.addComment(this, commentContent, true, cusId);
  }

  // 移除自定义批注
  @undoStackRecord(commands.uncertainty)
  deleteCusComment(
    cusCommentId: string | undefined,
    groupID: string | undefined
  ) {
    return Comment.unComments(this, cusCommentId, groupID, true);
  }


  /**
   * 更新自定义批注
   * @param id 批注 id
   * @param updateValue 批注内容
   * @returns 是否成功
   */
  updateCusComments(id: string, updateValue: string) {
    return Comment.updateComments(this, id, updateValue, true);
  }

  /**
   * 判断页面方向改变后宽度是否超出canvas宽度，如果超出，对页面进行缩放
   */
  autoChangeViewScale() {
    return EditorHelper.changePageZoom(this);
  }

  /**
   * 判断页面方向改变后宽度是否超出canvas宽度，如果超出，对页面进行缩放
   */
  reInitConfig(custom_config: any) {
    return EditorHelper.reInitConfig(this, custom_config);
  }

  /** -------↓ 表格相关 ↓------ */
  /**
   * 根据name值获取所有表格
   * @param name 表格的name值
   * @returns 表格数组
   */
  getTablesByName(name: string): Table[] {
    return Table.getTablesByName(name, this);
  }

  getAllTables() {
    return this.root_cell.paragraph.filter((item) => {
      return isTable(item);
    });
  }

  @undoStackRecord(commands.uncertainty)
  appendDataToTable({
    id,
    name,
    tables,
    data,
  }: {
    id?: string;
    name?: string;
    tables?: Table[];
    data: any[];
  }) {
    return EditorHelper.appendDataToTable({ editor: this, id, name, tables, data });
  }

  // 根据 id 获取表格 包含页眉页脚 只找一个
  getTableById(id: string) {
    return Table.getTableById(this, id);
  }

  judgeIsCanDeleteRowOrCol() {
    return Table.judgeIsCanDeleteRowOrCol(this);
  }

  /**
   * 表格中删除整行
   * @return boolean true 删除成功 false删除失败
   */
  @undoStackRecord(commands.handle_table)
  deleteRowFromTbl(): boolean {
    return Table.deleteRow(this);
  }

  /**
   * 表格中删除整列,
   * 不管colspan了，不修改这个属性值了，直接修改col_size的大小，和position的位置
   * @returns boolean true:删除成功 false: 删除失败
   */
  @undoStackRecord(commands.handle_table)
  deleteColFromTbl(): boolean {
    return Table.deleteCol(this);
  }

  /**
   * 根据光标位置 删除表格
   * @return boolean 删除成功返回true 删除失败返回false
   */
  @undoStackRecord(commands.handle_table)
  deleteTbl(table?: Table): boolean {
    if (table) {
      // 如果传入table实例则将光标直接置于table中
      this.selection.setCursorPosition(PathUtils.getStartPathByTable(table));
    }
    return Table.deleteTableWithCaret(this);
  }

  /**
   * 在表格中插入行和列, 开始光标不在表格内不能插入，选区不是纯表格不能插入，如果能插入就以第一个单元格为基准进行插入
   * ①：分页的split_parts应该也是要更新的，里边的各种属性，比如row_size,等等 如果有分页的情况
   * @param direction 插入的方向 上下左右 四个方向
   * @return boolean 插入成功为true 否则为false
   */
  @undoStackRecord(commands.handle_table)
  addRowOrColInTbl(direction: Direction): boolean {
    return Table.insertRowOrCol(this, direction);
  }

  // 设置单元格是否锁定(就是修改 cell 的 lock 字段) TODO 暂时留着防止报错
  setCellsLock() {
    console.warn("当前接口已作废，请尽快更改为updateCellsAttr({cells,lock:true})")
  }

  @undoStackRecord(commands.handle_cell)
  @resetFocus()
  updateCellsAttr({ cells, attr }: { cells?: Cell[]; attr: any }) {
    return Cell.updateCellsAttr({ cells, attr, editor: this });
  }
  // TODO xzq 考虑还是表格有单独的锁定比较合适
  // 设置表格是否锁定
  setTablesLock({
    tables,
    isLock = true,
  }: { tables?: Table[]; isLock?: boolean } = {}) {
    return Table.setTablesLock({ tables, isLock, editor: this });
  }

  @resetFocus()
  fixedCellHeight(type: "scroll" | "normal", height?: number) {
    Cell.fixedHeight(this, type, height);
  }

  /**
   * 设置在表单模式下可编辑
   */
  setTablesEditableInFormMode(editable: boolean, tableName: string) {
    return Table.setEditableInFormMode(editable, tableName, this);
  }

  // 创建表格排版
  createTableLayout(numberOfCellsPerRow: number[]): Table | undefined {
    return Table.createTableLayout({ editor: this, numberOfCellsPerRow });
  }
  // TODO xzq ？？
  formDataIntoTable() { }

  /**
   * 移动表格线
   * @param x 光标的横向坐标
   * @param y 光标的纵向坐标
   */
  // TODO xzq 位置
  move_cell_bounding_line(x: number, y: number) {
    return Table.dragLine(this, x, y);
  }

  /**
   * 鼠标抬起时 调整表格线
   * @returns boolean 修改成功返回true
   */
  // TODO xzq
  @undoStackRecord(commands.uncertainty)
  change_table_line() {
    return Table.changeTableLines(this);
  }

  /**
   * 根据行和列 生成原始表格 主要修改了col_size, row_size和新单元格的位置
   * @param row_num 要生成表格的行数
   * @param col_num 要生成表格的列数
   * @param height 生成单元格内行的高度
   * @returns 生成的原生表格
   */
  // TODO xzq 提供给产品使用了吗？
  generateRawTable(row_num: number, col_num: number, height: number): any {
    return Table.generateRawTable(row_num, col_num, height, this);
  }

  // TODO xzq 同上？ 能够合并至createElement
  // 根据行和列创建一个表格
  createTable(
    row_num: number,
    col_num: number,
    params?: {
      isImageTable?: boolean;
      newPage?: boolean;
      name?: string;
      skipMode?: SkipMode;
    }
  ): Table | undefined {
    let isImageTable = false;
    let newPage = false;
    let skipMode = SkipMode.ROW;
    if (typeof params === "object") {
      isImageTable = params.isImageTable || false;
      newPage = params.newPage || false;
    }
    const table = ImageTable.createTableOrImageTable(
      row_num,
      col_num,
      isImageTable,
      this,
      newPage
    );
    if (table && typeof params === "object" && params.name) {
      table.name = params.name;
    }
    if (table && typeof params === "object" && params.skipMode) {
      table.skipMode = params.skipMode;
    }
    return table;

    // return EditorHelper.createTable({ editor: this, row_num, col_num, params });
  }

  /**
   * 根据表格的name值插入行和列
   * @param name 表格的name值
   * @param row_num 行数
   * @param col_num 列数
   */
  insertRowAndColByName(name: string, row_num: number, col_num: number) {
    return Table.appendRowAndColByName(name, row_num, col_num, this);
  }

  /**
   * 插入表格: 在光标所在位置处，插入表格，根据行和列创建好表格，然后修改数据结构，插入表格
   * @param row_num 要插入表格的行数
   * @param col_num 要插入表格的列数
   * @param isImageTable 是否要创建图片排版的表格
   * @return boolean 成功为true 否则为false
   */
  @undoStackRecord(commands.handle_table)
  insertTable(
    row_num: number,
    col_num: number,
    params?: {
      isImageTable?: boolean;
      newPage?: boolean;
      name?: string;
      skipMode?: SkipMode;
      opacityOfLines?: number // 0 是完全透明 1 是不透明
    }
  ): Table | boolean {
    return Table.insertTable(row_num, col_num, this, params);
  }

  // TODO xzq 表格属性中没有该设置
  // 设置表格是否可以删除 传true为不能删除 传false就是可以删除
  setTblIsCanDelete(notCan: boolean) {
    return Table.setIsCanBeDeleted(notCan, this);
  }

  /**
   * 设置固定表头
   * fixed_table_header_num 固定几行做表头 默认是1
   * 如果什么都不传，就固定光标位置处的表格
   * 到底固定多少行就按照选区来：选区选中的最后一行，如果可以就固定，否则就不能固定，如果没有选区就看表头开始第一行能否固定否则就按照能固定的最小行数来
   * 目前先只考虑一个表格的情况
   */
  setFixedTableHeader({
    name,
    fixed_table_header_num,
  }: { name?: string; fixed_table_header_num?: number } = {}) {
    return Table.fixedHeader({
      name,
      fixedHeaderNum: fixed_table_header_num,
      editor: this,
    });
  }

  /**
   * 在单元格里边绘制斜线
   */
  @undoStackRecord(commands.uncertainty)
  drawSlashOnCell(direction: Direction, count: number) {
    return Table.drawSlashOnFocusCell(direction, this, count);
  }

  /**
   * 控制表格线的显示隐藏
   * @param line_show_type 表格线展示类型
   * @returns 操作成功与否
   */
  // TODO xzq 不是很明了 count的含义
  @undoStackRecord(commands.uncertainty)
  cotrolTableLine(line_show_type: string, count: number = 1) {
    return Table.toggleLines(line_show_type, this, count);
  }

  /**
   * 切换单元格的某条线 是否显示
   * @param direction 控制显隐的那条线 上下左右四个方向
   * @param is_show 是否显示 true是要显示 false 为不显示
   * @returns boolean 成功为true 否则为false
   */
  @undoStackRecord(commands.uncertainty)
  toggleCellSingleLineShowHide(
    direction: Direction | null,
    is_show: boolean,
    cells: Cell[]
  ): boolean {
    return Table.toggleCellsLine(direction, is_show, cells, this);
  }

  /**
   * 调整单元格尺寸,此处为解决同一个方法无法使用多个装饰器修饰问题，resetSelectionFocus修饰的方法体内不能存在update方法
   * @param cell viewData 中的 光标所在的 cell
   * @param cell_position
   * @param left_col
   * @param right_col
   */
  @resetSelectionFocus()
  resetCellSize(
    cell: Cell,
    cell_position: Direction,
    left_col: number,
    right_col: number
  ) {
    return cell.resize(cell_position, left_col, right_col);
  }

  /**
   * 判断能否插入列
   */
  // TODO xzq 是否跟上面的重复了
  judgeIsCanInsertCol(): boolean {
    return Table.judgeIsCanInsertCol(this);
  }

  // TODO xzq 使用场景太单一
  // 获取表格内所有的字符串
  getTblStr(): string {
    return Table.getStrWithFocusTable(this);
  }

  // TODO xzq 挑合适的时机移入ext
  // PACS用 根据ID删除图片
  @undoStackRecord(commands.uncertainty)
  deleteInImageTable(id: string) {
    return ImageTable.deletePictureByID(id, this);
  }

  /**
   * 交换两个单元格 c1 的内容换成 c2, c2 的内容换成 c1
   * @param c1 需要交换内容的 单元格1
   * @param c2 需要交换内容的 单元格2
   */
  // TODO xzq 不调用update会不会有问题？
  @undoStackRecord(commands.uncertainty)
  swapCells(c1: Cell, c2: Cell) {
    return EditorHelper.swapCells(c1, c2);
  }

  /**
   * 重新给单元格内容排序 该方法内没有 update render 所以调用完 需要调 update render
   * @param moveCell 开始准备移动位置的单元格
   * @param targetCell 移动到的目标单元格
   * @param relativePosition 要放到 dropCell 的前边还是后边还是原位置
   */
  // TODO xzq 使用场景是只有表格图片排版？
  @undoStackRecord(commands.uncertainty)
  reorderCells(
    moveCell: Cell,
    targetCell: Cell,
    relativePosition: "front" | "behind" | "situ" | "swap"
  ) {
    const table1 = moveCell.parent?.getOrigin();
    const table2 = targetCell.parent?.getOrigin();
    if (isTable(table1) && table1 === table2) {
      table1.reorderCells(moveCell, targetCell, relativePosition);
    }
    if (table1 instanceof ImageTable && table1 === table2) {
      table1.reorderImageList(moveCell, targetCell, relativePosition);
    }

  }

  /**
   * 绘制临时边框
   * @param target 目标
   * @param direction 绘制边框位置 上下左右
   */
  // TODO xzq ？？
  drawTempBorder(
    target: Cell,
    direction: "left" | "right" | "top" | "bottom" | "border"
  ) {
    target.drawTempBorder(direction);
  }

  /**
   * 插入图片排版
   * @param src 可以是图片的网址 也可以是图片的base64
   * @param serialNum 图片编号
   * @param field 第二个 文本域
   * @param index 删除单元格的下标
   */
  // TODO xzq 挑合适时机移入ext, 该给pacs提需求就提
  @undoStackRecord(commands.uncertainty)
  insertImageTable({
    src,
    field,
    index,
    width,
    height,
    maxTableHeight,
    id,
    needSerialNum,
  }: {
    src?: string;
    field?: XField;
    index?: number;
    width?: number;
    height?: number;
    maxTableHeight?: number;
    id?: string;
    needSerialNum?: boolean;
  } = {}) {
    return ImageTable.insertImageTable({
      src,
      field,
      index,
      width,
      height,
      maxTableHeight,
      id,
      editor: this,
      needSerialNum,
    });
  }
  // TODO xzq 是否内部用
  // 判断能否删除表格上方的空段
  judgeCanDelEmptyParaOnTblSide(direction: Direction): boolean {
    return Table.judgeIsCanDeleteBlankRow(direction, this);
  }

  // 删除表格旁边的空段
  @undoStackRecord(commands.uncertainty)
  deleteEmtptyParagraphOnTblSide(direction: Direction) {
    return Table.deleteBlankRow(direction, this);
  }

  // 插入空行
  // TODO xzq 是否内部用
  @undoStackRecord(commands.uncertainty)
  insertEmptyParagraphOnTblSide(direction: Direction) {
    return Table.insertBlankRow(direction, this);
  }


  // 光标是否在视口内
  caretIsInViewport() {
    return EditorHelper.caretIsInViewport({ editor: this });
  }
  // TODO xzq 是否内部用
  // 点击表格下方空白处获取光标位置
  getCaretPathInTableEmptyArea(
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row)[] = this.pages,
    path: Path = []
  ):
    | {
      row: Row;
      view_path: Path;
      x: number;
      y: number;
    }
    | undefined {
    if (this.editFloatModelMode) {
      containers = [this.current_cell];
    }
    return Table.getCaretPathInEmptyArea(this, x, y, containers, path);
  }

  /**
   * 判断分割后的单元格是否符合要求，如果有一个符合则返回true
   */
  // TODO xzq 是否内部用
  splitCellJudge(
    cell_: Cell,
    start_row_index: number,
    end_row_index: number,
    start_col_index: number,
    end_col_index: number
  ): boolean {
    return Table.splitCellJudge(
      cell_,
      start_row_index,
      end_row_index,
      start_col_index,
      end_col_index
    );
  }

  /**
   * 获取分割后的表格，此处因为多处使用，所以将共有逻辑提取，防止修改时漏改
   * @param origin_tbl  模型数据中的表格
   * @param cur_row  模型数据中的行
   */
  getSplitTable(origin_tbl: Table, cur_row: Row) {
    return Table.getSplitTable(origin_tbl, cur_row);
  }

  // 将表格按照完整的行进行拆分成单个表格然后再插入锚点 看似仍然是个完整的表格
  @undoStackRecord(commands.uncertainty)
  splitTableByFullRowsWithAnchors() {
    return Table.splitTableByFullRowsWithAnchors({ editor: this });
  }

  // 判断单元格能否拆分 返回true能拆分 否则不能拆分
  judgeIsCanSplitCell(): boolean {
    return Table.judgeFocusCellIsCanSplit(this);
  }

  // 拆分单元格 能拆分 就返回true  不能拆分 返回false
  // TODO 方法是里根据 rowspan colspan 完全拆分 有部分数据 拆分效果不对
  @undoStackRecord(commands.handle_cell)
  splitCell(): boolean {
    return Table.splitCellWithCaret(this);
  }

  // 判断能否合并单元格
  judgeIsCanMergeCell(): boolean {
    return Table.canMergeCells(this);
  }

  /**
   * 合并单元格 暂时只考虑规则的合并，那么每一行的单元格数量必须是一样的 每一列的单元格数量也必须是一样的
   * 逻辑：将选中的每个单元格都拆分一遍，然后数每一行的单元格，如果都一样则可以合并，否则不能合并
   * @returns boolean 不能合并 返回false 合并成功 返回true
   */
  @undoStackRecord(commands.handle_cell)
  mergeCell(pointCells?: Cell[]): boolean {
    return Table.mergeCells(this, pointCells);
  }

  /**
   * 调整单元格边距
   * @param setType 0 直接设置传入值， 1- 在原基础增加 2-在原基础上减少
   * @param cell[]
   * @param paddingVal
   */
  @undoStackRecord(commands.handle_cell)
  setCellPadding(
    setType: IncreaseType = IncreaseType.fixed,
    paddingVal: {
      left?: number;
      right?: number;
      top?: number;
      bottom?: number;
    },
    cells: Cell[] = []
  ) {
    return Cell.setCellPadding(this, setType, paddingVal, cells);
  }

  /**
   * 根据坐标位置获取到当前表格
   * @param x 光标距离canvas左上角的横坐标位置
   * @param y 光标距离canvas左上角的纵坐标位置
   */
  getTblByPointIsOnHorizontalLine(
    x: number,
    y: number
  ): { tbl: Table | undefined; line: number | undefined } {
    return EditorHelper.getTblByPointIsOnHorizontalLine({ editor: this, x, y });
  }
  /**
 * 锚点展示或隐藏文本域
 * @param fields
 * @param display
 */
  fieldsShowOrHide(fields: XField[], display: Boolean = true) {
    return EditorHelper.fieldsShowOrHide({ editor: this, fields, display });
  }
  /**
   * 在指定文本域后面插入表格
   * @param field 指定文本域
   */
  insertTableAfterField(field?: XField | null) {
    return EditorHelper.insertTableAfterField({ editor: this, field });
  }

  /**
   * 在文本域后后边插入文本域
   * @param field 在该文本域后边插入文本域
   */
  insertFieldAfterField(parameter: {positionField?: XField | null, insertField?: any}) {
    return EditorHelper.insertFieldAfterField({ editor: this, ...parameter });
  }
  /** -------↑ 表格相关 ↑------ */

  // 点击表格或编辑器外部获取光标位置
  getPointerOutSideCaretPath(
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row)[] = this.editFloatModelMode
      ? this.currentFloatModel?.children || this.pages
      : this.pages,
    path: Path = []
  ):
    | {
      row: Row;
      view_path: Path;
      x: number;
      y: number;
    }
    | undefined {
    return EditorHelper.getPointerOutSideCaretPath(
      this,
      x,
      y,
      containers,
      path
    );
  }

  /**
   * 根据光标位置 x,y 返回所在的元素 是否在page上 在哪个段落上 在哪个表格上等
   * @param x
   * @param y
   */
  getElementByPoint(
    x: number,
    y: number
  ): {
    isEditHF: boolean; // 判断是否在页眉上
    page?: Page; // 是否在白色区域内
    row?: Row; // 当前行
    table?: Table | null; // 当前表格
    cell?: Cell; // 当前单元格
    image?: Image | null; // 当前图片
    element?: Character | Image | Widget | Line | Box | Button | null; // 当前元素
    paragraph?: Paragraph; // 当前段落
    group?: Group | null; // 当前分组
    field?: XField | BoxField | null; // 当前文本域
    button?: Button;
    viewPath?: Path;
    fractionObj?: any
  } {
    return EditorHelper.getElementByPointer(this, x, y);
  }

  /**
   * 拖拽选区至选区外部粘贴的方法
   * @returns
   */
  dragSelectedArea(x: number, event?: DragEvent | PointerEvent) {
    return EditorHelper.dragPaste(this, x, event);
  }

  /**
   * 绘制虚拟光标
   * @param x
   * @param y
   */
  drawShadowCaret(x: number, y: number) {
    return Caret.drawVirtual(this, x, y);
  }
  drawFieldWidthShadowCaret(x: number, y: number) {
    return Caret.drawFieldShadow(this, x, y);
  }
  /**
   * 判断鼠标是否在选区内
   * @param x
   * @param y
   * @returns
   */
  isInSelectedArea(x: number, y: number) {
    return EditorHelper.isInSelectedArea({ editor: this, x, y });
  }

  /**
   * 全选内容
   * @returns
   */
  selectAll() {
    return EditorHelper.selectAll(this);
  }

  /**
   * 外部接口 修改文档 文档对齐
   * @param start_path 开始坐标
   * @param end_path 结束坐标
   * @returns 修改成功
   */
  setParaToDocuAlignInterface(start_path?: number[], end_path?: number[]) {
    EditorHelper.setDocumentAlign(this, start_path, end_path);
    this.update();
    this.render();
    return true;
  }

  /**
   *通过view_path获取元素绝对路径
   * @param view_path
   * @param containers
   * @param x
   * @param y
   * @returns 应该是获取到相对于画布原点的 x y 值
   */
  getElementAbsolutePositionByViewPath(
    view_path: number[],
    containers: (
      | Page
      | Cell
      | Table
      | Row
      | Character
      | Image
      | Widget
      | Line
      | Box
    )[] = this.pages,
    x: number = 0,
    y: number = 0
  ): { x: number; y: number } {
    return EditorHelper.getElementAbsolutePositionByViewPath(
      this,
      view_path,
      containers,
      x,
      y
    );
  }

  /**
   * 查找全部
   * @param text 查询内容
   * @returns
   */
  searchAll(
    text: string | string[],
    isHighlight?: boolean,
    group?: Group,
    color?: string,
    para?: Paragraph | Table,
    index?: number
  ) {
    return Search.searchAll(this, text, isHighlight, group, color, para, index);
  }

  /**
   * 取消查找
   */
  cancelSearch() {
    return Search.cancelFind(this);
  }

  /**
   * 取消高亮
   */
  cancelHighLight() {
    return Search.cancelFind(this);
  }

  /**
   *  查找下一个
   * @param text 查询内容
   * @returns
   */
  searchNext(text: string, group?: Group) {
    return Search.findNext(this, text, group);
  }

  /**
   * 替换单个
   * @param select_text 查询内容
   * @param replace_text 替换内容
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  replaceFont(select_text: string, replace_text: string, group?: Group) {
    return Search.replaceOne(this, select_text, replace_text, group);
  }

  /**
   * 全部替换
   * @param select_text 查询内容
   * @param replace_text 替换内容
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  replaceAllFont(
    select_text: string,
    replace_text: string,
    group?: Group,

  ) {
    return Search.replaceAll(
      this,
      select_text,
      replace_text,
      group,

    );
  }

  // TODO 假设不存在 upset: 10, offset: 16 又接着又有一个 upset: 13 offset: 20 这种情况 目前这种实现不了,可以改进
  /**
   * 根据字符串下标高亮
   * @param param0
   * @returns
   */
  highlightByKeywordSubscripts({
    keywords,
    groupId,
    highLightColor = "orange",
  }: HightLightParameter) {
    return EditorHelper.highlightByKeywordSubscripts({
      editor: this,
      keywords,
      groupId,
      highLightColor,
    });
  }

  highLightByFields(parameter: {isHighLight: boolean, id?: string, name?: string, color?: string, fields?: XField[], }) {
    if (!parameter) return;
    return EditorHelper.highLightByFields(this, parameter);
  }
  
  /**
   * 高亮显示
   */
  highLightMark(text: string, color?: string, data?: Group | XField[]) {
    Search.highLightMark(this, text, color, data);
  }

  /**
   * 区分大小写
   */
  caseSensitive() {
    return Search.caseSensitive();
  }

  /* -- 查找替换相关 end -- */
  /* -- 字符段落格式 start -- */

  /**
   * 修改文字样式
   * @param style 文字样式
   * @returns
   */
  @undoStackRecord(commands.style_change)
  change_font_style(style: Partial<FontStyle>) {
    return Font.changeStyle(this, style);
  }

  /**
   * 修改段落样式
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  change_paragraph_style() {
    return Paragraph.changeStyle(this);
  }

  // 给中草药 四个一行的功能使用 默认传入的 paragraphs 都是需要排版的
  findContinuousParagraphs(paragraphs: Paragraph[]): Paragraph[] {
    return EditorHelper.findContinuousParagraphs({ paragraphs });
  }

  /**
   * 云病历 四个一行的需求
   * 需要云病历调用方法之前先设置好字符对齐
   */
  formatParagraph(paragraphs: Paragraph[], docuAlignPosition: number = 0, left: number = 0, update: boolean = true) {
    return EditorHelper.formatParagraph({ editor: this, paragraphs, docuAlignPosition, left, update });
  }

  /**
   * 格式刷功能
   */
  formatBrush() {
    return EditorHelper.formatBrush(this);
  }

  setParagraphState() {
    return Paragraph.setState(this);
  }

  /**
   * 改变选区文字样式
   * @param style
   * @returns
   */
  @resetSelectionFocus()
  changeFontStyleBySelection(style: Partial<FontStyle> | "bigger" | "smaller") {
    return XSelection.changeFontStyle(this, style);
  }

  /**
   *
   * @param color 必须传入#七位颜色，不能传rgb
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  changeTableBgStyle(color: string) {
    return XSelection.changeTableBgStyle(color, this.selection, this);
  }

  changeCellStyle(parameter: {cells?: Cell[], style: any}) {
    return EditorHelper.changeCellStyle({ ...parameter, editor: this });
  }
  
  /**
   *
   * @param color 必须传入#七位颜色，不能传rgb
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  averageRowHeight() {
    return EditorHelper.averageRowHeight({ editor: this });
  }

  /**
   *
   * @param color 必须传入#七位颜色，不能传rgb
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  averageColWidth() {
    return EditorHelper.averageColWidth({ editor: this });
  }



  /**
   * 设置选区内字符的尺寸 更大或者更小
   * @param type 更大或者更小
   */
  @undoStackRecord(commands.uncertainty)
  @resetSelectionFocus()
  setSelectionCharacterSize(type: "bigger" | "smaller") {
    return XSelection.setCharactersZoom(this, type);
  }

  /**
   * 清除样式
   */
  @undoStackRecord(commands.uncertainty)
  deleteStyle() {
    return EditorHelper.clearStyle(this);
  }

  /**
   * 格式刷设置段落格式
   */
  @undoStackRecord(commands.uncertainty)
  formatBrushBySelection() {
    return XSelection.setParagraphStyleByFormatBrush(this);
  }

  /**
   * 获取段落的字符串
   */
  getParaStr(): string {
    return Paragraph.getStrWithCaret(this);
  }

  /**
   * 返回focus处图片
   * @returns 返回focus处图片，吐过没有返回null
   */
  getFocusImage() {
    return getFocusImage();
  }

  /**
   * 添加单选复选框
   * @param type 框类型
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  insertSimpleWidget(type: widgetType, border: string = "solid") {
    return Widget.insertSingle(this, type, border);
  }

  /**
   * 添加单选复选框
   * @param params
   * {
   *  isGroup, 是否成组
   *  groupName, 分组名称
   *  hideBorder, 是否隐藏边框
   *  isMulti, 是否多选
   *  widgetType, 边框类型
   *  deletable, 是否可删除
   *  items 项目
   *  border 边线
   * }
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  insertWidget(params: any) {
    return Widget.insert(this, params);
  }

  /**
   * 添加单选复选框
   * @param params
   * {
   *  num, 卡尺的最大刻标
   *  spacing, 卡尺的刻标间距
   *  selectNum, 卡尺的选择数字
   * }
   * @returns
   */
  @undoStackRecord(commands.uncertainty)
  insertCaliper(height: number, params: any) {
    return Widget.insertSingle(this, "caliper", "solid", height, params);
  }

  // 更新caliper
  updateCaliper(caliper: Widget, num: number, height: number, spacing: number) {
    return Widget.updateCaliper(caliper, num, height, spacing, this);
  }

  /**
   * 插入按钮
   */
  @undoStackRecord(commands.uncertainty)
  insertButton(text: string, width?: number, height?: number, color?: string) {
    return Button.insertButton(this, text, color, width, height);
  }

  // 插入分数
  insertFraction(parameter: {int: string, numerator: string, denominator: string}) {
    Fraction.insert({ editor: this, ...parameter });
  }

  /**
   * 修改复选框单选框信息
   * @param box_field 对应的外层box类型文本域
   * @param params 重新插入所需的参数信息
   */
  @undoStackRecord(commands.uncertainty)
  updateWidget(box_field: BoxField, params: any) {
    return Widget.update(this, box_field, params);
  }

  /**
   * 设置焦点元素，主要是设置图片与复选框与单选框
   * @param container_info
   */
  @undoStackRecord(commands.uncertainty)
  setSelectImageOrWidget(
    container_info: any,
    focus_field: XField | BoxField | null
  ) {
    return EditorHelper.setSelectImageOrWidget(
      this,
      container_info,
      focus_field
    );
  }

  /**
   * 设置图片的大小
   * @param width 图片宽
   * @param height 图片高
   * @returns
   */
  @undoStackRecord(commands.insert_image)
  changeImageSize(width: number, height: number) {
    return Image.changeSize(this, width, height);
  }

  /**
   * 在光标处插入图片
   * @param src
   * @param other_param
   */
  @undoStackRecord(commands.insert_image)
  insertImage(src: string, other_param: any = {}) {
    return Image.insert(this, src, other_param);
  }

  // 压缩图片
  compressImage(base64: string, maxWidth = Infinity, maxHeight = Infinity, quality = 1) {
    return new Promise((resolve, reject) => {
      // 创建一个 Image 对象
      const img = getImage();
      img.src = base64;

      img.onload = () => {
        let width = img.width;
        let height = img.height;

        // 计算需要压缩的比例
        if (width > maxWidth) {
          height = height * (maxWidth / width);
          width = maxWidth;
        }
        if (height > maxHeight) {
          width = width * (maxHeight / height);
          height = maxHeight;
        }

        // 创建一个 Canvas 元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return

        // 设置 Canvas 的宽度和高度
        canvas.width = width;
        canvas.height = height;

        // 在 Canvas 上绘制图片
        ctx.drawImage(img, 0, 0, width, height);

        // 将 Canvas 内容转换为 Base64 编码的图片
        const compressedBase64 = canvas.toDataURL('image/jpeg', quality);

        resolve(compressedBase64);
      };

      img.onerror = () => {
        reject(new Error('压缩失败了'));
      };
    });
  }

  

  @undoStackRecord(commands.insert_image)
  handleClickHumanBodyDiagram(parameter: {clickInfo: any}) {
    const { element, row, x, y } = parameter.clickInfo;
    const relativeX = x - element.left;
    const relativeY = y - row.top;
    if (!element.meta) {
      element.meta = {}
    }
    if (!element.meta.originBase64) {
      element.meta.originBase64 = element.src; // element.src 是最原始的 base64 如果是网址的话 那么该 element.src 就是网址
    }
    if (!element.meta.points || !Array.isArray(element.meta.points)) {
      element.meta.points = []
    }
    const obj = this.imageMap.get().get(element.src);
    let isDelete = false;
    const points = element.meta.points;
    if (points && points.length) {
      for (let i = 0; i < points.length; i++) {
        const p = points[i];
        if ((relativeX - p[0]) ** 2 + (relativeY - p[1]) ** 2 < HUMAN_BODY_DIAGRAM_POINT_RADIUS_SQUARE) {
          points.splice(i, 1);
          isDelete = true;
          break;
        }
      }
    }

    !isDelete && element.meta.points.push([relativeX, relativeY]);
    const { canvas, ctx } = getCanvasRendering({ width: Number(obj.data.width), height: Number(obj.data.height) });
    const newImage = getImage();
    newImage.src = element.meta.originBase64;
    const ratio = obj.data.width / element.width;
    newImage.onload = () => {
      ctx.drawImage(newImage, 0, 0);
      for (const key of element.meta.points) {
        ctx.beginPath();
        ctx.arc(key[0] * ratio, key[1] * ratio, HUMAN_BODY_DIAGRAM_POINT_RADIUS * ratio, 0, Math.PI * 2);
        ctx.fillStyle = HUMAN_BODY_DIAGRAM_POINT_COLOR;
        ctx.fill();
        ctx.closePath();
      }
      const newSrc = canvas.toDataURL();
      element.src = newSrc;
      this.imageMap.addOnload(element);
      this.render();
    };
    return true; // 有一点点的延迟 如在 onload 里边加 setTimeout 加个 5 秒的延迟 快速点几下 会5秒后一次出来 撤销的时候 就一次都撤销了
  }


  /**
   * 目前就pacs在用，插入排版 创建出来的表格 不是 Table 类型而是 ImageTable 类型
   * @param ratio 每行显示几个单元格的数组
   * @param data 往单元格内放入的数据
   * @param width 单元格内图片内容宽度
   * @param height 单元格内图片内容高度
   * @param spacingWidth 图片与图片之间的间距
   * @returns
   */
  @undoStackRecord(commands.handle_table)
  insertLayout(
    ratio: number[],
    data: PacsLayoutParameter[],
    width: number,
    height: number,
    spacingWidth: number,
    spacingHeight: number,
  ): Table | void {
    const version = this.document_meta.versionList?.[0].version;
    if (!version || versionDiff(version, "10.0.0") < 0) {
      spacingHeight = 0;
    }
    if (
      PathUtils.isTablePath(this.selection.focus) ||
      this.selection.getFocusField()
    ) {
      return;
    }
    const table = this.createTableLayout(ratio);
    if (!table) return;
    table.formData(data, ratio, [width, height], spacingWidth, spacingHeight);
    table.insert();
    return table;
  }

  /**
   * 插入图片到指定位置
   * @param para_path//插入图片的位置
   * @param src //插入图片的src
   * @param other_params meta //图片所携带的参数 devicePixelRatio 图片缩放的倍数
   */
  insertImageByParaPath(
    para_path: any[],
    src: string,
    other_params: {
      meta?: any;
      width?: number;
      height?: number;
      devicePixelRatio?: number;
      loadCompleted?: (image: Image, focus_row: Row) => any;
    } = {}
  ) {
    return Image.insertByParaPath(this, para_path, src, other_params);
  }

  imageIsOverSize(width: number, height: number, para_row: Row) {
    return Image.isOverSize(this, width, height, para_row);
  }

  /* -- 打印PDF start -- */

  /**
   * 为PDF提供base64
   * @returns
   */
  providePDFParams() {
    // TODO  没什么卵用 base vue Demo上都没有用到该方法
    return Print.getBase64List(this);
  }

  /**
   * 执行打印的方法
   * @param afterPrint 打印后的回调函数
   * @returns 打印的base64或者image集合
   */
  print(printParameter?: any, afterPrint?: (e?: any) => {}) {
    return Print.print(this, printParameter, afterPrint);
  }

  /**
   *批量打印返回base64的方法
   * @param rawDataList rawData的集合
   */
  rawData2Base64(rawDataList: any[]) {
    return Print.rawDataa2Base64(this, rawDataList);
  }

  /**
   * 返回一个与当前编辑器相同配置的editor
   * @param rawData 不传则使用与原编辑器相同数据
   */
  copyEditor(rawData?: any): Editor {
    return EditorHelper.getCopiedEditor(this, rawData);
  }

  getPrintEditor(rawData?: any) {
    return EditorHelper.getPrintEditor(this, rawData);
  }

  // 获取blob的Promise函数
  getSingleBlobPromise(canvas: HTMLCanvasElement) {
    return new Promise((resolve) => {
      canvas.toBlob((blob) => resolve(blob));
    });
  }

  // 获取blob的Promise数组
  getAllBlobPromise() {
    return EditorHelper.getAllBlobPromise(this);
  }

  // 获取blob数组
  getAllBlobs() {
    const blobPromiseArr = this.getAllBlobPromise();
    return Promise.all(blobPromiseArr);
  }

  /**
   * 系统打印打印奇数页和偶数页的方法
   * @param mode 奇数页传入“odd”偶数页传入“even”
   * @returns 打印的base64或者image集合
   */
  printOddOrEven(mode: string) {
    return Print.printOddOrEvent(this, mode);
  }

  /**
   * 绘制区域打印的遮罩
   * @param x
   * @param y
   */
  drawAreaPrintShadow(x: number, y: number) {
    return Print.drawMaskOfAreaPrint(this, x, y);
  }

  /**
   *  通过鼠标位置获取鼠标所在元素左上角的坐标，如果没有则返回鼠标所在处x，y
   * @param x
   * @param y
   * @returns 鼠标所在处元素左上角的绝对坐标，如果没有则返回鼠标所在处x，y
   */
  getAbsoluteXYByPointer(x: number, y: number) {
    return EditorHelper.getAbsoluteXYByPointer(this, x, y);
  }

  /**
   * 区域打印
   */
  areaPrint(flag: boolean) {
    return Print.areaPrint(this, flag);
  }

  /**
   *设置续打遮罩高度
   * @param x
   * @param y
   */
  setShadowAreas(x: number, y: number) {
    return Print.setMaskHeightOfContinuePrint(this, x, y);
  }

  /**
   * 续打
   * @param flag 是否续打
   */
  printContinue(flag: boolean) {
    return Print.continueToPrint(this, flag);
  }

  /**
   * 立即续打
   * @param type 打印类型
   * @param afterPrint 打印后后回调
   */
  printContinueImmediate() {
    return Print.continueToPrintImmediately(this);
  }
  /* -- 打印PDF end -- */

  /**
   * 获取距离当前光标点击位置最近的文本域id
   * @param container_info
   */
  getNearestFieldByContainer(container_info: any): any {
    return Print.getFieldIDClosestToCaret(this, container_info);
  }

  /**
   * 插入分页
   */
  @undoStackRecord(commands.uncertainty)
  insertPageBreak() {
    return EditorHelper.insertPaginationSymbol(this);
  }

  /**
   * 是否展示页眉页脚
   */
  isShowFooterOrHeader() {
    return EditorHelper.isShowFooterOrHeader(this);
  }

  /**
   * 退出编辑页眉页脚的状态
   */
  @undoStackRecord(commands.uncertainty)
  quitEditHeaderAndFooterMode() {
    return EditorHelper.quitEditHeaderAndFooterMode(this);
  }

  /**
   * 进入编辑页眉页脚模式
   */
  @undoStackRecord(commands.uncertainty)
  enterEditHeaderAndFooterMode(
    type: "header" | "footer" = "header",
    page?: Page
  ) {
    return EditorHelper.enterEditHeaderAndFooterMode(this, type, page);
  }

  /**
   *获取当前点击容器信息辅助函数，此函数可返回点击空白区域时自动设置的模型坐标
   * @param x
   * @param y
   */
  getContainerInfoByPointHelper(x: number, y: number) {
    return EditorHelper.getContainerInfoByPointHelper(this, x, y);
  }

  /**
 *  是否在移动端选区小水滴内
 * @param x
 * @param y
 * @returns
 */
  isInMobileEdit(x: number, y: number) {
    return isInMobileEdit(x, y, this)
  }
  /**
 * 移动端选区
 * @param char
 * @returns
 */
  setMobileSelection(info: any) {
    return setMobileSelection(info, this)
  }
  /**
   * 鼠标按下并移动后，根据坐标设置focus
   * @param x event.offsetX
   * @param y event.offsetY
   */
  focus_by(x: number, y: number): void {
    return EditorHelper.focusBy(this, x, y);
  }

  /**
   *  传入x,y设置选区的方法
   * @param x
   * @param y
   * @returns
   */
  setSelectionByXY(x: number, y: number) {
    return XSelection.setSelectionArea(this, x, y);
  }

  /**
   * viewPath转换为modelPath
   * @param view_path viewPath
   * @returns
   */
  // @catchException()
  viewPath2ModelPath(view_path: Path): Path {
    return EditorHelper.viewPath2ModelPath(this, view_path);
  }

  /**
   * 获取文档所有字符(除了页眉页脚以外的)
   * @returns
   */
  getBodyText() {
    return EditorHelper.getBodyStr(this);
  }

  /**
   * modelPath转换为viewPath
   * @param path modelPath
   * @returns
   */
  // @catchException()
  modelPath2viewPath(path: Path): Path {
    return EditorHelper.modelPath2ViewPath(this, path);
  }

  /**
   * 将modelPath转换为paraPath
   * @param model_path
   * @return 段落下标以及字符相对于段落下标组成的数组
   */
  // @catchException()
  modelPath2ParaPath(model_path: Path): Path {
    return EditorHelper.modelPath2ParaPath(this, model_path);
  }

  /**
   * 将paraPath转换为modelPath
   * @param para_path 段落下标以及字符相对于段落下标组成的数组
   * @return model_path 行下标即字符相对于所在行的下标组成的数组
   * */
  // @catchException()
  paraPath2ModelPath(para_path: Path, current?: string): Path {
    return EditorHelper.paraPath2ModelPath(this, para_path, current);
  }

  /**
   * 将一个指向cell的 modelPath 转换成 viewPath
   * @param model_path
   */
  // @catchException()
  modelCellPath2viewCellPath(model_path: Path): Path[] | undefined {
    return EditorHelper.modelCellPath2viewCellPath(this, model_path);
  }

  /**
   * 方向键按下时光标移动
   * @param direction
   */
  @resCommand()
  caret_move(direction: DirectionType): any {
    return Caret.move(this, direction);
  }

  /**
   * 设置单元格内的所有内容上下对齐方式 (居中对齐 上对齐 下对齐)
   * @param alignDirection
   */
  setVerticalAlign(alignDirection: VerticalAlign) {
    return EditorHelper.setVerticalAlign(this, alignDirection);
  }

  /**
   * 插入字符
   * @param text
   * @param type 必要参数，不可删除(撤销功能使用) insert 默认值，每次调用接口都会记录到撤销堆栈中，传入init时不进行记录
   */
  @undoStackRecord(commands.uncertainty)
  insertText(text: string, type: insertType = "init"): boolean {
    if (this.config.useLetterPlaceholder) {
      if (this.hold_mouse) {
        return false;
      }
    }
    return EditorHelper.insertText(this, text, type);
  }

  // 处理聚合模式
  handleAggregationMode(cell: Cell) {
    return EditorHelper.handleAggregationMode({ editor: this, cell: cell });
  }

  /**
   * 插入盒子
   * @param content 盒子储存内容
   * @param focus_para 所在段落
   */
  @undoStackRecord(commands.uncertainty)
  insertBox(content: any, name: string = "", focus_para?: Paragraph) {
    return EditorHelper.insertBox(this, content, name, focus_para);
  }

  /**
   * 插入水平线
   * @param line_hight 水平线线高
   * @param color 水平线颜色
   */
  @undoStackRecord(commands.uncertainty)
  insertLine(
    line_hight: number,
    color: string,
    focus_para?: Paragraph,
    form?: string
  ) {
    return Line.insert(this, line_hight, color, focus_para, form);
  }

  /**
   * 获取文档内的第一个 character
   * @returns Character 或者 undefined
   */
  getFirstCharacter(): Character | undefined {
    return EditorHelper.getFirstCharacter({ editor: this });
  }

  /**
   * 获取所有的 cell  包括 header_cell root_cell footer_cell 还有 所有表格里边的所有 cell
   * @param assign_cell 获取指定cell及其子cell
   * @return cell数组
   */
  getAllCells(assign_cell?: Cell): Cell[] {
    return EditorHelper.getAllCell(this, assign_cell);
  }

  /**
   * 获取文档内所有段落
   * @param purePara 是否只获取段落
   * @returns 所有段落
   */
  getAllParagraph(purePara: boolean = false) {
    if (purePara) {
      return this.root_cell.paragraph.filter((p) => isParagraph(p));
    } else {
      return this.root_cell.paragraph;
    }
  }

  /**
   * 根据名称获取box
   * @param name
   * @param assign_cell
   * @return xField[] 文本域对象数组
   */
  getBoxesByName(name: string) {
    const get_boxes = this.root_cell.getBoxesByName(name);
    return get_boxes;
  }

  /**
   * 创建元素对象
   * @param type
   * @param props
   */
  createElement(
    type:
      | "widget"
      | "image"
      | "char"
      | "font"
      | "field"
      | "imageTable"
      | "table"
      | "paragraph"
      | "cell"
      | "shape"
      | "wartermark"
      | "group",
    props: any = {}
  ) {
    // 新增
    if (type === "imageTable") {
      // 写到 EditorHelper 里边会有 ts 引用问题
      const {
        id,
        groupId,
        colSize,
        rowSize,
        minRowSize,
        parentCell,
        left,
        right,
        top,
        newPage,
        skipMode,
      } = props;
      return new ImageTable(
        this,
        id ?? uuid("table"),
        groupId,
        colSize,
        rowSize,
        minRowSize,
        parentCell,
        left,
        right,
        top,
        newPage || false,
        skipMode || SkipMode.ROW
      );
    }
    return EditorHelper.createElement(this, type, props);
  }

  /**
   * 初始化一个浮动模型,在 reInitRaw 里边调用
   * @param editor
   * @param width
   * @param height
   * @param originPosition
   * @returns
   */
  initFloatModel(
    editor: Editor,
    width: number,
    height: number,
    originPosition: [number, number]
  ) {
    return new FloatModel(editor, width, height, originPosition);
  }

  // 创建浮动模型
  createFloatModel(
    width: number,
    height: number,
    originPosition: [number, number]
  ) {
    this.editFloatModelMode = true;
    // 应该改掉 this.current_cell 然后还有光标位置 应该仿照 header footer 怎么实现的 来实现这个浮动模型
    const floatModelCell = new FloatModel(this, width, height, originPosition);

    floatModelCell.insertEmptyParagraph();
    this.current_cell = floatModelCell;
    this.selection.setCursorPosition([0, 0]);
    this.currentFloatModel = floatModelCell;
    this.floatModels.push(floatModelCell);
    this.updateCaret();
    this.render();
    return floatModelCell;
  }

  insertChartPlaceholder(text = "图表位") {
    const field = XField.insert(this, { meta: { name: "chart" } });
    if (field) {
      XField.navigateTo(this, field, "start");
      const src = createImagePlaceholder(text);
      Image.insert(this, src, { meta: { name: "chart" } });
    }
  }
  
  /** -------↓ 文本域相关 ↓------ */

  /**
   * 插入文本域
   * @param props
   */
  @undoStackRecord(commands.uncertainty)
  insertField(props?: any): XField | boolean {
    return XField.insert(this, props);
  }

  /**
   * 删除指定的文本域
   * @param fields 文本域对象数组
   */
  removeFields(fields: (XField | BoxField)[]) {
    return XField.remove(this, fields);
  }

  adminRemoveFieldsKeepCursor(fields: (XField | BoxField)[]) {
    return EditorHelper.adminRemoveFieldsKeepCursor({ editor: this, fields });
  }

  /**
   * 修改文本域属性
   * @param parameter 文本域的各个属性
   * @returns 修改成功与否
   */
  @undoStackRecord(commands.uncertainty)
  reviseFieldAttr(parameter: any): boolean {
    return XField.reviseAttr(this, parameter);
  }

  /**
   * 修改文本域属性
   * @param parameter 文本域的各个属性
   * @returns 修改成功与否
   */
  @undoStackRecord(commands.uncertainty)
  changeFieldDisplay(type: display_type, fields?: (XField | BoxField)[]) {
    return XField.changeFieldDisplay(this, type, fields);
  }

  /**
   *  获取所有正在展示的文本域的 name 值
   */
  // TODO xzq 用处在哪？
  getAllShowFieldsName(cascade_list: any) {
    return XField.getNamesOfShowFields(cascade_list);
  }

  /**
   * 显示或隐藏文本域
   */
  // TODO xzq 容易迷惑并且是否只内部使用
  @resetFieldFocus()
  showOrHideField(field: XField | BoxField, txt?: Array<string>) {
    return XField.toggle(this, field, txt);
  }

  /**
   * 判断当前文档中是否存在该文本域id
   * @param id
   */
  judgeFieldIdExist(id: string): boolean {
    return XField.hasID(this, id);
  }

  /**
   * 根据id
   * @param id
   * @param assign_cell
   */
  getFieldById(id: string, assign_cell?: Cell): XField | BoxField | undefined {
    return XField.getFieldById(this, id, assign_cell);
  }

  /**
   * 根据名称获取文本域数组
   * @param name
   * @param assign_cell
   * @return xField[] 文本域对象数组
   */
  getFieldsByName(name: string | string[], assign_cell?: Cell): (XField | BoxField)[] {
    return XField.getFieldsByName(this, name, assign_cell);
  }

  /**
   * 根据类型获取文本域数组
   * @param type 文本域类型
   * @param assign_cell
   * @return xField[] 文本域对象数组
   */
  getFieldsByType(type: string, assign_cell?: Cell): (XField | BoxField)[] {
    return XField.getFieldsByType(this, type, assign_cell);
  }

  handleFieldNewTextByReplaceRule(field: XField, replaceRule: any) {
    return EditorHelper.handleFieldNewTextByReplaceRule({ field, replaceRule });
  }

  /**
   * 获取所有的或者指定cell中所有的文本域
   * @param assign_cell
   */
  getAllFields(assign_cell?: Cell): (XField | BoxField)[] {
    return XField.getAllFields(this, assign_cell);
  }

  /**
   *将原始json内容替换到文本域中
   * @param field 文本域实例
   * @param rawData 原始json数据
   * @param params { //其他参数
   *     append,  //true 追加内容，默认为false
   *     linebreak // true 保留末尾的换行符， 默认false，不保留换行符
   * }
   */
  @undoStackRecord(commands.uncertainty)
  updateFieldRaw(field: XField, rawData: any, params?: any) {
    return XField.updateRaw(this, field, rawData, params);
  }

  /**
   * 文本域内容的替换或者拼接方法入口
   * @param afferent_parameter { fields? 指定文本域数组, name?指定name值 ,id?指定id ,value需要替换的值,append?是否是在文本域后追加内容} isHF 老代码里用到了 现在不用传了
   * @param isDocumentAlign 是否需要字符对齐
   */
  @undoStackRecord(commands.uncertainty)
  updateFieldText(afferent_parameter: any, isDocumentAlign?: boolean): boolean {
    return XField.updateText(this, afferent_parameter, isDocumentAlign);
  }

  updateFieldsTextByDefaultValue(param: {
    fields: XField[]
  }) {
    return XField.updateFieldsTextByDefaultValue({ editor: this, fields: param.fields });
  }

  /**
   * 清空文本域内容
   * @param fields 文本域对象数组
   */
  @undoStackRecord(commands.uncertainty)
  clearFields(fields: XField[]): boolean {
    if (!Array.isArray(fields)) {
      throw new TypeError("调用editor.clearFields方法,必须传入文本域数组");
    }
    return XField.clear(this, fields);
  }

  /**
   * 只清空文本域 不牵扯光标定位
   * @param fields 要清空的文本域
   * @returns 最后清空的文本域
   */
  // TODO xzq 是否只内部用？
  clearFieldsOnly(fields: XField[]) {
    if (!Array.isArray(fields)) {
      throw new TypeError("调用editor.clearFieldsOnly方法,必须传入文本域数组");
    }
    return XField.clearOnly(fields);
  }

  /**
   * 将坐标定位到文本域内
   * @param field 文本域对象
   * @param position start:定位到开头，end：定位到末尾
   */
  locatePathInField(field: XField, position: "start" | "end" = "end") {
    return XField.navigateTo(this, field, position);
  }

  locatePathOutField(field: XField, position: "start" | "end" = "end") {
    return XField.navigateTo(this, field, position === "start" ? "preStart" : "afterEnd");
  }

  /**
   * 获取字符串相对于页面左下角的位置
   * @param text 要获取位置的字符串
   */
  // TODO xzq 位置
  getPositionRelativeToPageLeftBottom(text: string) {
    return EditorHelper.getPositionRelativeToPageLeftBottom(this, text);
  }

  /**
   * 定位到指定文本域开头
   * @param id 文本域id
   * @param name 文本域名称
   * @param field 文本域
   * @returns 文本域或者提示
   */
  // TODO xzq 重复
  fixPositionToTargetField(id?: string, name?: string, field?: XField) {
    return XField.navigateByIdentifier(this, id, name, field);
  }

  /**
   * 替换图片到文本域
   * @param fields 传入的需要替换图片的文本域
   * @param image_info 图片信息 包含base64码、宽、高信息及扩展信息
   */
  replaceFieldsImage(
    fields: XField[],
    image_info: {
      src: string;
      url?: string;
      width?: number | string;
      height?: number | string;
      meta?: any;
    }
  ) {
    return XField.replaceImage(this, fields, image_info);
  }

  /**
   * 替换元素对象到文本域
   * @param fields
   * @param elements
   */
  // TODO xzq 是否跟下面updateFieldElements重复
  replaceFieldsElements(
    fields: XField[],
    elements: (Character | Image | Widget | Box | Button)[]
  ) {
    return XField.replaceElements(this, fields, elements);
  }

  /**
   * 文本域元素内容替换
   * @param field
   * @param elements
   * @param params
   */
  updateFieldElements(field: XField, elements: any[], params?: any) {
    return XField.updateElements(this, field, elements, params);
  }
  /**
   *
   * @param field 需要更新的文本域
   * @param focusField 光标所在文本域
   * @returns
   */
  // TODO xzq 内部使用
  updateFieldAutomation(field: any, focusField: any, options?:{clear:boolean}) {
    return XField.updateFieldAutomation(field, focusField, this, options);
  }

  // TODO xzq 内部使用
  @resetFieldFocus()
  // @undoStackRecord(commands.uncertainty) // 该接口不应该再触发堆栈保存，这样撤销后才能保持结果一致
  updateFieldAdvancedFunctions(field: XField | BoxField, options?:{clear:boolean}) {
    return XField.updateFieldAdvancedFunctions(field, this , options);
  }

  /**
   * 更新文本域公式值的方法
   */
  // TODO xzq 内部使用
  updateFieldsFormulaList(field: XField) {
    return XField.updateFieldsFormulaList(this, field);
  }

  /**
   * 开启关闭文本域表单模式
   */
  // TODO xzq 注释？？？
  formulaMode(is_mode: boolean, name: any = null) {
    return EditorHelper.formulaMode({ editor: this, is_mode, name });
  }

  /**
   * 显示隐藏文本域边框与背景文本
   * @param isShow 是否显示
   */
  // TODO xzq 内部使用？？？
  showFieldSymbol(isShow?: boolean) {
    return XField.toggleSymbol(this, isShow);
  }

  /**
   * 设置文本域只读
   * @param isReadonly 是否只读
   * @param cascade 是否级联
   */
  @undoStackRecord(commands.uncertainty)
  setFieldReadonly(
    isReadonly: boolean,
    cascade: boolean = false,
    field?: XField | BoxField
  ) {
    return EditorHelper.setFieldReadonly({ editor: this, isReadonly, cascade, field });
  }

  /**
   * 校验多个或者所有文本域需要校验的规则
   * @param fields 指定文本域
   */
  validFields(fields?: XField[]) {
    return XField.valid(this, fields);
  }

  /** -------↑ 文本域相关 ↑------ */

  /**
   * 插入分组
   * 页面上没有分组
   *  cell上的分组从null变成一个数组
   * 如果有分组 并且在分组内
   *  区分在分组之上或者下面插入分组
   * 分组外的需要判断位置在哪，位于分组的index
   * @param direction 方向
   * @param id 定制化id
   * @param date 设置的时间
   * @param referenceGroup 可选参数 在当前分组下方插入
   */
  // 插入分组后不能再进行撤销操作
  @resCommand(true)
  insertGroup(
    direction: Direction = Direction.down,
    date?: string,
    id?: string,
    referenceGroup?: Group,
    is_form?: boolean
  ) {
    return Group.insert(this, direction, is_form, date, id, referenceGroup);
  }

  /**
   * 在插入分组的时候的重复代码可以复用
   * @param focus_group
   * @param new_group
   * @param direction
   */
  // TODO xzq 内部使用
  insertGroupHelper(
    focus_group: Group | null,
    new_group: Group,
    direction: Direction = Direction.down
  ) {
    return Group.insertHelper(this, focus_group, new_group, direction);
  }

  /**
   * 业务中插入分组调用排序，默认在当前分组的下方插入
   * @param date 时间
   * @param id 分组id
   */
  insertGroupWithDateSort(date: string, id?: string) {
    return Group.insertWithDateSort(this, date, id);
  }

  /**
   * 删除分组
   * @returns 删除成功与否
   */
  @resCommand(true)
  deleteGroup(target_group?: Group) {
    return EditorHelper.deleteGroup({ editor: this, target_group });
  }

  // TODO xzq 应该有重复接口
  locationCaret(para_tabl: Paragraph | Table) {
    return EditorHelper.locationCaret(para_tabl);
  }

  /**
   * 分组分页
   */
  groupToNextPage(target_group?: Group) {
    return Group.setPaging(this, target_group);
  }

  /**
   * 设置分组的锁定与否
   * @param locked 是否锁定
   */
  setGroupIslock(locked?: boolean) {
    return Group.lock(this, locked);
  }

  /**
   * 分组排序
   * @param sortParameter 排序参数
   * @returns 分组排序成功
   */
  // 分组排序后也不能进行撤销操作
  @resCommand(true)
  sortGroup(sortParameter: string = "date") {
    return Group.sort(this, sortParameter);
  }

  /**
   * 清空分组
   * @param groups 分组对象数组
   */
  @undoStackRecord(commands.uncertainty)
  clearGroups(groups: Group[]) {
    return Group.clear(this, groups);
  }

  /**
   * 获取所有分组
   * @returns 所有分组
   */
  getAllGroup() {
    const groups = this.root_cell.groups;
    return groups;
  }

  /**
   * 根据id定位分组
   * @param id 分组id
   * @returns true
   */
  locationToGroupById(id: string) {
    return Group.navigateToGroupByID(this, id);
  }

  /**
   * 修改分组时间
   * @param date 分组日期
   */
  modifyGroupTime(date: string) {
    return Group.modifyDate(this, date);
  }

  // 对比分组里边的页眉信息
  // TODO xzq 确定云病历是否在用
  isGroupHeaderInfoDiff(obj: any) {
    return EditorHelper.isGroupHeaderInfoDiff({ editor: this, obj });
  }

  /**
   * 修改分组对应的页眉信息
   * @param info 页眉信息
   * @returns 修改成功与否
   */
  modifyGroupHeadInfo(obj: any) {
    return Group.modifyHeaderInfo(this, obj);
  }

  /**
   * 先计算出选区的开始和结束位置 然后根据位置 调用deleteContentByPath(根据选区的开始和结束位置删除)
   * 如果选中多个单元格 单元格内 即便是有禁止删除的文本域 也是会被删除的
   * @returns
   */
  @undoStackRecord(commands.backspace_delete)
  delete_backward(): boolean {
    return EditorHelper.deleteBackward(this);
  }

  drawShape(type: shapeType) {
    return this.internal.drawShape({ editor: this, type });
  }

  // TODO xzq 内部使用
  delete_shape(): boolean {
    return Shape.deleteShape(this);
  }
  // TODO xzq 内部使用
  // @undoStackRecord(commands.watermark)
  delete_watermark(): boolean {
    return WaterMark.deleteWaterMark(this);
  }
  // TODO xzq 注释, 个性化接口移至ext?
  getParaIndexWithKeyword(keywords: string[], paras: (Paragraph | Table)[]) {
    return EditorHelper.getParaIndexWithKeyword(keywords, paras);
  }

  /**
   * delete键删除
   */
  @undoStackRecord(commands.backspace_delete)
  delete_forward(): boolean {
    return EditorHelper.deleteForward(this);
  }

  /**
   * 根据para_path删除内容
   * @param para_start_path
   * @param para_end_path
   */
  // TODO xzq 内部使用, 理论上所有以path参数的接口都是内部用接口
  deleteContentByPath(
    para_start_path: Path,
    para_end_path: Path
  ): Path | undefined | boolean {
    return EditorHelper.deleteContentByPath(
      this,
      para_start_path,
      para_end_path
    );
  }

  /**
   * 根据传入的container获取update方法所需的参数
   * @param container
   */
  // TODO xzq 内部使用
  getUpdateParamsByContainer(
    container: Table | Cell | Row | undefined
  ): number[] {
    return EditorHelper.getUpdateParamsByContainer(container);
  }

  /**
   * 获取光标focus处的cell
   * @param path modelPath
   * @returns
   */
  // TODO xzq 是否应该在selection中
  getFocusCellByPath(path: Path): Cell {
    return Cell.getFocusCellByPath(this, path);
  }

  /**
   * 添加有序无序列表
   * @param isOrder 有序无序
   * @returns 添加成功
   */
  @undoStackRecord(commands.add_list)
  addList(isOrder: boolean, paragraphs?: Paragraph[]) {
    return EditorHelper.addList(this, isOrder, paragraphs);
  }

  /**
   * 有序列表序号重新从1开始
   */
  restartListIndex() {
    return EditorHelper.reStartListIndex(this);
  }

  // @undoStackRecord(commands.add_list)
  changeListNumStyle(numStyle: "number" | "chinese") {
    return EditorHelper.changeListNumStyle(this, numStyle);
  }

  /**
   * 普通文档中tab功能：插入宽度和四个空格相当的字符
   * list行中的enter功能：插入一个换行字符
   * @param focus_row 光标所在行
   * @param para_path 段落路径
   * @param text 需要插入的文字
   */
  // TODO xzq 内部使用？
  tabAddFourSpace(focus_row: Row, para_path: number[], text: string) {
    return EditorHelper.addFourSpaces(this, focus_row, para_path, text);
  }

  /**
   * 键盘tab事件
   * 1、普通文档中，按下tab键相当于插入四个空格，不过删除的时候是一次性删除
   * 2、list列表文档
   *    ·在段落首位的时候设置该段落的级别加1
   *    ·在段落中间的时候，相当于普通的tab键
   * 3、在表格中类似上述两种情况
   */
  @undoStackRecord(commands.input)
  tabDown() {
    return EditorHelper.tabDown(this);
  }

  /**
   * 键盘输入回车键
   * 1、普通文档中正常按照换行来处理
   * 2、list类型
   *    ·如果光标所在行没有任何元素，则该行级别减一
   *    ·如果该行有元素，则生成同级list
   *    ·光标不在当前段落末尾的话，将光标后的内容生成同级
   */
  @undoStackRecord(commands.enter_linebreak)
  enterDown() {
    return EditorHelper.enterDown(this);
  }

  /**
   *改变对齐方式的方法
   * @param direction//方向
   */
  // TODO xzq
  @undoStackRecord(commands.uncertainty)
  changeContentAlign(direction: alignType) {
    return EditorHelper.changeContentAlign({ editor: this, direction });
  }

  /**
   * 修改段落内容对齐方式
   * @param direction 方向"left" | "center" | "right" | "dispersed" | "docuAlign"
   * @returns 修改成功否
   */
  // @undoStackRecord(commands.uncertainty)
  @resetSelectionFocus() // 修复字符对齐段落自上而下选区设置左对齐报错问题
  changeAlign(direction: alignType) {
    return EditorHelper.changeAlign(this, direction);
  }

  /**
   * 设置段落的分散对齐
   * @returns 分散对齐
   */
  @undoStackRecord(commands.uncertainty)
  setParaDispersed() {
    return Paragraph.setDispersedAlign(this);
  }

  /**
   * 修改行倍距
   * @param row_ratio 行倍距
   */
  @undoStackRecord(commands.uncertainty)
  changeRowRatio(
    row_ratio: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    return Paragraph.changeRowRatio(this, row_ratio, paragraphs, setType);
  }

  /**
   * 修改段前距 段落距离上一个段落之间的距离
   * @param before_paragraph_spaces 段前距
   */
  @undoStackRecord(commands.uncertainty)
  changeParaBeforSpacing(
    before_paragraph_spaces: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    return Paragraph.changeParagraphBeforeSpace(
      this,
      before_paragraph_spaces,
      paragraphs,
      setType
    );
  }

  /**
   * 首行缩进
   * @param indentation 缩进量
   * @param paragraphs 需要设置的段落
   * @param setType 设置类型 是固定设置指定值还是在原基础上增加或减少
   */
  @undoStackRecord(commands.uncertainty)
  firstRowIndentation(
    indentation: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    return Paragraph.setFirstRowIndent(this, indentation, paragraphs, setType);
  }

  /**
   * 指定或者选中段落格式重排 - 传入的所有段落首行缩进两个字符，并且删除这些段落中间的空行
   * @param paragraph 指定的段落集合
   * @param textIndent 是否开启首行缩进
   * @param clearSpace 是否清除段落中的空格
   */
  @undoStackRecord(commands.uncertainty)
  reformatParagraph(
    paragraph: Paragraph[] = [],
    textIndent: boolean = true,
    clearSpace: boolean = true
  ) {
    return Paragraph.reFormat(this, paragraph, textIndent, clearSpace);
  }

  /**
   * 刷新文档
   * @param isForce 是否强刷新
   */
  @monitorPerformance()
  @resetSelectionFocus(true)
  refreshDocument(isForce: boolean = false) {
    return EditorHelper.refreshDocument({ editor: this, isForce });
  }

  // /**
  // * 更新页眉页脚信息
  // */
  // updateHeaderFooterInfo() {
  //   return EditorHelper.updateHeaderFooterInfo(this);
  // }

  /**
   * 更新光标
   */
  @correctCaret()
  updateCaret() {
    return Caret.update(this);
  }
  // TODO xzq 内部使用？
  // 把本来在 update 里边的方法抽出来
  updateFormatParagraph() {
    return EditorHelper.updateFormatParagraph({ editor: this });
  }

  /**
   * @param cell_index 在当前Cell中的下标 表格里边单元格中的row的cell_index就是对应在表格中单元格里边的下标
   * @param page_number 在第几页
   * @param page_index 该Row或者表格在当前页中的下标
   */
  @monitorPerformance()
  update(
    cell_index: number = 0,
    page_number: number = 1,
    page_index: number = 0
  ) {
    // EditorHelper.update(this, cell_index, page_number, page_index);
    if (LocalTest.totalTime) {
      console.log("片段逻辑执行总耗时>>>", LocalTest.totalTime);
      LocalTest.totalTime = 0;
      setTimeout(() => {
        console.table(this.monitor.result);
      }, 100);
    }
    return this.event.emit("update", this, cell_index, page_number, page_index);
  }

  /**
   * 设置光标的位置，同时更新contextState
   * @param view_path
   * @param containers
   * @param x
   * @param y
   */
  // TODO xzq 内部使用？
  updateCaretByViewPath(
    view_path: number[],
    containers: (
      | Page
      | Cell
      | Table
      | Row
      | Character
      | Image
      | Widget
      | Box
    )[] = this.pages,
    x: number = 0,
    y: number = 0
  ) {
    return Caret.updateByViewPath(this, view_path, containers, x, y);
  }

  /**
   * 设置放大缩小的方法
   * @param val 放大缩小倍数
   * @returns
   */
  setViewScale(val: number = 1) {
    return EditorHelper.setViewScale(this, val);
  }

  /**
   * 页面放大至最大值
   */
  setMaxViewScale() {
    const max_val =
      this.init_canvas.width /
      this.config.devicePixelRatio /
      this.page_size.width;
    this.setViewScale(max_val);
  }

  /**
   *  canvas内外xy坐标数值转换,得到的Y值不包含scroll_top
   * @param param_x
   * @param param_y
   * @param type //1.外部转内部，距离canvas左上 2.内部转外部，距离canvas左上  3.外部转内部，距离屏幕左上  4.内部转外部，距离屏幕左上
   * @returns
   */
  // TODO xzq 内部使用？
  getNeedXYbyXY(param_x: number, param_y: number, type: number = 1) {
    return EditorHelper.getNeedXYbyXY({ editor: this, param_x, param_y, type });
  }

  /**
   * 通过编辑器获取的绝对定位计算canvas内实际定位
   * @param absolute_x
   * @param absolute_y
   */
  getViewPositionByAbsolutePosition(absolute_x: number, absolute_y: number) {
    return EditorHelper.getViewPositionByAbsolutePosition({ editor: this, absolute_x, absolute_y });
  }

  /**
   * 通过canvas内实际定位获取相对于编辑器的绝对定位
   * @param view_x
   * @param view_y
   * @returns
   */
  // TODO xzq 内部使用？
  getAbsolutePositionByViewPosition(view_x: number, view_y: number) {
    return EditorHelper.getAbsolutePositionByViewPosition({ editor: this, view_x, view_y });
  }

  /**
   * 下载字体文件
   * @returns
   */
  // TODO xzq 内部使用？
  downloadFontFace(isRareCharacter: boolean = false) {
    return EditorHelper.downloadFontFile(this, isRareCharacter);
  }

  /**
   * 实现数据另存为
   * @param filename
   * @param text
   */
  // TODO xzq 打开文件是否应移至common
  download(filename: string, text: string | object) {
    if (!text) {
      text = this.getRawData();
    }
    if (typeof text === "object") {
      text = JSON.stringify(text);
    }
    return EditorHelper.download(filename, text);
  }

  /**
   * C++测试数据导出
   */
  // TODO xzq 是否应直接使用download
  printCpp() {
    return EditorHelper.printCpp({ editor: this });
  }
  // TODO xzq ???
  getCtx() {
    return Renderer.get();
  }

  /**
   * 绘制内容
   */
  @monitorPerformance()
  render() {
    Renderer.render(this);
  }

  /**
   * 绘制选区与光标
   */
  // TODO xzq 内部使用
  renderSelection() {
    return XSelection.draw(this);
  }
  // TODO xzq 内部使用
  compareAnchorAndFocusIsBig(anchor: Path, focus: Path) {
    return EditorHelper.compareAnchorAndFocusIsBig(anchor, focus)
  }
  // 获取鼠标点击处距离页面顶部的距离
  getSpacingPageTopByY(y: number) {
    return EditorHelper.getSpacingPageTopByY(y, this);
  }
  // TODO xzq 内部使用
  getPageByRealY(y: number) {
    return EditorHelper.getPageByRealY(y, this);
  }

  printCurrentPage() {
    return Print.printCurrentPage(this);
  }

  /**
   * 开启关闭水印模式
   */
  waterMarkModel(open: boolean) {
    return WaterMark.toggleMode(this, open);
  }

  @undoStackRecord(commands.watermark)
  insertWarterMarkBy(
    parameter:
      {
        field?: XField,
        fieldId?: string,
        offsetX?: number,
        offsetY?: number,
        imageSrc: string,
        width?: number,
        heighit?: number,
        fields?: XField[]
      }
  ) {
    return WaterMark.insertWarterMarkBy({ ...parameter, editor: this })
  }

  /**
   * 开启关闭图形模式
   */
  shapeMode(open: boolean) {
    return Shape.changeShapeMode(this, open);
  }

  /**
 * 插入倾斜水印
 * @returns
 *  params:{
 *      direction:"left" ||"right" || "horizontal",        //水印方向
 *      module : ["pageWrite", "printView", "printPaper"], //水印控制模块
 *      font: {                                            //水印字体
              fontSize: 18,
              fontFamily: "华文彩云",
              opacity: 0.5,
              color: "red",
    }
 * }
 */
  insertItalicMark(text: string, params: any) {
    return WaterMark.insertItalicMark(text, params, this);
  }

  /**
   * 清除倾斜水印
   */
  clearItalicMark() {
    return WaterMark.clearItalicMark(this);
  }

  /**
 * 插入水印图片
 * @param src
 * @param mode
 * @param params={
 *    name: "aa",
      src: src,
      mode: "single",
      padding: true,
      level:3,
      location: [0, 0],
      width: 400,
      height: 300
    }
 * @returns
 */
  @undoStackRecord(commands.watermark)
  insertWaterMarkImage(src: string, params?: any) {
    setFocusImage(null);
    return WaterMark.insertImage(src, this, params);
  }

  // TODO xzq ？？？
  @undoStackRecord(commands.handle_shapes)
  shapeDeal(params: any, type: shapeType, x: number, y: number) {
    return Shape.shapeDeal(this, params, type, x, y);
  }

  /**
   * 是否插入水印文字模式
   */
  // TODO xzq 内部使用
  insertWaterMarkTextMode(openMarkText: boolean) {
    return WaterMark.openInsertTextMode(openMarkText, this);
  }

  // 改变水印模式
  changeMarkMode(mode: string) {
    return WaterMark.changeMarkMode(mode, this);
  }
  // TODO xzq 内部使用
  @undoStackRecord(commands.watermark)
  putMarkInputInCanvas() {
    return WaterMark.putMarkInputInCanvas(this);
  }
  // TODO xzq 内部使用
  isInMark(x: number, y: number) {
    return WaterMark.isInMark(x, y, this);
  }

  /**
   * 插入水印文字
   * @param src
   * @param mode
   * @returns
   */
  @undoStackRecord(commands.watermark)
  insertWaterMarkText(text: string) {
    setFocusImage(null);
    return WaterMark.insertText(text, this);
  }

  /**
   * 修改水印文字样式
   * @param style 文字样式
   * @returns
   */
  // TODO xzq 内部使用
  changeMarkFontStyle(style: Partial<FontStyle>) {
    return WaterMark.changeFontStyle(this, style);
  }

  // 开启/关闭护眼模式
  eyeProtectionMode(open: boolean = true, color: string = "rgb(199,237,204)") {
    return EditorHelper.toggleEyeProtectionMode(this, open, color);
  }
  // TODO xzq 内部使用
  // 是否是移动端
  isMobileTerminal() {
    return EditorHelper.isMobileTerminal({ editor: this });
  }
  // TODO xzq 内部使用
  isIOS() {
    const ua = navigator.userAgent;
    return (/macintosh|mac os x/i.test(ua) && window.screen.height > window.screen.width && !ua.match(/(iPhone\sOS)\s([\d_]+)/)) || ua.match(/(iPad).*OS\s([\d_]+)/);
  }

  /**
   * 销毁示例及其副作用
   */
  destroy() {
    return clearTimeout(this.internal.cursorFlashingTimer);
  }

  /**
   *  监听横向滚动条滚动距离赋值，拖动滚动条时改变滚动条位置的方法
   * @param x 横向滚动条滚动距离
   */
  setScrollX(x: number) {
    this.scrollX = x;
    this.render();
  }

  /**
   * 点击滚动条
   * @param x
   * @param y
   */
  // TODO xzq 内部使用
  clickScrollBar(x: number, y: number) {
    if (this.isMobileTerminal()) return
    return EditorHelper.clickScrollBar(this, x, y);
  }

  /**
   * 按页滚动
   * @param type 滚动方向
   * @param complete_height 所有页面和页面间距加起来的高度
   * @param canvas_height  canvas高度
   */
  scrollByPage(
    type: string = "top",
    complete_height: number,
    canvas_height: number
  ) {
    return EditorHelper.scrollByPage(
      this,
      type,
      complete_height,
      canvas_height
    );
  }

  // 根据当前选区 存储多选区
  saveMultipleSelectionByCurrent() {
    return EditorHelper.saveMultipleSelectionByCurrent({ editor: this });
  }

  /**
   * 绘制滚动条
   */
  // TODO xzq 内部使用
  drawScrollBar() {
    return EditorHelper.drawScrollBar(this);
  }
  // TODO xzq 内部使用
  drawCommentScrollBar(page?: Page) {
    return EditorHelper.drawCommentScrollBar(this, page);
  }

  /**
   * 绘制选中的单元格
   */
  // TODO xzq 内部使用
  drawSelectionCells() {
    const { multipleSelected } = this.selection;
    if (multipleSelected.length) {
      for (let i = 0; i < multipleSelected.length; i++) {
        const current = multipleSelected[i];
        current.selectedCellPath.length > 0 && EditorHelper.drawSelectionCells(this, current.selectedCellPath);
      }
    }
    if (this.selection.selected_cells_path.length) {
      return EditorHelper.drawSelectionCells(this);
    }
  }

  /**
   * 绘制选中的文本行区域
   */
  // TODO xzq 内部使用
  drawSelectionAreas() {
    const { multipleSelected } = this.selection;
    if (multipleSelected.length) {
      // 多选区的时候要绘制多个选区
      for (let i = 0; i < multipleSelected.length; i++) {
        const current = multipleSelected[i];
        current.selectedAreas.length > 0 && EditorHelper.drawSelectionAreas(this, current.selectedAreas);
      }
    }
    return EditorHelper.drawSelectionAreas(this);
  }
  /**
   * 绘制移动端选区小水滴
   */
  // TODO xzq 内部使用
  drawSelectionMobileEdit() {
    return EditorHelper.drawSelectionMobileEdit(this);
  }

  /**
   * 根据点位，返回path下最深层的row,view_path及点击位置相对坐标
   * @param x 光标距离canvas左上角的横坐标位置
   * @param y 光标距离canvas左上角的纵坐标位置
   * @param containers
   * @param path
   */
  getContainerInfoByPoint(
    x: number,
    y: number,
    containers: (Page | Table | Cell | Row | FloatModel)[] = this.pages,
    path: Path = []
  ):
    | {
      row: Row;
      element: Character | Image | Widget | Line | Box | Button | null;
      view_path: Path;
      x: number;
      y: number;
      fractionObj?: any
    }
    | undefined {
    if (this.editFloatModelMode && containers[0] === this.pages[0]) {
      containers = this.currentFloatModel?.children || [];
    }
    return EditorHelper.getContainerInfoByPoint(this, x, y, containers, path);
  }
  // TODO xzq 内部使用
  getElementByModelPath(
    path: Path = [],
    editor: Editor,
    containers: any[] = editor.current_cell.children,
    info: any = {}
  ): {
    element?: Character | Image | Widget | Line | Box | Button | null;
    row?: Row;
    cell?: Cell;
    table?: Table;
    page?: Page
  } {
    return EditorHelper.getElementByModelPath(path, editor, containers, info);
  }
  /**
   * 页眉页脚点击后处理
   * @param x
   * @param y
   */
  // TODO xzq 内部使用
  judgeClickHeaderFooter(x: number, y: number): any {
    return EditorHelper.judgeClickHeaderFooter(this, x, y);
  }

  /**
   * 获取需要存储内容原始数据及选区信息
   */
  // TODO xzq 内部使用
  @monitorPerformance()
  getContentState(is_first_undo: boolean = false) {
    return EditorHelper.getContentState(this, is_first_undo);
  }

  /**
   * 此方法可从历史堆栈中读取数据，并将数据转换为编辑器的model_data,并设置新的选区
   * @param contentState 从撤销堆栈或重做堆栈中取出的数据
   * @param optionType redo重做、undo撤销
   */
  // TODO xzq 内部使用
  @monitorPerformance()
  setContentState(contentState: any, optionType: "redo" | "undo" = "undo") {
    return EditorHelper.setContentState(this, contentState, optionType);
  }

  /**
   * 获取选中部分内容的原始数据信息
   * @param containHf 是否包含页眉页脚数据
   */
  getRawDataBySelection(containHf: boolean = false) {
    return XSelection.getRawData(this, containHf);
  }

  /**
   * 获取当前编辑器的原始数据信息
   */
  @monitorPerformance()
  getRawData() {
    return EditorHelper.getRawData(this);
  }

  getDescription(){
    return this.event.emit("getDescription", this);
  }

  // TODO 该方法跟 replace 方法成对使用，这两个方法感觉应该只获取单个元素的 rawData 数据，现在 getRawData 存了一些不该存的
  // 替换的时候也不应该使用插入模板，而是根据 rawData 转换成元素，直接替换掉原数据就可以，现在还有删除，而且还得设置选区...
  @monitorPerformance()
  getRawDatasByContainers(...containers: ReplaceContainerType[]) {
    return containers.map((container) => container.getRawData());
  }

  @monitorPerformance()
  replaceContainerByRawData(map: Map<ReplaceContainerType, any>) {
    return EditorHelper.replaceContainerByRawData(map);
  }

  /** ********** 分组相关 ↓ ******************* */

  /**
   * 分组内容转完整的原始数据内容
   * @param group
   */
  @monitorPerformance()
  getGroupRawData(group: Group) {
    return Group.getRawData(this, group);
  }

  getGroupByParaPth(path: Path): Group | undefined {
    return this.selection.getGroupByParaPth(path);
  }

  /** ********** 分组相关 ↑ ******************* */

  /**
   * 设置编辑器视图模式
   * @param view_mode 视图模式
   */
  @monitorPerformance()
  setViewMode(view_mode: ViewMode) {
    return EditorHelper.setViewMode(this, view_mode);
  }

  /**
   * 字符统计
   */
  @monitorPerformance()
  wordStatistics() {
    return EditorHelper.wordStatistics(this);
  }

  /**
   * 根据原始数据字符串插入模板数据
   * @param rawData 原始数据字符串或对象
   * @param replaceHeader 是否替换页眉内容
   * @param replaceFooter 是否替换页脚内容
   */
  @monitorPerformance()
  @undoStackRecord(commands.uncertainty)
  insertTemplateData(
    rawData: any,
    replaceHeader: boolean = false,
    replaceFooter: boolean = false,
    configItemUsedInRawData: any = [],
    option: any = {}
  ) {
    return EditorHelper.insertTemplateData(
      this,
      rawData,
      replaceHeader,
      replaceFooter,
      configItemUsedInRawData,
      option
    );
  }
  @undoStackRecord(commands.uncertainty)
  makeHistoryStackAble(fn: Function) {
    fn();
    return true;
  }

  @monitorPerformance()
  @undoStackRecord(commands.uncertainty)
  insertHeaderOrFooterTemplateData(rawData: any) {
    return EditorHelper.insertHeaderOrFooterTemplateData(this, rawData);
  }

  /**
   * 都昌xml转原始数据测试
   * @param xml
   */
  @monitorPerformance()
  dcXmlDataTrans(xml: any) {
    // console.log(dcXmlHandle, "不得已打印一下，因为这个文件开头必须引用它");
    // return EditorHelper.dcXmlDataTrans(this, xml);
    return this.event.emit("dcXml2RawData", xml);
  }

  /**
   * html数据转原始数据
   * @param html
   */
  @monitorPerformance()
  htmlDataTrans(html: any) {
    return this.event.emit("html2RawData", html);
  }

  /**
   *验证当前位置是否允许输入
   * @return true 允许输入  false 不允许输入
   */
  permitOperationValidation(): boolean {
    return EditorHelper.permitOperationValidation(this);
  }

  /**
   * 清空文档内容
   * @param isForce true强清空  true将整篇数据清空,false 只清空正文内容
   */
  clearDocument(isForce: boolean = false) {
    return EditorHelper.clearDocument(this, isForce);
  }

  /**
   * 删除选区，可控是否记录到撤销堆栈中
   * @param isRecord true记录，false不记录
   */
  @undoStackRecord(commands.uncertainty)
  delSectionRecordStack(isRecord: boolean = true): boolean {
    return XSelection.deleteAndControlRecord(this, isRecord);
  }
  // TODO xzq 内部使用
  drawMarkInput(x: number, y: number, mark: WaterMark | null = null) {
    return MarkInput.displayMarkInput(x, y, this, mark);
  }
  /**
   * 更新自定义水印图片
   * param:{
   *  name:"",
   *  src:"",
   *  mode:"repeat/single"
   *  width:100,
   *  height:100,
   * }
   */
  updateCustomMarkImage(param: any) {
    return WaterMark.updateCustomMarkImage(this, param)
  }
  // TODO xzq 内部使用
  updateMarkParameter(
    text: string,
    mark: WaterMark,
    x: number = 0,
    y: number = 0
  ) {
    return WaterMark.updateMarkParameter(text, mark, this, x, y);
  }

  /**
   * 输入过程中与输入结束时调用
   * @param ime_start
   * @param text
   * @param isEnd 是否是输入结束
   */
  @undoStackRecord(commands.uncertainty)
  onCompositionInputHelper(
    ime_start: Path | null,
    text: string,
    isEnd: boolean
  ) {
    return InputAdapter.onCompositionInputHelper(this, ime_start, text, isEnd);
  }

  /**
   * 页眉页脚水平线控制
   * @param header_show 页眉水平线控制
   * @param footer_show 页脚水平线控制
   */
  headerFooterHorizontal(header_show: boolean, footer_show: boolean) {
    return EditorHelper.headerFooterHorizontal({ editor: this, header_show, footer_show });
  }

  /**
   * 封装保存原始数据时应存的配置信息与文档信息
   */
  packSaveDocumentInfo(obj: any = {}) {
    return EditorHelper.saveInfo(this, obj);
  }


  /**
   * 删除光标所在行或者指定文本域所在行
   * @param field 指定文本域
   */
  deleteRowWithCaret(field?: XField) {
    return Row.deleteWithCaretOrField(this, field);
  }

  /**
   * 敏感词检测
   * @param words 需要检测的敏感词字符串数组
   * @param style 检测出来的敏感词要设置的样式，仅支持字体颜色与背景色设置
   */
  checkSensitiveWord(
    words: string[],
    style?: { bgColor?: string; color?: string }
  ) {
    return EditorHelper.checkSensitiveWord(this, words, style);
  }

  /**
   * 清空敏感词设置的样式
   */
  clearSensitiveWordStyle() {
    return EditorHelper.clearSensitiveWordStyle(this);
  }

  /**
   * 模型数据转html
   */
  modelData2Html() {
    // return DomTrans.modelData2Html(this);
    return this.event.emit("modelData2Html");
  }

  /**
   * 指定表格行转换为原始数据(返回完整的原始数据方便使用)
   * @param table 需要处理的表格
   * @param rowIndexArray 需要处理的行下标数组
   */
  assignTableRow2RawData(table: Table, rowIndexArray: number[]) {
    return Table.assignRow2RawData(table, rowIndexArray);
  }

  /**
   * 将每页数据转换成json数据
   * @param extraInfo
   * @param selectedPages
   */
  assemblePageJson(extraInfo?: any, selectedPages?: any) {
    return this.event.emit("assemblePageJson", extraInfo, selectedPages);
  }

  // 匿名化处理文本域
  setFieldsAsterisk(names: string[], rule?: any) {
    return EditorHelper.setFieldsAsterisk({ editor: this, names, rule });
  }

  /**
   * 用于判断焦点元素是否可编辑
   */
  judgeFocusEleEditable() {
    return EditorHelper.judgeFocusEleEditable(this);
  }
  // TODO xzq ？？？
  confirmExistence(groupId: string, path: Path) {
    return PathUtils.confirmExistence(groupId, path, this);
  }

  // 清空版本信息
  clearVersionInfo() {
    return EditorHelper.clearVersionInfo({ editor: this });
  }

  // 对所有文本域进行排序，按照startParaPath
  sortAllFields() {
    return EditorHelper.sortAllFields({ editor: this });
  }

  /**
   * 对比两个rawData
   */
  compareRawData(rawData1: any, rawData2: any, ignoreProps?: string[]) {
    return EditorHelper.compareRawData(rawData1, rawData2, ignoreProps);
  }

  // 解锁所有
  unlockEverything() {
    return EditorHelper.unlockEverything({ editor: this });
  }

  clearHeaderOrFooter(type: string) {
    return EditorHelper.clearHeaderOrFooter(this, type);
  }

  // 获取所有图片、
  // TODO tang 放到editorhelper里面
  getAllImages(assign_cell: Cell = this.current_cell) {
    return EditorHelper.getAllImages(this, assign_cell);
  }

  // TODO xzq 内部使用？
  // 删除para中的元素
  removeParagraphElement(
    paragraph: Paragraph,
    elements: Widget[] | Image[] | Character[] | Line[]
  ) {
    return EditorHelper.removeParagraphElement(paragraph, elements);
  }

  // TODO xzq 是否与已有接口重复
  operableOrNot(types: string[]) {
    return EditorHelper.operableOrNot(this, types);
  }

  setAdminMode(flag: boolean = true) {
    this.adminMode = flag;
  }
  // TODO xzq 内部使用
  @undoStackRecord(commands.uncertainty)
  splitCellNotHaveBeenMerged(rowNum: number, colNum: number) {
    return EditorHelper.splitCellNotHaveBeenMerged(this, rowNum, colNum);
  }

  userLogin(userInfo: any) {
    return EditorHelper.userLogin({ editor: this, userInfo });
  }

  changeStartPageNumber(pageNumber: number) {
    this.config.startPageNumber = pageNumber;
  }

  // 判断编辑器中的图片是否全部加载完毕
  judgeImageAllLoad() {
    return EditorHelper.judgeImageAllLoad({ editor: this });
  }
  //更新创建字段的值
  updateCustomFields(dataList: any) {
    return this.internal.autoFill.updateCustomFields(dataList, this)
  }

  @undoStackRecord(commands.uncertainty)
  fillContentByJson(
    rawData: any,
    jsonData: any, // 这个 jsonData 就是 design 上传进来的 oriDataSet
    jsonType: string,
    isClear: boolean
  ) {
    return EditorHelper.fillContentByJson({ rawData, jsonData, jsonType, isClear, editor: this });
  }

  handleDataSet2FillJson(dataSet: any) {
    const autoFill = new AutoFill();
    return autoFill.handleDataSet2FillJson(dataSet);
  }
  
  // TODO xzq params?
  setHighlightOrReplaceTextByParams(params: any) {
    return EditorHelper.setHighlightOrReplaceTextByParams(params, this);
  }
  // TODO xzq ?
  replaceParagraphTextByIndex(para: Paragraph, targetText: string, replaceText: string, index: number) {
    return Search.replaceParagraphTextByIndex(para, targetText, replaceText, index, this)
  }

  getFieldContentByName(fieldName: string[], simpleRes: boolean = false, checkEmpty: boolean = false) {
    return EditorHelper.getFieldContentByName(this, fieldName, simpleRes, checkEmpty)
  }

  @undoStackRecord(commands.uncertainty)
  transFields2Content(fields?: XField[]) {
    return XField.transFields2Content(this, fields)
  }

  handleFieldCharacterSize(field:XField,options?:any){
    return XField.autoFieldCharacterSize(this,field,options)
  }

  @undoStackRecord(commands.uncertainty)
  @resetSelectionFocus()
  multiTableSplicing(){
    return Table.multiTableSplicing(this)
  }

  transformDOM2RawData(node: Node) {
    return this.event.emit("transformDOM2RawData", node);
  }

  transformHTML2RawData(html: string) {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(html, "text/html"); 
    return this.transformDOM2RawData(xmlDoc);
  }

  // 将特定字符串转为文本域
  // 传参应该是个对象数组 
  // {
  //   text: "", // 将要转为文本域的字符串
  //   xxx: "", // 剩下的都是文本域的各种属性
  //   xxx: "", // 剩下的都是文本域的各种属性
  // }
  // 有个问题 我不知道先循环到哪个 text 我不能每个 text 都循环一遍吧 所以我需要找到那个算法 AC 自动机
  // 要先研究明白这个算法再说
  transformTextToField(arr: any[]) {
    // 第一步：使用AC自动机算法找到所有匹配的文本及其位置，并返回带有modelPath的结果
    const matchResults = this.findTextMatchesWithModelPath(arr);

    // 如果找到匹配结果，则执行替换操作
    if (matchResults && matchResults.length > 0) {
      this.replaceTextWithFields(matchResults);
    }

    return matchResults;
  }

  /**
   * 获取所有段落，包括表格内的段落
   * @returns 所有段落的数组，包含段落对象和其在文档中的位置信息
   */
  private getAllParagraphsIncludingTables(): Array<{paragraph: any, location: any}> {
    const allParagraphs: Array<{paragraph: any, location: any}> = [];

    // 遍历 root_cell.paragraph 中的所有容器
    for (let containerIndex = 0; containerIndex < this.root_cell.paragraph.length; containerIndex++) {
      const container = this.root_cell.paragraph[containerIndex];

      if (isParagraph(container)) {
        // 普通段落
        allParagraphs.push({
          paragraph: container,
          location: {
            type: 'normal',
            containerIndex: containerIndex
          }
        });
      } else if (isTable(container)) {
        // 表格，需要遍历其中的所有单元格和段落
        const table = container;
        for (let cellIndex = 0; cellIndex < table.children.length; cellIndex++) {
          const cell = table.children[cellIndex];
          if (cell.paragraph && Array.isArray(cell.paragraph)) {
            for (let paraIndex = 0; paraIndex < cell.paragraph.length; paraIndex++) {
              const paragraph = cell.paragraph[paraIndex];
              if (isParagraph(paragraph)) {
                allParagraphs.push({
                  paragraph: paragraph,
                  location: {
                    type: 'table',
                    containerIndex: containerIndex, // 表格在root_cell.paragraph中的索引
                    cellIndex: cellIndex,           // 单元格在表格中的索引
                    paraIndex: paraIndex            // 段落在单元格中的索引
                  }
                });
              }
            }
          }
        }
      }
    }
    return allParagraphs;
  }

  /**
   * 使用AC自动机算法查找文本匹配并返回对应的modelPath
   * @param textFieldConfigs 文本域配置数组，每个对象包含text属性和其他文本域属性
   * @returns 返回包含原始配置和modelPath信息的数组
   */
  private findTextMatchesWithModelPath(textFieldConfigs: any[]): any[] {
    try {
      // 提取所有要搜索的文本
      const keywords = textFieldConfigs.map(config => config.text);

      // 创建增强版AC自动机，能够返回匹配位置信息
      const ac = new EnhancedAhoCorasick(keywords);

      // 获取所有段落，包括表格内的段落
      const allParagraphsWithLocation = this.getAllParagraphsIncludingTables();

      if (!allParagraphsWithLocation || allParagraphsWithLocation.length === 0) {
        console.warn('没有找到任何段落');
        return textFieldConfigs.map(config => ({ ...config, paraPaths: [] }));
      }

      // 存储所有匹配结果
      const allMatches: any[] = [];

      // 遍历每个段落进行搜索
      for (let i = 0; i < allParagraphsWithLocation.length; i++) {
        const { paragraph, location } = allParagraphsWithLocation[i];

        // 使用AC自动机搜索这个段落
        const matches = ac.searchWithPosition(paragraph, i);

        // 为每个匹配添加位置信息
        matches.forEach((match: any) => {
          match.location = location;
        });

        allMatches.push(...matches);
      }

      // 按照在文档中出现的顺序对所有匹配进行排序
      allMatches.sort((a: any, b: any) => {
        // 首先按段落索引排序
        if (a.paragraphIndex !== b.paragraphIndex) {
          return a.paragraphIndex - b.paragraphIndex;
        }
        // 如果在同一段落，按字符索引排序
        return a.startCharIndex - b.startCharIndex;
      });

      // 将匹配结果转换为按出现顺序排列的配置对象数组
      // 每个匹配对应一个独立的配置对象      
      const result: any[] = [];
      
      for (const match of allMatches) {
        // 找到对应的原始配置
        const originalConfig = textFieldConfigs.find(config => config.text === match.keyword);
        if (originalConfig) {
          // 为每个匹配创建一个独立的配置对象
          const paraPath = this.getParaPathFromMatch(match);
          if (paraPath.length > 0) {
            result.push({
              ...originalConfig, // 保留原始配置的所有属性
              paraPath: paraPath // 单个paraPath，不是数组
            });
          }
        }
      }
      return result;
    } catch (error) {
      console.error('findTextMatchesWithModelPath出错:', error);
      return textFieldConfigs.map(config => ({ ...config, matches: [] }));
    }
  }

  /**
   * 根据匹配结果替换文本为文本域
   * @param matchResults 匹配结果数组，每个元素包含paraPath和文本域配置
   */
  private replaceTextWithFields(matchResults: any[]): void {
    try {
      // 从后往前处理，避免位置偏移问题
      for (let i = matchResults.length - 1; i >= 0; i--) {
        const matchResult = matchResults[i];
        const { paraPath, text } = matchResult;

        if (!paraPath || !Array.isArray(paraPath) || paraPath.length === 0) {
          console.warn('无效的paraPath:', paraPath);
          continue;
        }

        try {
          // 第一步：根据paraPath设置选区
          const startModelPath = this.paraPath2ModelPath(paraPath);

          // 计算结束位置的paraPath
          const endParaPath = [...paraPath];
          endParaPath[endParaPath.length - 1] += text.length;

          // 将endParaPath转换为modelPath
          const endModelPath = this.paraPath2ModelPath(endParaPath);

          // 设置选区
          this.selection.setSelectionByPath(startModelPath, endModelPath, "model_path");

          // 第二步：使用现有的删除选区方法删除文本
          this.delete_backward();

          // 第三步：插入文本域
          const fieldConfig = {
            ...matchResult // 包含所有配置属性
          };

          // 删除不需要的属性
          delete fieldConfig.text;
          delete fieldConfig.paraPath;

          // 插入文本域
          const field = this.insertField(fieldConfig);

          if (field) {
            console.log(`成功替换文本 "${text}" 为文本域`);
          } else {
            console.warn(`替换文本 "${text}" 为文本域失败`);
          }

        } catch (error) {
          console.error(`处理匹配结果时出错:`, error, matchResult);
        }
      }
    } catch (error) {
      console.error('replaceTextWithFields出错:', error);
    }
  }

  /**
   * 根据匹配结果获取paraPath
   * @param match 匹配结果对象，包含location和字符索引信息
   * @returns paraPath数组
   */
  private getParaPathFromMatch(match: any): number[] {
    try {
      const { location, startCharIndex } = match;

      if (location.type === 'normal') {
        // 普通段落：[段落索引, 字符索引]
        return [location.containerIndex, startCharIndex];
      } else if (location.type === 'table') {
        // 表格内段落：[表格索引, 单元格索引, 段落索引, 字符索引]
        return [location.containerIndex, location.cellIndex, location.paraIndex, startCharIndex];
      } else {
        console.warn('未知的位置类型:', location.type);
        return [];
      }
    } catch (error) {
      console.error('getParaPathFromMatch出错:', error);
      return [];
    }
  }
}
